#!/usr/bin/env python3
# coding=utf-8
"""
Test script for enhanced logging features.
Run this to verify all enhancements are working correctly.
"""

import asyncio
import logging
import os
import platform
import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from custom_logger import (
    CorrelationContext, 
    CustomLogger, 
    debug_monitor, 
    debug_handler,
    UnifiedUnicodeHandler
)


def test_basic_logging():
    """Test basic logging functionality with Unicode handling."""
    print("🧪 Testing basic logging with Unicode handling...")
    
    log_file = "/tmp/test_enhanced_logging.log"
    custom_logger = CustomLogger("test_logger", log_file, use_formatter=True)
    logger = custom_logger.get_logger()
    
    # Test Unicode characters
    logger.info("✅ Basic logging test started")
    logger.warning("⚠️ This is a warning with Unicode")
    logger.error("❌ This is an error with emojis 🚀")
    logger.info("🎯 Testing various symbols: →←↑↓ ✓✗ €£¥")
    
    print("✅ Basic logging test completed")


def test_correlation_context():
    """Test correlation context functionality."""
    print("🧪 Testing correlation context...")
    
    log_file = "/tmp/test_correlation.log"
    custom_logger = CustomLogger("correlation_test", log_file, use_formatter=True)
    logger = custom_logger.get_logger()
    
    # Test synchronous correlation context
    with CorrelationContext(operation_name="sync_operation") as ctx:
        logger.info("Starting synchronous operation")
        logger.info("Step 1 of sync operation")
        logger.info("Step 2 of sync operation")
        logger.info("Synchronous operation completed")
    
    # Test without correlation context
    logger.info("Log without correlation context")
    
    print("✅ Correlation context test completed")


async def test_async_correlation():
    """Test async correlation context functionality."""
    print("🧪 Testing async correlation context...")
    
    log_file = "/tmp/test_async_correlation.log"
    custom_logger = CustomLogger("async_test", log_file, use_formatter=True)
    logger = custom_logger.get_logger()
    
    async def async_task(task_id):
        async with CorrelationContext(f"async_task_{task_id}") as ctx:
            logger.info(f"Starting async task {task_id}")
            await asyncio.sleep(0.1)
            logger.info(f"Processing in task {task_id}")
            await asyncio.sleep(0.1)
            logger.info(f"Async task {task_id} completed")
    
    # Run multiple async tasks concurrently
    tasks = [async_task(i) for i in range(3)]
    await asyncio.gather(*tasks)
    
    print("✅ Async correlation test completed")


def test_debug_monitor():
    """Test debug monitor functionality."""
    print("🧪 Testing debug monitor...")
    
    if not debug_monitor.enabled:
        print("⚠️ Debug monitor is not enabled. Set ENABLE_DEBUG_MONITOR=true to test.")
        return
    
    # Test manual check (works on all platforms)
    debug_monitor.manual_check()
    
    # Test platform-specific features
    if platform.system() == 'Windows':
        print("🪟 Windows detected - testing manual debugging methods")
        debug_monitor.trigger_stack_dump()
        if debug_handler:
            debug_handler.trigger_debug_dump()
            debug_handler.trigger_state_dump()
    else:
        print("🐧 Unix system detected - signal handlers available")
        print("   Use 'kill -USR1 <pid>' for stack traces")
        print("   Use 'kill -USR2 <pid>' for state dump")
    
    # Test queue activity logging
    debug_monitor.log_queue_activity("test_queue", "put", 50)
    debug_monitor.log_queue_activity("test_queue", "get", 49)
    debug_monitor.log_queue_activity("large_queue", "put", 150)  # Should trigger warning
    
    # Test HTTP request logging
    debug_monitor.log_http_request("GET", "/api/test", 200)
    debug_monitor.log_http_request("POST", "/api/data", 429)  # Rate limit
    
    # Test database operation logging
    debug_monitor.log_db_operation("SELECT", 2.5)
    debug_monitor.log_db_operation("INSERT", 6.0)  # Slow operation
    
    print("✅ Debug monitor test completed")


def test_unicode_handler():
    """Test the unified Unicode handler directly."""
    print("🧪 Testing unified Unicode handler...")
    
    handler = UnifiedUnicodeHandler()
    
    # Create a mock log record
    class MockRecord:
        def __init__(self, msg, args=None):
            self.msg = msg
            self.args = args or ()
            self.levelname = "INFO"
    
    # Test various Unicode scenarios
    test_cases = [
        "✅ Success message",
        "❌ Failure with emoji 🚀",
        "Mixed symbols: →←↑↓ ✓✗ €£¥",
        "Normal ASCII message",
        "Unicode with args: %s %s",
    ]
    
    for i, test_msg in enumerate(test_cases):
        record = MockRecord(test_msg, ("arg1", "arg2") if "args" in test_msg else ())
        result = handler.filter(record)
        print(f"   Test {i+1}: {'✅' if result else '❌'} - {record.msg}")
    
    print("✅ Unicode handler test completed")


def test_performance():
    """Test performance of enhanced logging."""
    print("🧪 Testing logging performance...")

    log_file = "/tmp/test_performance.log"
    custom_logger = CustomLogger("perf_test", log_file, use_formatter=True)
    logger = custom_logger.get_logger()

    # Test performance with correlation context
    start_time = time.time()

    with CorrelationContext("performance_test") as ctx:
        for i in range(1000):
            logger.info(f"Performance test message {i} with Unicode ✅")

    end_time = time.time()
    duration = end_time - start_time

    print(f"   Logged 1000 messages in {duration:.3f} seconds")
    print(f"   Average: {(duration/1000)*1000:.3f} ms per message")

    print("✅ Performance test completed")


def test_database_logging_integration():
    """Test database logging integration (if available)."""
    print("🧪 Testing database logging integration...")

    try:
        from containers import ApplicationContainer, initialize_database_logging

        # Set up test environment
        os.environ.setdefault("ENABLE_DATABASE_LOGGING", "true")
        os.environ.setdefault("DB_LOG_BATCH_SIZE", "5")

        # Initialize container
        container = ApplicationContainer()
        container.wire(modules=[__name__])
        container.logger_container.init_resources()

        # Configure database logging
        db_handlers = initialize_database_logging(
            container=container,
            schema='public',
            enabled=True
        )

        if db_handlers:
            logger = container.logger_container.logger()

            with CorrelationContext("db_test") as ctx:
                logger.info("Testing database logging integration")
                logger.warning("This should be stored in database")

            print(f"   Created {len(db_handlers)} database handlers")
            print("✅ Database logging integration test completed")
        else:
            print("⚠️ No database handlers created - check configuration")

    except ImportError:
        print("⚠️ Database logging not available - containers module not found")
    except Exception as e:
        print(f"⚠️ Database logging test failed: {e}")
        print("   This is expected if database is not configured")


async def run_all_tests():
    """Run all tests."""
    print("🚀 Starting Enhanced Logging Tests")
    print("=" * 50)
    
    # Basic tests
    test_basic_logging()
    test_correlation_context()
    await test_async_correlation()
    test_unicode_handler()
    test_debug_monitor()
    test_performance()
    test_database_logging_integration()
    
    print("=" * 50)
    print("🎉 All tests completed!")
    print("\nFeatures tested:")
    print("- ✅ Unicode handling and emoji support")
    print("- ✅ Correlation ID tracking")
    print("- ✅ Async task correlation")
    print("- ✅ Cross-platform debugging")
    print("- ✅ Performance monitoring")
    print("- ✅ Enhanced formatting")
    print("- ✅ Database logging integration")
    
    print(f"\nPlatform: {platform.system()} {platform.release()}")
    print(f"Debug Monitor: {'Enabled' if debug_monitor.enabled else 'Disabled'}")
    
    # Show log files created
    log_files = [
        "/tmp/test_enhanced_logging.log",
        "/tmp/test_correlation.log", 
        "/tmp/test_async_correlation.log",
        "/tmp/test_performance.log"
    ]
    
    print("\nLog files created:")
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"  📄 {log_file} ({size} bytes)")


if __name__ == "__main__":
    # Set up environment for testing
    os.environ.setdefault("ENABLE_DEBUG_MONITOR", "true")
    os.environ.setdefault("DEBUG_MONITOR_INTERVAL", "10")
    
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
