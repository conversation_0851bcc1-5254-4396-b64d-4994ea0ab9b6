# coding=utf-8
import requests
from requests.auth import HTTPBasicAuth
import pandas as pd
from pykeepass import PyKeePass as pkp

import logging
import os
import json
import sys
from logging.handlers import TimedRotatingFileHandler
from urllib.parse import quote, urlparse
from sqlalchemy.orm import relationship, sessionmaker, object_mapper, reconstructor
from sqlalchemy import create_engine, DDL, event, engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import Column, ForeignKey, Integer, String, Boolean, Numeric, ARRAY, DECIMAL
from sqlalchemy.sql import select
from sqlalchemy.sql.sqltypes import BIGINT, Date, DateTime
from sqlalchemy.sql.schema import ForeignKeyConstraint, Index, MetaData, Table
from citext import CIText
from sqlalchemy_utils import LtreeType

import psycopg2
from dateutil.relativedelta import relativedelta
from datetime import datetime
import sqlalchemy


class RepresentableBase(object):
    """
    This class can be used by ``declarative_base``, to add an automatic
    ``__repr__`` method to *all* subclasses of ``Base``. This ``__repr__`` will
    represent values as::

        ClassName(pkey_1=value_1, pkey_2=value_2, ..., pkey_n=value_n)

    where ``pkey_1..pkey_n`` are the primary key columns of the mapped table
    with the corresponding values.
    """

    def __repr__(self):
        mapper = object_mapper(self)
        items = [(p.key, getattr(self, p.key))
                 for p in [
                     mapper.get_property_by_column(c) for c in mapper.primary_key]]
        return "{0}({1})".format(
            self.__class__.__name__,
            ', '.join(['{0}={1!r}'.format(*_) for _ in items]))


def make_http_request(request_method: str, **kwargs) -> requests.Response:
    if kwargs['payload'] is None:
        response = requests.request(
            request_method,
            url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
            headers=kwargs['headers'],
            auth=kwargs['auth']
        )
    else:
        response = requests.request(
            request_method,
            url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
            headers=kwargs['headers'],
            auth=kwargs['auth'],
            params=kwargs['payload']
        )
    return response


def make_http_request_data(**kwargs) -> requests.Response:
    response = requests.request(
        "POST",
        url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
        headers=kwargs['headers'],
        auth=kwargs['auth'],
        data=kwargs['data']
    )
    response.raise_for_status()
    return response


def get_logger(logger_name, log_file, use_formatter=False):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)  # better to have too much log than not enough
    logger.addHandler(get_console_handler(use_formatter))
    logger.addHandler(get_file_handler(log_file, use_formatter))
    # with this pattern, it's rarely necessary to propagate the error up to parent
    logger.propagate = False
    return logger


def get_console_handler(formatter=False):
    console_handler = logging.StreamHandler(sys.stdout)
    if formatter:
        formatter = logging.Formatter("%(asctime)s — %(name)s — %(levelname)s — %(message)s")
        console_handler.setFormatter(formatter)
    return console_handler


def get_file_handler(log_file, formatter=False):
    # source: https://stackoverflow.com/questions/3220284/how-to-customize-the-time-format-for-python-logging
    file_handler = TimedRotatingFileHandler(log_file, when='midnight')
    if formatter:
        formatter = logging.Formatter(
            # "%(asctime)s |:| %(name)s - %(levelname)s - %(message)s",
            '%(asctime)s |:| LEVEL: %(levelname)s |:| FILE PATH: %(pathname)s |:| FUNCTION/METHOD: %(funcName)s |:| LINE NO.: %(lineno)d |:| PROCESS ID: %(process)d |:| THREAD ID: %(thread)d |:| %(message)s',
            "%Y-%m-%d %H:%M:%S"
        )
        file_handler.setFormatter(formatter)
    return file_handler


def get_env_variables() -> dict:
    if os.name == "nt":
        filename = "../dags/data_pipeline/config.json"
    else:
        filename = "/opt/airflow/dags/data_pipeline/config.json"
    # Using context manager.
    with open(filename, "r") as fp:
        data = json.load(fp)
    keedb = f'KeePassDB_{os.name}'
    keepass = f'KeyPassKey_{os.name}'

    ref = pkp(filename=data[keedb], keyfile=data[keepass])
    entry = ref.find_entries(title=data['KpCoreCardJira'], first=True)
    data['baseurl'] = entry.url
    data['username'] = entry.username
    data['pwd'] = entry.password
    data['auth'] = HTTPBasicAuth(entry.username, entry.password)
    data['payload'] = None
    data['headers'] = {'Accept': data['Accept'], 'Content-Type': data['Content-Type']}

    # Get the cloudId
    response = requests.get(url=f'{data["baseurl"]}/_edge/tenant_info')
    for key, value in response.json().items():
        data[key] = value
    # Delete unwanted keys that are not going to be used later
    for key in [f'KeePassDB_{os.name}', f'KeyPassKey_{os.name}', 'KpCoreCardJira', 'username', 'pwd', 'Accept', 'Content-Type']:
        del data[key]

    return data

def handle_pyscopg2_exception(err):
    # get details about the exception
    err_type, err_obj, traceback = sys.exc_info()

    # get the line number when exception occurred
    line_num = traceback.tb_lineno

    print(f'{line_num} psycopg2 traceback: {traceback}, type: {err_type}')
    print(f'{line_num} psycopg2 traceback: {traceback}, type: {err_type}')
    print('exiting with Failure !!!')


def start_session():
    dbschema = 'public,PLAT'  # Searches left-to-right
    if os.name == "nt":
        filename = "../dags/data_pipeline/config.json"
    else:
        filename = "/opt/airflow/dags/data_pipeline/config.json"
    keedb = f'KeePassDB_{os.name}'
    keepass = f'KeyPassKey_{os.name}'

    with open(filename, "r") as fp:
        data = json.load(fp)
    ref = pkp(filename=data[keedb], keyfile=data[keepass])
    entry = ref.find_entries(title='lilly', first=True)
    user = entry.username
    pwd = entry.password

    if os.name == 'nt':
        conn_str = 'postgresql+psycopg2://{username}:{password}@localhost:5432/{database}'.format(
            username='postgres',
            password=quote(pwd),
            database='jira'
        )
    else:
        conn_str = 'postgresql+psycopg2://{username}:{password}@**************:5432/{database}'.format(
            username='postgres',
            password=quote(pwd),
            database='jira'
        )
    print(conn_str)

    # "postgresql+psycopg2://postgres:%s@localhost:5432/jira" % quote('ABC'),
    try:
        session = sessionmaker()
        engine_ = create_engine(conn_str, connect_args={'options': '-csearch_path={}'.format(dbschema)})
        session.configure(bind=engine_)
        Base.metadata.create_all(engine_)
        return session()
    except sqlalchemy.exc.OperationalError as e:
        handle_pyscopg2_exception(e)
        exit(1)
    except psycopg2.OperationalError as e:
        handle_pyscopg2_exception(e)
        exit(1)


def upsert(session, model, rows: pd.DataFrame, primary_key: str, no_update_cols: tuple = ()):
    table = model.__table__

    stmt = insert(table).values(rows.to_dict(orient='records'))
    update_cols = [c.name for c in table.c
                   if c not in list(table.primary_key.columns)
                   and c.name not in no_update_cols]

    on_conflict_stmt = stmt.on_conflict_do_update(
        index_elements=table.primary_key.columns,
        set_={k: getattr(stmt.excluded, k) for k in update_cols},
        index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
    )

    session.execute(on_conflict_stmt)


def wkdict2values(raw, issueKey):
    arr = {}
    for key, value in raw.items():
        if key == 'worklogs':
            wk_list = []
            for wk in value:
                wk_list.extend(
                    [
                        {
                            'author': wk["author"]["accountId"],
                            'updateauthor': wk["updateAuthor"]["accountId"],
                            'created': wk["created"],
                            'updated': wk["updated"],
                            'started': wk["started"],
                            'timeSpent': wk["timeSpent"],
                            'timeSpentSeconds': wk["timeSpentSeconds"],
                            'id': wk["id"],
                            'issueId': wk["issueId"],
                            'issueKey': issueKey
                        }
                    ]
                )
            arr['worklogs'] = wk_list
        else:
            arr[key] = value

    return arr


def dict2values(raw):
    arr = {}

    def extract(raw, arr):
        seqs = tuple, list, set, frozenset
        final = str, int
        arr['customfield_10018.key'] = arr['customfield_10018.id'] = None
        if isinstance(raw, dict):
            for key, value in raw.items():
                if key == "fields":
                    extract(value, arr)
                elif key in ['issuelinks', 'worklog']:
                    pass
                elif key in ["assignee", "reporter"]:
                    arr[key] = None if value is None else value['accountId']
                elif key in ["expand", "self"]:
                    pass
                elif key in ["issuetype"]:
                    arr[key] = value['name']
                    arr['isSubTask'] = value['subtask']
                elif key == "parent":
                    arr[key] = None if value is None else value['key']
                elif key in ["resolution", "status", "priority"]:
                    arr[key] = None if value is None else value['name']
                    if key == "status":
                        arr['statusCategory'] = value['statusCategory']['name']
                elif key in ["components", "fixVersions", "versions"]:
                    arr[key] = None if value is None else [i['name'] for i in value]
                elif key in ["aggregateprogress", "progress"]:
                    if value is not None:
                        arr[key + '_' + 'progress'] = value["progress"] / 3600
                        arr[key + '_' + 'total'] = value["total"] / 3600
                        if 'percent' in value:
                            arr[key + '_' + 'percent'] = value['percent'] / 100
                        else:
                            arr[key + '_' + 'percent'] = None
                elif key in ["customfield_10018"]:
                    try:
                        arr[key + "." + "key"] = value["data"]["key"]
                        # arr[key + "." + "id"] = value["data"]["id"]
                    except KeyError:
                        pass
                elif key == "customfield_10014":
                    arr[key] = None if value is None else value
                elif key in ['customfield_10001']:
                    if value is not None:
                        arr['Team'] = value['title']
                elif key in ['customfield_10020']:
                    if value is not None:
                        arr[key + '.' + 'name'] = [name["name"] for name in value]
                        arr[key + '.' + 'id'] = [name["id"] for name in value]
                    else:
                        arr[key + '.' + 'name'] = None
                        arr[key + '.' + 'id'] = None
                elif key in ['customfield_10146', 'customfield_10078']:
                    if value is not None:
                        arr[key] = value['value']
                else:
                    arr[key] = value
        elif isinstance(raw, list):
            for item in raw:
                extract(item, arr)
        return arr

    return extract(raw, arr)


def get_jira_issues():
    # global my_logger;
    # my_logger = get_logger(__name__, "main.log", use_formatter=True)
    payload_dict = {}
    config_dict = get_env_variables()
    _session = start_session()
    _value = 0
    # Use current date to update the next fetch start time
    curr_date = datetime.now() - relativedelta(minutes=30)
    for row in _session.execute(select([run_details])):
        _topic, _last_run = row
        if _topic == "Issues":
            _value = _last_run
            break;
    # payload_dict['jql'] = f"project = PLAT and updated > '{_value.strftime('%Y-%m-%d %H:%M')}' order by updated asc"
    payload_dict['jql'] = f"project = CPP"
    # payload_dict['jql'] = "project = PLAT and issuekey = PLAT-5288"
    payload_dict['expand'] = []
    payload_dict['fieldsByKeys'] = "false"

    if os.name == "nt":
        filename = "../dags/data_pipeline/field_list.json"
    else:
        filename = "/opt/airflow/dags/data_pipeline/field_list.json"

    with open(filename, "r") as fp:
        data = json.load(fp)
        for key, value in data.items():
            field_list = value

    issue_list = []
    wk_details = []
    startAt = 0
    total = 27000
    payload_dict['fields'] = field_list
    payload_dict['maxResults'] = 100
    time_taken_secs = 0

    while True:
        payload_dict['startAt'] = startAt
        config_dict['url_seg'] = "/rest/api/3/search"
        config_dict['data'] = json.dumps(payload_dict)
        res = make_http_request_data( **config_dict)

        if res.status_code != 200:
            print(f"Error:{payload_dict['jql']}")
            exit(1)
        total = res.json()['total']
        print(f"Elasped time: {res.elapsed.total_seconds()}")

        for issue in res.json()["issues"]:
            # startAt += 1
            ret_issue = dict2values(issue)
            if 'parent' in ret_issue and ret_issue['parent'] is not None:
                ret_issue['parentkey'] = ret_issue['parent']
            elif 'customfield_10018.key' in ret_issue and ret_issue['customfield_10018.key'] is not None:
                ret_issue['parentkey'] = ret_issue['customfield_10018.key']
            elif 'customfield_10014' in ret_issue and ret_issue['customfield_10014'] is not None:
                ret_issue['parentkey'] = ret_issue['customfield_10014']
            else:
                ret_issue['parentkey'] = None
            # print(f"{ret_issue['key']}:{ret_issue['progress.total']}:{ret_issue['progress.percent']}")
            issue_list.append(ret_issue)
            ret_wk = wkdict2values(issue['fields']['worklog'], ret_issue['key'])
            wk_details.extend(ret_wk['worklogs'])

            if issue['fields']['worklog']['total'] > 20:
                config_dict['url_seg'] = f"/rest/api/3/issue/{ret_issue['key']}/worklog"
                config_dict['payload'] = {'startAt': 20}

                res_wk = make_http_request("GET", **config_dict)
                ret_wk_1 = wkdict2values(res_wk.json(), ret_issue['key'])

                wk_details.extend(ret_wk_1['worklogs'])
        startAt += len(res.json()['issues'])
        print(f"Total Records: {total}, Records processed: {startAt}, length: {len(res.json()['issues'])}")
        if total == startAt:
            break
    print(f"Total Records: {total}, Records processed: {startAt}")

    df_wk = pd.DataFrame(wk_details)
    df_issue = pd.DataFrame(issue_list)
    if df_issue.empty:
        print("No Issues Found")
    if df_wk.empty:
        print("No Worklogs found")

    # Check if column parent exists in dataframe
    if 'parent' in df_issue:
        df_issue.drop(columns=["parent"], inplace=True)

    for column_name in ["customfield_10018.key", "customfield_10018.id", "customfield_10014"]:
        if column_name in df_issue.columns:
            df_issue.drop(columns=[column_name], inplace=True)

    for column_name in [
        'timeoriginalestimate', 'aggregatetimeoriginalestimate', 'timeestimate',
        'aggregatetimeestimate', 'timespent', 'aggregatetimespent',
    ]:
        if column_name in df_issue.columns:
            df_issue[column_name] = df_issue[column_name] / 3600
    # print(df_wk['issueKey'].unique)
    df_issue.rename(
        columns={
            'customfield_10015': 'startdate',
            'customfield_10020.name': 'sprint',
            'customfield_10020.id': 'sprintid',
            'customfield_10067': 'ClientJira',
            'customfield_10019': 'Rank',
            'customfield_10120': 'totaleffort',
            'customfield_10121': 'totaldeveffort',
            'customfield_10122': 'baeffort',
            'customfield_10123': 'adeffort',
            'customfield_10124': 'rdeffort',
            'customfield_10125': 'qaeffort',
            'customfield_10024': 'storypoints',
            'customfield_10126': 'contingency',
            'customfield_10059': 'testcaseno',
            'customfield_10060': 'testcasesuite',
            'customfield_10061': 'teststepno',
            'customfield_10062': 'scenariono',
            'customfield_10146': 'reqfinalized',
            "customfield_10078": 'approvalstatus'
        }, inplace=True
    )
    # print(df_issue.shape, df_issue.columns)

    if not df_issue.empty:
        df_issue["subkey"] = df_issue["key"].str.replace("PLAT-", "")

    try:
        if os.name == "nt":
            df_issue.to_csv("log/issuelog.csv", index=False)
            df_wk.to_csv("log/wklog.csv", index=False)
        else:
            df_issue.to_csv("/opt/airflow/dags/log/issuelog.csv", index=False)
            df_wk.to_csv("/opt/airflow/dags/log/wklog.csv", index=False)
    except PermissionError as e:
        print("Not able to create csv as permission denied!!!")
        print("Skipping file export step")
    except FileNotFoundError as e:
        print("Skipping file export step")
        print(e)
        print("Skipping writing to csv")

    for column_name in [
        'storypoints', 'timeoriginalestimate', 'aggregatetimeoriginalestimate', 'aggregatetimeestimate', 'timespent',
        'aggregatetimespent', 'timeestimate', 'progress_percent', 'progress_progress', 'progress_total',
        'aggregateprogress_progress', 'aggregateprogress_total', 'aggregateprogress_percent',
        'totaleffort', 'totaldeveffort', 'baeffort', 'adeffort', 'rdeffort', 'qaeffort', 'contingency'
    ]:
        if column_name in df_issue.columns:
            df_issue[column_name] = df_issue[column_name].fillna(0)

    # if df_issue.empty:
    #     print("No issues found. Skipping updates")
    # else:
    #     upsert(_session, Issue, df_issue, 'key')
    # if df_wk.empty:
    #     print("No Worklogs found. Skipping updates")
    # else:
    #     upsert(_session, WorkLog, df_wk, 'id')
    # _session.execute(run_details.update().values(LAST_RUN=curr_date).where(run_details.c.Topic == 'Issues'))
    # _session.commit()

    return df_issue, df_wk


# Global declaration of logger
# if os.name == 'nt':
#     my_logger = get_logger(__name__, "log/main.log", use_formatter=True)
# else:
#     my_logger = get_logger(__name__, "/opt/airflow/dags/log/main.log", use_formatter=True)

Base = declarative_base(cls=RepresentableBase)

class User(Base):
    __tablename__ = "User"
    accountId = Column(String, primary_key=True, nullable=False)
    accountType = Column(String, nullable=False)
    emailAddress = Column(CIText())
    displayName = Column(String)
    active = Column(Boolean)
    timeZone = Column(String)
    locale = Column(String)


class Issue(Base):
    __tablename__ = "Issue"

    id = Column(BIGINT, nullable=False)
    key = Column(String, nullable=False, primary_key=True)
    subkey = Column(BIGINT)
    summary = Column(String, nullable=False)
    # description = Column(String, nullable=True)
    isSubTask = Column(Boolean, nullable=False)
    issuetype = Column(String, nullable=False)
    statusCategory = Column(String, nullable=False)
    statuscategorychangedate = Column(DateTime(timezone=True), nullable=False, )
    resolution = Column(String)
    resolutiondate = Column(DateTime)
    components = Column(ARRAY(String), nullable=True)
    fixVersions = Column(ARRAY(String), nullable=True)
    versions = Column(ARRAY(String), nullable=True)
    assignee = Column(String, ForeignKey(User.accountId), nullable=True, )
    reporter = Column(String, ForeignKey(User.accountId), nullable=False)
    created = Column(DateTime(timezone=True), nullable=False)
    updated = Column(DateTime(timezone=True), nullable=False)
    status = Column(String, nullable=False)
    Rank = Column(String, nullable=False)
    sprint = Column(ARRAY(String), nullable=True)
    sprintid = Column(ARRAY(BIGINT), nullable=True)
    Team = Column(String, nullable=True)
    ClientJira = Column(ARRAY(String), nullable=True)
    startdate = Column(Date, nullable=True)
    duedate = Column(Date, nullable=True)
    originalestimate = Column(Numeric)
    timeoriginalestimate = Column(Numeric)
    aggregatetimeoriginalestimate = Column(Numeric)
    aggregatetimeestimate = Column(Numeric)
    timespent = Column(Numeric)
    aggregatetimespent = Column(Numeric)
    timeestimate = Column(Numeric)
    progress_progress = Column(Numeric)
    progress_total = Column(Numeric)
    progress_percent = Column(Numeric)
    aggregateprogress_progress = Column(Numeric)
    aggregateprogress_total = Column(Numeric)
    aggregateprogress_percent = Column(Numeric)
    totaleffort = Column(Numeric)
    totaldeveffort = Column(Numeric)
    baeffort = Column(Numeric)
    adeffort = Column(Numeric)
    rdeffort = Column(Numeric)
    qaeffort = Column(Numeric)
    contingency = Column(Numeric)
    parentkey = Column(String, nullable=True)
    parent_path = Column(LtreeType, nullable=True)
    storypoints = Column(BIGINT, nullable=True)
    testcaseno = Column(String)
    testcasesuite = Column(String)
    teststepno = Column(String)
    scenariono = Column(String)
    reqfinalized = Column(String)
    approvalstatus = Column(String)
    # parent = relationship(
    #     'Issue',
    #     primaryjoin=remote(parent_path) == foreign(func.subpath(parent_path, 0, -1)),
    #     backref='Issue',
    #     viewonly=True
    # )

    __table_args__ = (
        Index("ix_parent_path", parent_path, postgresql_using='gist'),
        # ForeignKeyConstraint(('reporter', 'assignee'), ['User.accountId'], name="fk_account_id"),
        # ForeignKeyConstraint(['assignee'], ['User.accountId']),
        {'schema': 'PLAT'}
    )

    def __str__(self):
        return self.name

    # def __repr__(self):
    #     return 'Issue({})'.format(self.name)

    def __init__(self, parent=None) -> None:
        super().__init__()
        self.parent_path = Issue.parentkey if self.parent is None else self.parent_path + parent.replace('-', '_')

    @reconstructor
    def init_parent_path(self, parent=None) -> None:
        print("init_parent_path called!!!")
        self.parent_path = Issue.parentkey if self.parent is None else self.parent_path + parent.replace('-', '_')


class WorkLog(Base):
    __tablename__ = "WorkLog"
    author = Column(String, nullable=False)
    updateauthor = Column(String, nullable=False)
    created = Column(DateTime, nullable=False)
    updated = Column(DateTime, nullable=False)
    started = Column(DateTime, nullable=False)
    timeSpent = Column(String, nullable=False)
    timeSpentSeconds = Column(Integer, nullable=False)
    id = Column(Integer, nullable=False, primary_key=True)
    issueId = Column(Integer, nullable=False)
    issueKey = Column(String, nullable=False)

    __table_args__ = (
        # ForeignKeyConstraint(['author'], ['User.accountId']),
        # ForeignKeyConstraint(['updateauthor'], ['User.accountId']),
        {'schema': 'PLAT'}
    )


convention = {
  "ix": 'ix_%(column_0_label)s',
  "uq": "uq_%(table_name)s_%(column_0_name)s",
  "ck": "ck_%(table_name)s_%(constraint_name)s",
  "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
  "pk": "pk_%(table_name)s"
}

metadata_obj = MetaData(naming_convention=convention)


run_details = Table(
    "Run_Details",
    metadata_obj,
    Column('Topic', String, primary_key=True),
    Column('LAST_RUN', DateTime)
)

if __name__ == '__main__':
    _, _ = get_jira_issues()