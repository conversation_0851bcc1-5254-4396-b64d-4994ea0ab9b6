import asyncio
import atexit
import base64
import functools
import json
import logging
import math
import os
import queue
import random
import re
import sys
import time
import traceback
from asyncio import Queue, current_task
from contextlib import contextmanager, asynccontextmanager
from datetime import datetime
from logging import Logger
from logging.handlers import Que<PERSON><PERSON><PERSON><PERSON>, TimedRotatingFileHandler
from typing import Dict, ContextManager, AsyncContextManager, Type, Union, Any, Optional
from urllib.parse import urlparse
from zoneinfo import ZoneInfo

import aiohttp
import markdownify
import numpy as np
import orjson

import pandas as pd
import psycopg2
import requests
import sqlalchemy.exc
from aiohttp import ClientResponse, ClientResponseError
from dateutil.relativedelta import relativedelta
from dependency_injector.containers import DynamicContainer
from dependency_injector.wiring import Provide, inject

from psycopg2 import extensions as psy_extension
from pykeepass import PyKeePass
from rich.align import Align
from rich.layout import Layout
from rich.live import Live
from rich.logging import Rich<PERSON>andler
from rich.panel import Panel
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, MofNCompleteColumn, SpinnerColumn
from rich.table import Table
from sqlalchemy import event, engine, create_engine, DDL, MetaData, Table, Column, String, DateTime, select, func, \
    update, null, inspect, and_, UniqueConstraint, exc, column, case, TEXT, Integer, delete
from sqlalchemy.dialects.postgresql import insert, dialect
from sqlalchemy.engine import Engine, URL, CursorResult
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_scoped_session
from sqlalchemy.orm import sessionmaker, aliased, Session
from sqlalchemy_utils import Ltree

from dags.data_pipeline.dbmodels.allboards import AllBoards
from dags.data_pipeline.dbmodels.base import Base
from dags.data_pipeline.dbmodels.changelog import ChangeLog, ChangelogJSON
from dags.data_pipeline.dbmodels.initiativeattribute import InitiativeAttribute
from dags.data_pipeline.dbmodels.issue import Issue, IssueLinks, IssueComments
from dags.data_pipeline.dbmodels.issueclassification import IssueClassification
from dags.data_pipeline.dbmodels.rundetails import RunDetailsJira
from dags.data_pipeline.dbmodels.sprint import SprintOld
from dags.data_pipeline.dbmodels.user import User
from dags.data_pipeline.dbmodels.versions import Versions
from dags.data_pipeline.dbmodels.worklog import WorkLog, DeletedWorklog
from dags.data_pipeline.utility_code import my_logger, main_console, \
    handle_exception, compile_query, MAX_RETRIES, INITIAL_RETRY_DELAY, \
    lock, JITTER_MULTIPLIER_RANGE, queue_container, create_db_extension, \
    create_schema_tables_ddl, get_deleted_worklog, get_fields, get_jira_users, get_all_jira_boards, \
    process_jira_versions, get_sprint_details, process_jira_issues, upsert_issue_classification, create_refresh_mv, \
    create_layout, monitor, format_time_difference
from dags.data_pipeline.database.upsert_operations import upsert, smart_retry
from dags.data_pipeline.queue_processors import fetch_changelog, fetch_worklog, fetch_comments
from dags.data_pipeline.jira.api_client import calculate_retry_delay
from dags.data_pipeline.dataframe_utils.dataframe_debugger import DataframeDebugger
from archived.reference import fetch_issues
from dags.data_pipeline.containers import EntryDetails, JiraEntryDetailsContainer, \
    get_kp_entry_details, LoggerContainer, ApplicationContainer, QueueContainer, CircuitBreakerConfig, CircuitState, \
    ErrorType, shutdown_handler


# Source https://stackoverflow.com/questions/2082152/case-insensitive-dictionary
# https://github.com/kennethreitz/requests/blob/v1.2.3/requests/structures.py#L37
class CaseInsensitiveDict(dict):
    @classmethod
    def _k(cls, key):
        return key.lower() if isinstance(key, basestring) else key

    def __init__(self, *args, **kwargs):
        super(CaseInsensitiveDict, self).__init__(*args, **kwargs)
        self._convert_keys()
    def __getitem__(self, key):
        return super(CaseInsensitiveDict, self).__getitem__(self.__class__._k(key))
    def __setitem__(self, key, value):
        super(CaseInsensitiveDict, self).__setitem__(self.__class__._k(key), value)
    def __delitem__(self, key):
        return super(CaseInsensitiveDict, self).__delitem__(self.__class__._k(key))
    def __contains__(self, key):
        return super(CaseInsensitiveDict, self).__contains__(self.__class__._k(key))
    def has_key(self, key):
        return super(CaseInsensitiveDict, self).has_key(self.__class__._k(key))
    def pop(self, key, *args, **kwargs):
        return super(CaseInsensitiveDict, self).pop(self.__class__._k(key), *args, **kwargs)
    def get(self, key, *args, **kwargs):
        return super(CaseInsensitiveDict, self).get(self.__class__._k(key), *args, **kwargs)
    def setdefault(self, key, *args, **kwargs):
        return super(CaseInsensitiveDict, self).setdefault(self.__class__._k(key), *args, **kwargs)
    def update(self, E={}, **F):
        super(CaseInsensitiveDict, self).update(self.__class__(E))
        super(CaseInsensitiveDict, self).update(self.__class__(**F))
    def _convert_keys(self):
        for k in list(self.keys()):
            v = super(CaseInsensitiveDict, self).pop(k)
            self.__setitem__(k, v)


def nan_to_null(f, _NULL=psy_extension.AsIs('NULL'), _Float=psy_extension.Float):
    if f != f:
        return _NULL
    else:
        return _Float(f)

psy_extension.register_adapter(float, nan_to_null)


@event.listens_for(engine.Engine, "handle_error")
def erase_parameters(exception_context):
    my_logger.error(exception_context)
    for exc_error in [
        exception_context.chained_exception,
        exception_context.original_exception,
        exception_context.sqlalchemy_exception,
    ]:
        if exc_error is not None:
            exc_error.params = {"no_parameters": "parameters hidden"}


# Source:
# https://stackoverflow.com/questions/1171166/how-can-i-profile-a-sqlalchemy-powered-application/1175677#1175677

@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement,
                        parameters, context, executemany):
    context._query_start_time = time.time()
    my_logger.debug("Start Query:\n%s" % statement)
    # Modification for StackOverflow answer:
    # Show parameters, which might be too verbose, depending on usage..
    my_logger.debug("Parameters:\n%r" % (parameters,))


@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement,
                        parameters, context, executemany):
    total = time.time() - context._query_start_time
    my_logger.debug("Query Complete!")

    # Modification for StackOverflow: times in milliseconds
    my_logger.debug(f'Total Time: {total:.3f} seconds')
    # my_logger.dataframe_utils("Total Time: %.02fms" % (total*1000))


def start_session(prjkey: str = "plat"):

    keedb = os.getenv("AIRFLOW_HOME") + "/Database.kdbx"
    keepass = os.getenv("AIRFLOW_HOME") + "/Database.key"
    ref = PyKeePass(keedb, keyfile=keepass)
    entry = ref.find_entries(title="JIRA_RW", first=True)

    conn_str = URL.create(
        drivername=entry.get_custom_property("DB_DRIVER"),
        username=entry.username,
        password=entry.password,
        host=entry.get_custom_property("DB_SERVER_NAME"),
        port=entry.get_custom_property("DB_SERVER_RW_PORT"),
        database=entry.get_custom_property("DB_NAME")
    )

    try:
        engine_ = create_engine(
            conn_str,
            connect_args={
                # 'options': '-csearch_path={}'.format(dbschema),
                'connect_timeout': 30,
                "keepalives": 1,
                "keepalives_idle": 30,
                "keepalives_interval": 10,
                "keepalives_count": 5,
            },
            echo=False,
            pool_pre_ping=True,
            pool_use_lifo=True,
            pool_recycle=300, max_overflow=20, pool_size=10
        ).execution_options(schema_translate_map={None: prjkey})

        # insp = inspect(engine_)
        # my_logger.info(insp.get_schema_names())

        session = sessionmaker(bind=engine_, autoflush=False)
        # session.configure()
        # Base.metadata.bind = engine_

        # Base.metadata.create_all(
        #     engine_.execution_options(schema_translate_map={None: prjkey}),
        # )

        # Define a DDL for creating schema if it doesn't exist
        create_schema_ddl = DDL(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_namespace
                    WHERE nspname = '%(schema)s'
                ) THEN
                    CREATE SCHEMA %(schema)s;
                END IF;
            END $$;
            """,
            context={'schema': None}  # placeholder for schema name
        )

        event.listen(
            Base.metadata,
            'before_create',
            create_schema_ddl.execute_if(dialect='postgresql')
        )

        Base.metadata.create_all(bind=engine_, checkfirst=True)

        return session()
    except sqlalchemy.exc.OperationalError as e:
        print(e)
        handle_pyscopg2_exception(e)
        exit(1)
    except psycopg2.OperationalError as e:
        print(e)
        handle_pyscopg2_exception(e)
        exit(1)


@contextmanager
def session_scope(project: str):
    """Provide a transactional scope around a series of operations."""
    session = start_session(project)
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.close()


def handle_pyscopg2_exception(err):
    # get details about the exception
    exc_type, exc_value, exc_tb = sys.exc_info()

    # get the line number when exception occurred
    line_num = exc_tb.tb_lineno
    tb = traceback.TracebackException(exc_type, exc_value, exc_tb)

    my_logger.error(f'{line_num} psycopg2 traceback with {err}: {"".join(tb.format_exception_only())}', exc_info=True)
    print('exiting with Failure !!!')


metadata_obj = MetaData(schema=None)
run_details = Table(
    "Run_Details",
    metadata_obj,
    Column('Topic', String, primary_key=True),
    Column('LAST_RUN', DateTime)
)
function_update_parent_path = DDL("CREATE OR REPLACE FUNCTION update_issue_parent_path() RETURNS TRIGGER AS $$ "
                                  "DECLARE "
                                  "path ltree; "
                                  "BEGIN "
                                  "RAISE NOTICE 'Inside update_issue_parent_path %%', NEW.key; "
                                  "IF NEW.parent_key IS NULL THEN "
                                  "NEW.parent_path = replace(NEW.key, '-', '_')::ltree; "
                                  "ELSEIF TG_OP = 'INSERT' OR OLD.parent_key IS NULL OR OLD.parent_key != NEW.parent_key THEN "
                                  "SELECT text2ltree(concat_ws('.', b.parent_path, subpath(a.parent_path, 1, 1))) "
                                  "FROM \"issue\" a, \"issue\" b "
                                  "WHERE a.key = NEW.key and a.parent_key = b.key INTO path; "
                                  "IF path IS NULL THEN "
                                  "RAISE EXCEPTION 'Invalid parent_key %%', NEW.key; "
                                  "END IF; "
                                  "NEW.parent_path = path; "
                                  "END IF; "
                                  "RETURN NEW; "
                                  "END; "
                                  "$$ LANGUAGE plpgsql;")
parent_path_trigger = DDL(
    'CREATE TRIGGER parent_path_tgr BEFORE INSERT OR UPDATE ON "issue" FOR EACH ROW EXECUTE PROCEDURE update_issue_parent_path();'
)

event.listen(Issue.__table__, 'after_create', function_update_parent_path)
event.listen(Issue.__table__, 'after_create', parent_path_trigger)


def init_jira_get_worklog():
    # Discard this approach as it is not possible to segregate based on project
    config_dict = get_env_variables()

    _session = start_session()
    _value = 0
    for row in _session.execute(
            select(run_details.columns.Topic, run_details.columns.LAST_RUN).select_from(run_details)):
        _topic, _last_run = row
        if _topic == "WorkLog":
            _value = int(_last_run.timestamp() * 1e3)
            break
    config_dict['payload'] = {'since': str(_value)}
    lastPage = False

    count = 0
    while not lastPage:
        config_dict['url_seg'] = "/rest/api/3/worklog/updated"
        response = make_http_request("GET", **config_dict)
        res = response.json()
        response_json = [{key: value for key, value in d.items() if key in ['worklogId']} for d in res['values']]
        _df = pd.DataFrame(response_json)
        wk_dict = json.dumps({"ids": _df['worklogId'].tolist()})

        config_dict['url_seg'] = "/rest/api/3/worklog/list"
        config_dict['data'] = json.dumps({"ids": [10052]})
        response = make_http_request_data(**config_dict)

        config_dict['payload'] = {'since': res['until']}
        lastPage = res['lastPage']
        count += 1
        break


def measure_time(f):
    def timed(*args, **kwargs):
        time_started = time.monotonic_ns()
        result = f(*args, **kwargs)
        time_end = time.monotonic_ns()
        logging.info('Function Name: %r, Arguments Passed= [%r, %r], Time Taken = %f sec' %
                     (f.__name__, args, kwargs, (time_end - time_started) * pow(10, -9)))
        return result

    return timed


def get_jira_issues(prjkey: str, scope: str = 'project'):
    payload_dict = {}
    config_dict = get_env_variables()
    _session = start_session(prjkey)
    _value = 0
    # Use current date to update the next fetch start time
    curr_date = datetime.now() - relativedelta(minutes=30)
    for row in _session.execute(
            select(run_details.columns.Topic, run_details.columns.LAST_RUN).select_from(run_details)
    ):
        _topic, _last_run = row
        if _topic == "Issues":
            _value = _last_run
            break
    if scope == 'project':
        payload_dict['jql'] = f"project = {prjkey} and updated > '{_value.strftime('%Y-%m-%d %H:%M')}' and key= PLAT-13"
    elif scope == 'recon':
        issue_subtask = aliased(Issue)
        stmt = select(
            Issue.key
        ).select_from(Issue).join(IssueClassification, Issue.key == IssueClassification.standard_key).join(
            issue_subtask, issue_subtask.key == IssueClassification.key).filter(
            Issue.isSubTask == 'false',
            issue_subtask.isSubTask == 'true',
            ~Issue.issuetype.in_(
                ['Bug', 'Production Defect', 'UAT Defect', 'In-sprint Defects', 'Observation', 'Epic', 'Initiative'])
        ).group_by(
            Issue.key, Issue.aggregatetimespent, Issue.timespent
        ).having(
            func.round(Issue.aggregatetimespent - Issue.timespent, 1) != func.round(
                func.sum(issue_subtask.aggregatetimespent), 1)
        )
        result = _session.execute(stmt).fetchall()
        output_list = [row[0] for row in result]
        payload_dict['jql'] = f"project = {prjkey} and key in ({', '.join(output_list)})"
    else:
        # payload_dict['jql'] = f"project = {prjkey} and key = 'PLAT-105093'"
        # payload_dict['jql'] = f"project = {prjkey} and 'Initiative Details[Short text]' is not EMPTY"
        payload_dict['jql'] = f"project = {prjkey} and key in (PLAT-47282, PLAT-191404, PLAT-187846)"
        # payload_dict['jql'] = f"project = {prjkey} and key = 'PLAT-484'"
        # payload_dict['jql'] = "project = PLAT and issuekey = PLAT-5288"
    payload_dict['expand'] = ['changelog', 'renderedFields']
    payload_dict['fieldsByKeys'] = "false"
    # TODO: Use dynaconf for managing the configuration.

    if os.name == "nt":
        filename = "../dags/data_pipeline/field_list.json"
    elif os.uname().nodename == "airflow":
        filename = "/home/<USER>/airflow/dags/data_pipeline/field_list.json"
    else:
        filename = "/opt/airflow/dags/airflow/data_pipeline/field_list.json"

    with open(filename, "r") as fp:
        data = json.load(fp)
        for key, value in data.items():
            field_list = value

    startAt = 0
    total = 100
    payload_dict['fields'] = field_list
    payload_dict['maxResults'] = 100
    time_taken_secs = 0

    while startAt < total:
        issue_list = []
        wk_details = []
        change_log_master = []
        change_log_master_new = []
        initiative_attributes = []
        payload_dict['startAt'] = startAt
        config_dict['url_seg'] = "/rest/api/3/search"
        config_dict['data'] = json.dumps(payload_dict)
        res = make_http_request_data(**config_dict)
        if res.status_code != 200:
            print(f"Error:{payload_dict['jql']}")
            exit(1)
        total = res.json()['total']

        # Code change
        startAt += len(res.json()["issues"])
        time_taken_secs += res.elapsed.total_seconds()
        proj_id_dict = dict(acq='10035', plat='10028', cpp='10029', ccp='10030', plp='10034')
        counter = 0
        for issue in res.json()["issues"]:
            # Code Change
            # startAt += 1
            # Check changelog
            change_log = []
            change_log_new = []

            counter += 1

            if issue['changelog']['total'] < 101:
                for history in issue['changelog']['histories']:

                    for item in history['items']:
                        change_log_new.append(
                            [
                                history['id'], history.get('author', {}).get('accountId', None),
                                history['created'],
                                history['items'],
                                issue['key'], issue['id']
                            ]
                        )
                        field_id = item['fieldId'] if 'fieldId' in item else None
                        author_account_id = history.get('author', {}).get('accountId', None)
                        # Append to change_log with the extracted or default accountId
                        change_log.append(
                            [
                                history['id'], author_account_id, history['created'],
                                item['field'], item['fieldtype'], item.get("fieldId", None),
                                item['from'], item['fromString'],
                                item['to'], item['toString'],
                                item.get("tmpFromAccountId", None),
                                item.get("tmpToAccountId", None),
                                issue['key'], issue['id'],
                            ]
                        )
            else:
                config_dict_hist = get_env_variables()
                payload_dict['startAt'] = 0
                config_dict_hist['url_seg'] = f"/rest/api/3/issue/{issue['key']}/changelog"
                while True:
                    config_dict_hist['payload'] = payload_dict
                    his_ret = make_http_request("GET", **config_dict_hist)
                    for history in his_ret.json()['values']:
                        change_log_new.append(
                            [
                                history['id'], history['author']['accountId'],
                                history['created'],
                                history['items'],
                                issue['key'], issue['id']
                            ]
                        )
                        for item in history['items']:
                            field_id = item['fieldId'] if 'fieldId' in item else None
                            if 'author' in history:
                                change_log.append(
                                    [
                                        history['id'], history['author']['accountId'], history['created'],
                                        item['field'], item['fieldtype'], field_id, item['from'], item['fromString'],
                                        item['to'], item['toString'],
                                        issue['key'], issue['id'],
                                    ]
                                )
                            else:
                                # if anonymous user performs an activity, then lookup on author fails
                                # Hardcode my id

                                change_log.append(
                                    [
                                        history['id'], "5ec59968b199aa0c13f1c552", history['created'],
                                        item['field'], item['fieldtype'], field_id, item['from'], item['fromString'],
                                        item['to'],
                                        item['toString'],
                                        issue['key'], issue['id'],
                                    ]
                                )
                    payload_dict['startAt'] += 100
                    if his_ret.json()['isLast']:
                        break
                    if his_ret.json()['isLast'] > 0:
                        check_progress(payload_dict['startAt'] / his_ret.json()['isLast'])

            change_log_master.extend(change_log)
            change_log_master_new.extend(change_log_new)
            # End change

            # Issue Links
            # Collect all issue link data in a list of dictionaries
            issue_links_data = []

            for item in issue['fields']['issuelinks']:
                outward_issue = item.get("outwardIssue", {})
                outward_issue_id = outward_issue.get("id")
                outward_issue_key = outward_issue.get("key")
                inward_issue = item.get("inwardIssue", {})
                inward_issue_id = inward_issue.get("id")
                inward_issue_key = inward_issue.get("key")

                issue_link_data = {
                    'id': item.get("id"),
                    'type': item.get("type"),
                    'outwardIssue_id': outward_issue_id,
                    'outwardIssue_key': outward_issue_key,
                    'inwardIssue_id': inward_issue_id,
                    'inwardIssue_key': inward_issue_key,
                    'issue_id': issue['id'],
                    'issue_key': issue['key']
                }

                issue_links_data.append(issue_link_data)

            # Create a DataFrame from the list of dictionaries
            if len(issue_links_data) > 0:
                issue_links_df = pd.DataFrame(issue_links_data)
                upsert(_session, IssueLinks, issue_links_df, 'id')

            # End Issue links
            config_comment = get_env_variables()
            config_comment['url_seg'] = f"/rest/api/3/issue/{issue['key']}/comment"
            start_at = 0
            payload_dict_comment = {'expand': 'renderedBody'}

            while True:
                payload_dict_comment['startAt'] = start_at
                config_comment['payload'] = payload_dict_comment
                response = make_http_request("GET", **config_comment)
                response.raise_for_status()
                data = response.json()
                comment_data = data.get('comments', [])
                if not comment_data:
                    break
                comments = parse_comment_data(comment_data, issue['id'], issue['key'])
                upsert(_session, IssueComments, comments, 'id')

                if data['total'] <= start_at + 5000:
                    break
                start_at += 5000

            ret_issue = dict2values(issue)
            if ret_issue['issuetype'] in ['Initiative', 'Initiative ']:
                ret_issue['issue_hierarchy_level'] = 1
            elif ret_issue['issuetype'] == 'Epic':
                ret_issue['issue_hierarchy_level'] = 2
            elif ret_issue['isSubTask'] is True:
                ret_issue['issue_hierarchy_level'] = 4
            else:
                ret_issue['issue_hierarchy_level'] = 3

            if ret_issue['parent_key'] is None:
                ret_issue['path'] = Ltree(ret_issue['key'].replace("-", "_"))
                ret_issue['path_id'] = Ltree(str(ret_issue['id']))
                ret_issue['parent_path'] = Ltree(prjkey)
                ret_issue['parent_path_id'] = Ltree(proj_id_dict[prjkey])
            else:
                ret_issue['path'] = Ltree(ret_issue['parent_key'].replace("-", "_")) + \
                                    Ltree(ret_issue['key'].replace("-", "_"))
                ret_issue['path_id'] = Ltree(str(ret_issue['parent_id'])) + Ltree(str(ret_issue['id']))
                if ret_issue['issuetype'] == 'Epic':
                    ret_issue['parent_path'] = Ltree(ret_issue['key'].replace("-", "_")) + \
                                               Ltree(ret_issue['parent_key'].replace("-", "_"))
                    ret_issue['parent_path_id'] = Ltree(proj_id_dict[prjkey]) + Ltree(str(ret_issue['parent_id']))
                else:
                    ret_issue['parent_path'] = Ltree(ret_issue['parent_key'].replace("-", "_"))
                    ret_issue['parent_path_id'] = Ltree(str(ret_issue['parent_id']))
            ret_issue['description_markdown'] = \
                markdownify.markdownify(issue['renderedFields']['description'], heading_style='ATX')

            if ret_issue['issuetype'] in ['Initiative', 'Initiative ']:
                initiative_attributes.append(
                    [
                        ret_issue['id'], ret_issue['key'], ret_issue['customfield_10182'],
                        ret_issue['customfield_10183'], ret_issue['customfield_10184']
                    ]
                )

            if ret_issue["linked_issues"]:
                ret_issue["linked_issues"].append(issue['key'])
                ret_issue["linked_issues"] = sorted(ret_issue["linked_issues"], key=custom_sort_key)
            else:
                ret_issue["linked_issues"] = None

            # print the values
            # print(ret_issue['Team'])
            # print(ret_issue['customfield_10179'])
            # ret_issue['customfield_10179'] = ret_issue['Team']
            # print(ret_issue['statusCategory'])
            # end print the values

            issue_list.append(ret_issue)
            ret_wk = wkdict2values(issue['fields']['worklog'], ret_issue['key'], ret_issue['parent_key'])
            wk_details.extend(ret_wk['worklogs'])

            if issue['fields']['worklog']['total'] > 20:
                config_dict['url_seg'] = f"/rest/api/3/issue/{ret_issue['key']}/worklog"
                config_dict['payload'] = {'startAt': 20}

                res_wk = make_http_request("GET", **config_dict)
                time_taken_secs += res_wk.elapsed.total_seconds()
                ret_wk_1 = wkdict2values(res_wk.json(), ret_issue['key'], ret_issue['parent_key'])
                wk_details.extend(ret_wk_1['worklogs'])
        if total > 0:
            update_progress(startAt, total)

        # Bringing in the code inside if loop and commiting for each row.
        # This should avoid creating huge dataframes

        df_changelog = pd.DataFrame(change_log_master, columns=[
            "id", "author", "created", "field", "fieldtype", "fieldId", "from_", "fromString", "to", "toString",
            "issue_key", "issue_id"
        ])
        df_changelog_new = pd.DataFrame(
            change_log_master_new, columns=[
                "id", "author", "created", "items", "issue_key", "issue_id"
            ]
        )
        # Testing
        # df_changelog.from_.fillna("null", inplace=True)
        # df_changelog.to.fillna("null", inplace=True)
        df_wk = pd.DataFrame(wk_details)
        df_issue = pd.DataFrame(issue_list)
        df_initiative_attribute = pd.DataFrame(initiative_attributes, columns=[
            "initiative_id", "initiative_key", "project", "release", "feature"
        ])

        # Check if column parent exists in dataframe. parent is coming from JIRA API JSON respon
        if 'parent' in df_issue:
            df_issue.drop(columns=["parent"], inplace=True)

        for column_name in [
            "customfield_10018.key", "customfield_10018.id", "customfield_10014",
            "customfield_10182", "customfield_10183", "customfield_10184"
        ]:
            if column_name in df_issue.columns:
                df_issue.drop(columns=[column_name], inplace=True)

        # timeoriginalestimate: Original estimate
        # timespent:    Time logged in Time tracking
        # timeestimate: Tune remaining in Time tracking
        for column_name in [
            'timeoriginalestimate', 'aggregatetimeoriginalestimate', 'timeestimate',
            'aggregatetimeestimate', 'timespent', 'aggregatetimespent',
        ]:
            if column_name in df_issue.columns:
                df_issue[column_name] = df_issue[column_name] / 3600

        df_issue.rename(
            columns={
                'customfield_10015': 'startdate',
                'customfield_10020.name': 'sprint',
                'customfield_10020.id': 'sprintid',
                'customfield_10067': 'ClientJira',
                'customfield_10019': 'Rank',
                'customfield_10120': 'totaleffort',
                'customfield_10121': 'totaldeveffort',
                'customfield_10122': 'baeffort',
                'customfield_10123': 'adeffort',
                'customfield_10124': 'rdeffort',
                'customfield_10125': 'qaeffort',
                'customfield_10024': 'storypoints',
                'customfield_10126': 'contingency',
                'customfield_10059': 'testcaseno',
                'customfield_10060': 'testcasesuite',
                'customfield_10061': 'teststepno',
                'customfield_10062': 'scenariono',
                'customfield_10146': 'reqfinalized',
                "customfield_10078": 'approvalstatus',
                "customfield_10147": "reopen_count",
                "customfield_10092": "urgency",
                "customfield_10179": "qc_check",
                "customfield_10199": "cvss_score",
                "customfield_10071": "initiated_by",
                "customfield_10006": "change_risk",
                "customfield_10056": "category_type",
                "customfield_10049": "severity",
                "customfield_10256": "initiative_detail"
            }, inplace=True
        )

        df_initiative_attribute.rename(
            {
                "customfield_10182": "project",
                "customfield_10183": "release",
                "customfield_10184": "feature"
            }, inplace=True
        )

        # Check if parent key exists in dataframe
        # key_map = zip(df_issue.id, df_issue.parent_id)
        if not df_issue.empty:
            df_issue['parent_key_exists'] = np.where(df_issue['id'].isin(df_issue['parent_id']), 'Y', 'N')
            # df_issue['parent_path_id'] = np.where(df_issue['parent_id'].isin(df_issue['id']), df_issue['path_id'], pd.Series([Ltree('0')]))
            # df_issue['parent_path'] = np.where(df_issue['parent_id'].isin(df_issue['id']), df_issue['path'], pd.Series([Ltree('none')]))
            # df_issue['path'] = np.where(df_issue['parent_key_exists'] == 'Y', df_issue['parent_path'] + df_issue['path'][1:], df_issue['path'])
            # df_issue['path_id'] = np.where(df_issue['parent_key_exists'] == 'Y', df_issue['parent_path_id'] + df_issue['path_id'][1:], df_issue['path_id'])

        if not df_issue.empty:
            df_issue.drop(columns=['parent_key_exists'], inplace=True)

        for column_name in [
            'storypoints', 'timeoriginalestimate', 'aggregatetimeoriginalestimate', 'aggregatetimeestimate',
            'timespent',
            'aggregatetimespent', 'timeestimate', 'progress_percent', 'progress_progress', 'progress_total',
            'aggregateprogress_progress', 'aggregateprogress_total', 'aggregateprogress_percent',
            'totaleffort', 'totaldeveffort', 'baeffort', 'adeffort', 'rdeffort', 'qaeffort', 'contingency'
        ]:
            if column_name in df_issue.columns:
                df_issue[column_name] = df_issue[column_name].fillna(0)

        key_breaking_relationship = set()

        if df_issue.empty:
            print("No issues found. Skipping updates")
        else:
            if column_name in ['Team', 'reqfinalized', 'approvalstatus']:
                df_issue[column_name] = df_issue[column_name].fillna('')

            # Remove keys that break refrential integrity
            parent_key_set = set(df_issue['parent_key'].unique())
            # Remove None
            parent_key_set.discard(None)
            key_set = set(df_issue['key'])

            # Check if parent key exists in key_set else check if exists in DB
            # if not found, then drop from dataframe

            # key_missing = parent_key_set - key_set
            # my_logger.info(f'Missing keys: {list(key_missing)}')
            #
            # if sqlalchemy.__version__ == '1.3.24':
            #     result = _session.query(Issue.key).filter(Issue.key.in_(list(key_missing)))
            #     df_key = pd.DataFrame(result, columns=['key'])
            # else:
            #     query = select(Issue.key).select_from(Issue).filter(Issue.key.in_(list(key_missing)))
            #     result = _session.execute(query).all()
            #
            # df_key = pd.DataFrame(result, columns=['key'])
            #
            # key_in_db = set(df_key['key'])

            # As there is no foreign key relationship between key and parent, no need to check
            # key_breaking_relationship = key_missing - key_in_db
            # my_logger.dataframe_utils(f'parent key to be dropped: {key_missing - key_in_db} ')
            if key_breaking_relationship:
                my_logger.debug("Inside key breaking relationship check")
                # Get keys that are getting dropped from issue
                # Drop those from changelog
                df_issue_keys = df_issue[(df_issue['parent_key'].isin(key_breaking_relationship))]
                df_issue_keys_list = df_issue_keys['key'].to_list()
                df_changelog = df_changelog[~(df_changelog['issue_key'].isin(df_issue_keys_list))]
                df_issue = df_issue[~(df_issue['parent_key'].isin(key_breaking_relationship))]

            check_dup = not df_issue['id'].is_unique
            if check_dup:
                duplicate = df_issue[df_issue.duplicated('id')]
                my_logger.info("Duplicate Rows Dropped")
                my_logger.info(f'{duplicate}')
                df_issue.drop_duplicates(subset='id', inplace=True, keep='first')

            df_issue.to_csv("e:/vishal/issue_dump.csv")

            upsert(_session, Issue, df_issue, 'id', no_update_cols=('tscv_summary_description',))
            # df_changelog.drop_duplicates(inplace=True, keep='first')
            # my_logger.info(df_changelog.info(verbose=True, memory_usage='deep'))
            df_changelog.to_csv("e:/vishal/change_log.csv")
            upsert(_session, ChangeLog, df_changelog, 'id', on_conflict_update=False)

            upsert(_session, ChangelogJSON, df_changelog_new, 'id', on_conflict_update=False)

        if df_initiative_attribute.shape[0] > 0:
            # my_logger.info(df_initiative_attribute.info(verbose=True, memory_usage='deep'))
            upsert(_session, InitiativeAttribute, df_initiative_attribute, "initiative_id",
                   no_update_cols=('attr',))

        if df_wk.empty:
            print("No Worklogs found. Skipping updates")
        else:
            if key_breaking_relationship:
                df_wk = df_wk[~(df_wk['parent_key'].isin(key_breaking_relationship))]
            df_wk.drop(columns=['parent_key'], inplace=True)
            check_dup = not df_wk['id'].is_unique
            df_wk.drop_duplicates(subset='id', inplace=True)
            # my_logger.info(df_wk.info(verbose=True, memory_usage='deep'))
            upsert(_session, WorkLog, df_wk, primary_key='id', no_update_cols=("timeSpentHours",))
    if scope == 'project':
        _session.execute(run_details.update().values(LAST_RUN=curr_date).where(run_details.c.Topic == 'Issues'))

    # Since i am using delayed evaluation
    # Update parent key to null if it does not exist in key column

    # Subquery to find parent_keys without corresponding keys
    issue_parent = aliased(Issue)
    find_parent_key = (
        select(issue_parent.parent_key)
        .select_from(issue_parent)
        .outerjoin(Issue, issue_parent.parent_key == Issue.key)
        .filter(Issue.key.is_(None), issue_parent.parent_key.is_not(None))
        .distinct()
    ).subquery()

    # Convert the subquery into a select statement explicitly
    find_parent_key_select = select(find_parent_key.c.parent_key)

    update_stmt = (
        update(Issue)
        .where(Issue.parent_key.in_(find_parent_key_select))
        .values(parent_key=null(), parent_id=null())
        .execution_options(synchronize_session='fetch')
    )
    # Execute the update statement
    _session.execute(update_stmt)

    _session.commit()
    # return df_issue, df_wk


def wkdict2values(raw, issueKey: str, parent_key: str) -> dict:
    arr = {}
    for key, value in raw.items():
        if key == 'worklogs':
            wk_list = []
            for wk in value:
                wk_list.extend(
                    [
                        {
                            'author': wk["author"]["accountId"],
                            'updateauthor': wk["updateAuthor"]["accountId"],
                            'created': wk["created"],
                            'updated': wk["updated"],
                            'started': wk["started"],
                            'timeSpent': wk["timeSpent"],
                            'timeSpentSeconds': wk["timeSpentSeconds"],
                            'id': wk["id"],
                            'issue_id': wk["issueId"],
                            'issue_key': issueKey,
                            'parent_key': parent_key
                        }
                    ]
                )
            arr['worklogs'] = wk_list
        else:
            arr[key] = value
    return arr


def traverse_issuelinks(issuelinks: list) -> list:
    elements = []

    for elem in issuelinks:
        id = elem["id"]
        link_type = elem['type']['name']
        link_type_inward = elem['type']['inward']
        link_outward = elem['type']['outward']
        if "outwardIssue" in elem:
            rel_key = elem['outwardIssue']['key']
            rel_id = elem['outwardIssue']['id']
        else:
            rel_key = elem['inwardIssue']['key']
            rel_id = elem['inwardIssue']['id']
        elements.append(rel_key)
    return elements


def dict2values(raw):
    arr = {}

    key_mapping = {
        'issuelinks': None,
        'worklog': None, 'changelog': None, 'renderedFields': None
    }

    def extract(raw, arr):
        seqs = tuple, list, set, frozenset
        final = str, int
        # arr['customfield_10018.key'] = arr['customfield_10018.id'] = None
        arr['parent_key'] = None
        arr['parent_id'] = None
        if isinstance(raw, dict):
            for key, value in raw.items():
                if key == "fields":
                    extract(value, arr)
                elif key in ['issuelinks']:
                    arr['linked_issues'] = traverse_issuelinks(value)
                elif key in ['worklog', 'changelog', 'renderedFields']:
                    pass
                elif key in ["assignee", "reporter"]:
                    arr[key] = None if value is None else value['accountId']
                elif key in ["expand", "self"]:
                    pass
                elif key in ["issuetype"]:
                    arr[key] = value['name']
                    arr['isSubTask'] = value['subtask']
                elif key == "parent":
                    if value is not None:
                        arr[f'{key}_key'] = value['key']
                        arr[f'{key}_id'] = value['id']
                elif key in ["resolution", "status", "priority"]:
                    arr[key] = None if value is None else value['name']
                    if key == "status":
                        arr['statusCategory'] = value['statusCategory']['name']
                elif key in ["components", "fixVersions", "versions"]:
                    arr[key] = None if value is None else [i['name'] for i in value]
                elif key in ["aggregateprogress", "progress"]:
                    if value is not None:
                        arr[key + '_' + 'progress'] = value["progress"] / 3600
                        arr[key + '_' + 'total'] = value["total"] / 3600
                        if 'percent' in value:
                            arr[key + '_' + 'percent'] = value['percent'] / 100
                        else:
                            arr[key + '_' + 'percent'] = None
                elif key in ['customfield_10001']:
                    if value is not None:
                        arr['Team'] = value['title']
                    else:
                        arr['Team'] = None
                elif key in ['customfield_10020']:
                    if value is not None:
                        arr[key + '.' + 'name'] = [name["name"] for name in value]
                        arr[key + '.' + 'id'] = [name["id"] for name in value]
                    else:
                        arr[key + '.' + 'name'] = None
                        arr[key + '.' + 'id'] = None

                elif key in [
                    'customfield_10146', 'customfield_10078', 'customfield_10179',
                    "customfield_10071", "customfield_10006", "customfield_10056", "customfield_10049"
                ]:
                    arr[key] = value['value'] if value is not None else None
                elif key in ['customfield_10147', ]:
                    if value is None:
                        arr[key] = 0
                    else:
                        arr[key] = value
                elif key in ['customfield_10256']:
                    if value is None:
                        arr[key] = None
                    else:
                        arr[key] = value
                else:
                    arr[key] = value
        elif isinstance(raw, list):
            for item in raw:
                extract(item, arr)
        return arr

    return extract(raw, arr)


def remove_deleted_worklog_():
    config_dict = get_env_variables()
    _session = start_session()
    payload_dict = {}
    _value = 0
    # Use current date to update the next fetch start time
    curr_date = datetime.now() - relativedelta(minutes=30)
    for row in _session.execute(select([run_details])):
        _topic, _last_run = row
        if _topic == "Worklog":
            _value = int((_last_run - datetime(1970, 1, 1)).total_seconds())
            break

    last_page = False
    config_dict['url_seg'] = "/rest/api/3/worklog/deleted"
    payload_dict['since'] = _value
    config_dict['payload'] = json.dumps(payload_dict)
    wkid_list = []
    while not last_page:
        res = make_http_request("GET", **config_dict)
        last_page = res.json()['lastPage']
        wkid = [{key: value for key, value in i.items() if key == "worklogId"} for i in res.json()["values"]]
        wkid_list.extend([item for sublist in [list(item.values()) for item in wkid] for item in sublist])
        # urlparse returns scheme, netloc, path, params, query, fragment
        config_dict["payload"] = urlparse(res.json()['nextPage']).query if not last_page else None
    stmt = WorkLog.__table__.delete().where(WorkLog.id.in_(wkid_list))
    _session.execute(stmt)
    _session.execute(run_details.update().values(LAST_RUN=curr_date).where(run_details.c.Topic == 'Worklog'))
    _session.commit()


def init_jira_update_versions(prjkey: str = "plat"):
    print("starting sessions")
    _session = start_session(prjkey)
    # _session.execute("TRUNCATE TABLE versions")
    print(f" getting version details from jira api calls")
    df = get_versions(prjkey)
    print(f'retrieved rows: {df.shape}')

    # Get existing IDs from the database
    existing_ids = {row.id for row in _session.query(Versions.id).all()}
    new_ids = set(df['id'].astype(int))

    # Find IDs that are in the database but not in the new data
    ids_to_delete = list(existing_ids - new_ids)
    print(f"ids to delete: {ids_to_delete}")
    # Delete rows that are no longer in the new data
    # if ids_to_delete:
    #     _session.execute(delete(Versions).where(Versions.id.in_(ids_to_delete)))
    print(f"firing upsert")

    upsert(_session, Versions, df, 'id')
    _session.commit()


def get_versions(prjkey: str):
    config_dict = get_env_variables()
    config_dict['url_seg'] = f"/rest/api/3/project/{prjkey.upper()}/versions"
    response = make_http_request("GET", **config_dict)
    df = pd.DataFrame(response.json())

    # df['overdue'] = df['overdue'].replace(NaN, False)
    df.replace({np.nan: None}, inplace=True)
    df.drop(columns=['self'], inplace=True)

    # unresolved_list = []
    # related_list = []
    # for id_ in df['id']:
    #     config_dict['url_seg'] = f"/rest/api/3/version/{id_}/unresolvedIssueCount"
    #     response = make_http_request("GET", **config_dict)
    #     resp_dict_unresolved = response.json()
    #     resp_dict_unresolved['id'] = id_
    #     unresolved_list.append(resp_dict_unresolved)
    #
    #     config_dict['url_seg'] = f"/rest/api/3/version/{id_}/relatedIssueCounts"
    #     response = make_http_request("GET", **config_dict)
    #     resp_dict_related = response.json()
    #     resp_dict_related['id'] = id_
    #     related_list.append(resp_dict_related)
    #
    # df_unresolved = pd.DataFrame(unresolved_list)
    # df_related = pd.DataFrame(related_list)
    #
    # df_related.drop(columns=['self'], inplace=True)
    # df_unresolved.drop(columns=['self'], inplace=True)
    #
    # df = df.merge(df_unresolved, on="id", how="inner")
    # df = df.merge(df_related, on="id", how="inner")

    return df


def update_parent_path(prjkey: str = "PLAT"):
    session = start_session(prjkey)
    issue_a = aliased(Issue)
    issue_b = aliased(Issue)

    query = update(Issue).values(
        parent_path=
        func.text2ltree(
            func.concat_ws('.', issue_b.parent_path, func.subpath(issue_a.parent_path, 1, 1))),
    ).where(issue_a.key == Issue.key) \
        .where(issue_a.isSubTask == False) \
        .where(~Issue.issuetype.in_(['Epic'])) \
        .where(issue_a.parent_key == issue_b.key)

    session.execute(query)

    #         session.query(Issue).update(
    #     {
    #         Issue.parent_path:
    #         func.text2ltree(func.concat_ws('.', issue_b.parent_path, func.subpath(issue_a.parent_path, 1, 1)))
    #     }, synchronize_session=False
    # ) \
    #     .filter(issue_a.parentkey == issue_b.key) \
    #     .filter(Issue.key == issue_a.key) \
    #     .filter(issue_a.isSubTask == False) \
    #     .filter(~issue_a.issuetype.in_('Epic')) \
    #
    # session.flush()
    # #
    # stmt = Issue. \
    #     filter(issue_a.parentkey == issue_b.key) \
    #     .filter(Issue.key == issue_a.key) \
    #     .filter(issue_a.isSubTask == True) \
    #     .filter(func.nlevel(issue_a.parent_path)) \
    #     .update({Issue.parent_path:
    #                  func.text2ltree(
    #                      func.concat_ws('.', issue_b.parent_path, func.subpath(issue_a.parent_path, 1, 1)))})
    # session.execute(stmt)
    session.commit()


def fill_null_values(value):
    dtype = value.dtype
    fill_value = ''

    # Handle string dtype
    if dtype in ['int', 'float']:
        fill_value = 0

    return value.fillna(fill_value)


def custom_sort_key(value):
    match = re.match(r'([A-Z]+)-(\d+)', value)
    if match:
        prefix = match.group(1)
        number = int(match.group(2))
        return (prefix, number)
    return value


def parse_comment_data(comment_data, issue_id: int, issue_key: str):
    data = [
        {
            "id": int(comment["id"]),
            "author": comment["author"]["accountId"],
            "body": comment["body"],
            "renderedBody": comment["renderedBody"],
            "updateAuthor": comment["updateAuthor"]["accountId"],
            "created": comment["created"],
            "updated": comment["updated"],
            "jsdPublic": comment["jsdPublic"],
            "issue_id": issue_id,
            "issue_key": issue_key
        }
        for comment in comment_data
    ]
    df = pd.DataFrame(data)
    return df


async def main(
        project_key: str, initial_load: bool = False,
        scope='project'
) -> None:
    config_dict = get_env_variables()
    with start_session(project_key) as pg_session:
        stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Issue")
        try:
            local_datetime = pg_session.execute(stmt).one()
        except sqlalchemy.exc.NoResultFound:
            config_dict['url_seg'] = "/rest/api/3/myself"
            response = make_http_request("GET", **config_dict)
            response.raise_for_status()
            time_zone = ZoneInfo(response.json()['timeZone'])
            # Create a datetime object with the specified date, time, and timezone
            local_datetime = datetime(2000, 1, 1, 0, 0, 0, tzinfo=time_zone)

        payload_dict = {}
        if scope == "project":
            if initial_load:
                jql = f"""
                    project={project_key.upper()} 
                    and created > '{local_datetime.strftime('%Y-%m-%d %H:%M')}' 
                    order by created asc
                """
            else:
                jql = f"project = {project_key.upper()} and updated > '{local_datetime.strftime('%Y-%m-%d %H:%M')}' order by updated asc"
        else:
            jql = None

        with open("../dags/data_pipeline/field_list.json", "rb") as fp:
            file_content = fp.read()
        fields: list = orjson.loads(file_content)['fields']

        async with aiohttp.ClientSession() as http_session:
            issue_start_at = 0
            issue_total_records = 100000
            fields = fields
            payload_dict['fields'] = fields
            payload_dict['maxResults'] = 100
            loop_count = 0
            cummulative_time = 0
            max_comments = 0
            while issue_start_at < issue_total_records:
                start_time = time.time_ns()
                response = await fetch_issues(
                    http_session, jql, fields,
                    headers=config_dict['headers'],
                    start_at=issue_start_at
                )
                issue_total_records = response['total']
                if issue_total_records == 0:
                    break

                issue_start_at += len(response['issues'])

                df_issue = pd.DataFrame(response['issues'])

                # Extract description in markdown format from renderedFields
                # then drop the column renderedFields

                df_issue['description_markdown'] = df_issue['renderedFields'].apply(
                    lambda x: x.get('description', '') if isinstance(x, dict) else None
                )
                df_issue.drop(columns=["renderedFields"], inplace=True)

                for key in ['worklog', 'comment']:
                    df_issue[key] = df_issue['fields'].apply(lambda x: x.get(key))
                    df_issue['fields'] = df_issue['fields'].apply(lambda x: {k: v for k, v in x.items() if k != key})

                df_changelog = df_issue[["id", "key", 'changelog']]
                df_changelog = df_changelog.join(
                    pd.json_normalize(df_changelog.changelog)
                ).drop(columns=["changelog"])

                changelog_keys: list = (
                    df_changelog.query("total > maxResults")
                    [['key', 'id']].to_dict(orient='records')
                )

                if len(changelog_keys) > 0:
                    # Send these to async process
                    async def fetch_changelog_data(issue: Dict[str, str]):
                        issue_key = issue['key']
                        issue_id = issue['id']

                        async with aiohttp.ClientSession() as session:
                            changelog = await fetch_changelog(
                                session, config_dict['headers'], issue_key,
                                params={'startAt': 20}
                            )
                            return {"issue_key": issue_key, "issue_id": issue_id, "changelog": changelog}

                    tasks_changelog = [fetch_changelog_data(issue_key) for issue_key in changelog_keys]
                    changelog_results = await asyncio.gather(*tasks_changelog)
                    df = pd.DataFrame(changelog_results)
                    df.drop(
                        columns=["self", "nextPage", "isLast"],
                        inplace=True
                    )
                    df.rename(
                        columns={'issue_key': 'key', 'issue_id': 'id', 'values': 'histories'},
                        inplace=True
                    )
                    df_changelog = pd.concat([df_changelog, df])

                df_changelog = (
                    df_changelog.explode("histories")
                    .rename(columns={"id": "issue_id", "key": "issue_key"})
                )
                changelog_json_df = (
                    df_changelog.join(
                        pd.json_normalize(df_changelog['histories'])
                        [["id", "created", "author.accountId", "items"]]
                    )
                    .drop(columns=["histories", "startAt", "maxResults", "total"])
                )
                changelog_json_df.rename(columns={"author.accountId": "author"}, inplace=True)

                upsert(
                    pg_session, ChangelogJSON, changelog_json_df,
                    primary_key="id", on_conflict_update=False
                )

                # Due to the data being sent by JIRA is items field
                # it is not possible to create a meaningful relation table.
                # park changelog
                # changelog_df = changelog_json_df.explode(["items"])
                # changelog_df = changelog_df.join(
                #     pd.json_normalize(changelog_df["items"])
                # ).drop(columns=["items"])
                # changelog_df.rename(columns={"from": "from_"}, inplace=True)
                # debug_dataframe(changelog_df, "changelog_df.xlsx")
                # upsert(
                #     pg_session, ChangeLog, changelog_df,
                #     primary_key="id", on_conflict_update=False
                # )

                df_worklog = df_issue[['worklog', "id", "key"]].copy()
                df_worklog = df_worklog.join(
                    pd.json_normalize(df_worklog.worklog)
                ).drop(columns=["worklog"])

                df_worklog.query("total > 0", inplace=True)
                if df_worklog.shape[0] > 0:
                    worklog_keys: list = df_worklog.query("total > maxResults")[['key', 'id']].to_dict(orient='records')
                    df_worklog.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
                    df_worklog.drop(columns=["startAt", "maxResults", "total"], inplace=True)
                    df_worklog = df_worklog.explode("worklogs").reset_index(drop=True)

                    if len(worklog_keys) > 0:
                        # send the keys to async process
                        async def fetch_worklog_data(issue: Dict[str, str]):
                            issue_key = issue['key']
                            issue_id = issue['id']

                            async with aiohttp.ClientSession() as session:
                                worklog = await fetch_worklog(session, config_dict['headers'], issue_key)
                                return {"issue_key": issue_key, "issue_id": issue_id, "worklogs": worklog}

                        tasks_worklog = [fetch_worklog_data(issue_key) for issue_key in worklog_keys]
                        worklog_results = await asyncio.gather(*tasks_worklog)
                        df = pd.DataFrame(worklog_results).explode("worklogs").reset_index(drop=True)
                        df_worklog = pd.concat([df_worklog, df])

                    df_worklog = df_worklog.join(
                        pd.json_normalize(df_worklog['worklogs'])
                    ).drop(columns=["worklogs"])
                    drop_columns_prefixes = [
                        "self", "issueId", "author.", "updateAuthor."
                    ]
                    drop_column_exception = {
                        "author.accountId", "updateAuthor.accountId",
                    }
                    df_worklog.drop(
                        columns=[
                            col for col in df_worklog.columns if any(map(col.startswith, drop_columns_prefixes))
                                                                 and col not in drop_column_exception
                        ], inplace=True
                    )

                    if 'comment.version' in df_worklog.columns:
                        df_worklog.loc[pd.notnull(df_worklog['comment.version']), 'comment'] = df_worklog.loc[
                            pd.notnull(df_worklog['comment.version'])].apply(
                            lambda x: {
                                "version": x["comment.version"],
                                "type": x["comment.type"],
                                "content": x["comment.content"]
                            },
                            axis=1
                        )
                        df_worklog['comment'] = df_worklog['comment'].apply(lambda x: x if pd.notnull(x) else None)

                        df_worklog.drop(
                            columns=["comment.version", "comment.type", "comment.content"],
                            inplace=True
                        )

                    df_worklog.rename(
                        columns={
                            "author.accountId": "author",
                            "updateAuthor.accountId": "updateauthor"
                        }, inplace=True
                    )

                    upsert(
                        pg_session, WorkLog, df_worklog, primary_key="id",
                        no_update_cols=("timeSpentHours",)
                    )

                df_comment = df_issue[['comment', "id", "key"]].copy()

                df_comment = df_comment.join(
                    pd.json_normalize(df_comment.comment)
                ).drop(columns=["comment", "self"])
                df_comment.query("total > 0", inplace=True)
                max_comments = max(max_comments, df_comment["total"].max())

                comment_keys: list = df_comment.query("total > maxResults")[['key', 'id']].to_dict(orient='records')
                if len(comment_keys) > 0:
                    # Send the keys to async proces
                    async def fetch_comments_data(issue: Dict[str, str]):
                        issue_key = issue['key']
                        issue_id = issue['id']

                        async with aiohttp.ClientSession() as session:
                            comments = await fetch_comments(session, config_dict['headers'], issue_key)
                            return {"issue_key": issue_key, "issue_id": issue_id, "comments": comments}

                    tasks_comment = [fetch_comments_data(issue_key) for issue_key in comment_keys]
                    comment_results = await asyncio.gather(*tasks_comment)
                    df = pd.DataFrame(comment_results)
                    df = df.explode(column="comments").reset_index()

                    # pending intergration

                if df_comment.shape[0] > 0:
                    df_comment.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
                    df_comment = df_comment.explode(column="comments").reset_index()

                    df_comment = df_comment.join(
                        pd.json_normalize(df_comment["comments"])
                        [
                            [
                                "id", "author.accountId", "updateAuthor.accountId",
                                "created", "updated", "jsdPublic", "body.version", "body.type", "body.content"
                            ]
                        ]
                    ).drop(columns=["comments", "maxResults", "total", "startAt"])

                    df_comment["body"] = df_comment.apply(
                        lambda x: {
                            "version": x.get("body.version", 1),
                            "type": x.get("body.type", "doc"),
                            "content": x.get("body.content", [])
                        }, axis=1
                    )
                    df_comment.drop(columns=["body.version", "body.type", "body.content"], inplace=True)

                    df_comment.rename(
                        columns={"author.accountId": "author", "updateAuthor.accountId": "updateAuthor"},
                        inplace=True
                    )

                    upsert(pg_session, IssueComments, df_comment, primary_key="id")

                df_issue.drop(columns=[
                    'changelog', 'expand', 'self', 'worklog', 'comment'
                ], inplace=True
                )

                df = df_issue.join(pd.json_normalize(df_issue.fields)).drop(columns=["fields"])
                if 'issuelinks' in df.columns:
                    print(f"found issuelinks")
                    try:
                        df_issue_links = df[['issuelinks', "id", "key"]].copy()
                        df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
                        df.drop(columns=['issuelinks'], inplace=True)
                    except Exception as e:
                        print(e)
                        raise e
                    # Drop rows with empty issuelinks
                    df_issue_links = df_issue_links[df_issue_links['issuelinks'].apply(lambda x: len(x) > 0)]

                    if not df_issue_links.empty:

                        df_issue_links = df_issue_links.explode("issuelinks")
                        df_issue_links = pd.concat(
                            [
                                df_issue_links.drop(columns="issuelinks").reset_index(),
                                pd.json_normalize(
                                    df_issue_links['issuelinks'],
                                    max_level=0
                                ).drop(columns="self")
                            ], axis=1
                        )
                        for col in ['outwardIssue', 'inwardIssue']:
                            if col in df_issue_links.columns:
                                df_issue_links = df_issue_links.join(
                                    pd.json_normalize(
                                        df_issue_links[col],
                                    )[['id', 'key']].rename(
                                        columns={"id": f"{col}_id", "key": f"{col}_key"}
                                    )
                                ).drop(columns=col)

                            upsert(pg_session, IssueLinks, df_issue_links, primary_key="id")

                df = df.explode('customfield_10020')
                df['customfield_10020'] = df['customfield_10020'].apply(lambda x: x if isinstance(x, dict) else {})

                df = df.join(
                    pd.json_normalize(df.customfield_10020).rename(columns=lambda x: f'customfield_10020.{x}'),
                ).drop(columns=["customfield_10020"])
                df.dropna(how='all', axis=1, inplace=True)

                for col in [
                    'components', 'fixVersions', 'versions',
                ]:
                    if col in df.columns:
                        df[col] = df[col].apply(
                            lambda x: [value['name'] for value in x] if isinstance(x, list) else None)

                # Define a condition to check if 'description.type' is not NaN
                if 'description.type' in df.columns:
                    condition = df['description.type'].notna()

                    df.loc[condition, 'description'] = df.loc[condition].apply(
                        lambda row:
                        {
                            'type': row['description.type'],
                            "version": row['description.version'],
                            "content": row['description.content']
                        },
                        axis=1
                    )
                # df.drop(
                #     columns=[
                #         'issuetype.self', 'issuetype.id', 'issuetype.description', 'issuetype.iconUrl',
                #         'issuetype.avatarId',
                #         'reporter.self', 'reporter.emailAddress', 'reporter.avatarUrls.48x48',
                #         'reporter.avatarUrls.24x24', 'reporter.avatarUrls.16x16', 'reporter.avatarUrls.32x32',
                #         'reporter.displayName', 'reporter.active', 'reporter.timeZone', 'reporter.accountType',
                #         'assignee.self', 'assignee.emailAddress', 'assignee.avatarUrls.48x48',
                #         'assignee.avatarUrls.24x24', 'assignee.avatarUrls.16x16', 'assignee.avatarUrls.32x32',
                #         'assignee.displayName', 'assignee.active', 'assignee.timeZone', 'assignee.accountType',
                #         'resolution.self', 'resolution.id', 'resolution.description',
                #
                #     ],
                #     inplace=True
                # )
                # for col in [
                #     "customfield_10071", 'customfield_10078', "customfield_10056", 'customfield_10179',
                #     "customfield_10006", "customfield_10049", 'customfield_10146', "customfield_10092"
                # ]:
                #     for col_extension in ['self', 'id']:
                #         if f"{col}.{col_extension}" in df.columns:
                #             df.drop(
                #                 columns=[f"{col}.{col_extension}"],
                #                 inplace=True
                #             )
                # for col in ['priority', 'status']:
                #     for col_extension in ['self', 'iconUrl', 'id']:
                #         if f"{col}.{col_extension}" in df.columns:
                #             df.drop(
                #                 columns=[f"{col}.{col_extension}"],
                #                 inplace=True
                #             )

                drop_columns_prefixes = [
                    'parent.',
                    'customfield_10001',
                    'worklog.',
                    'comment.comments',
                    'description.', 'assignee.', 'customfield_10006.',
                    'customfield_10049.', 'reporter.',
                    "customfield_10056.",
                    "customfield_10071.",
                    "customfield_10078.",
                    'customfield_10146.',
                    "customfield_10179.", "issuetype.", "priority.",
                    "resolution.", 'status.'
                ]
                exceptions = {
                    'parent.name', 'assignee.accountId', 'customfield_10006.value',
                    'customfield_10049.value', 'reporter.accountId',
                    "customfield_10056.value",
                    "customfield_10071.value",
                    "customfield_10078.value",
                    'customfield_10146.value',
                    "customfield_10179.value", 'issuetype.name', 'issuetype.subtask', 'issuetype.hierarchyLevel',
                    'priority.name',
                    'resolution.name', 'status.name', 'status.statusCategory.name',
                }

                df.drop(
                    columns=[
                        col for col in df.columns if any(map(col.startswith, drop_columns_prefixes))
                                                     and col not in exceptions
                    ],

                    inplace=True
                )

                col_prefix_rename = [
                    'aggregateprogress.'
                ]

                column_rename_map = {
                    'aggregateprogress.percent': 'aggregateprogress_percent',
                    'aggregateprogress.progress': 'aggregateprogress_progress',
                    'aggregateprogress.total': 'aggregateprogress_total',
                    'progress.percent': 'progress_percent',
                    'progress.progress': 'progress_progress',
                    'progress.total': 'progress_total',
                    'assignee.accountId': 'assignee', 'reporter.accountId': "reporter",
                    "customfield_10006.value": "change_risk",
                    'customfield_10049.value': "severity",
                    'customfield_10015': 'startdate',
                    'customfield_10019': 'Rank',

                    "customfield_10056.value": "category_type",
                    "customfield_10071.value": "initiated_by",
                    "customfield_10078.value": 'approvalstatus',
                    'customfield_10146.value': 'reqfinalized',
                    "customfield_10179.value": "qc_check",
                    'customfield_10059': 'testcaseno',
                    'customfield_10060': 'testcasesuite',
                    'customfield_10061': 'teststepno',
                    'customfield_10062': 'scenariono',
                    'customfield_10067': 'ClientJira',
                    'customfield_10123': 'adeffort',
                    "customfield_10147": "reopen_count",
                    "customfield_10256": "initiative_detail",
                    'issuetype.name': 'issuetype', 'priority.name': 'priority',
                    'resolution.name': "resolution", 'status.name': "status",
                    'issuetype.subtask': 'isSubTask',
                    'status.statusCategory.name': 'statusCategory'

                }

                # Update specific mappings if needed
                column_rename_map.update({
                    'customfield_10001.name': 'Team',
                    'issuetype.hierarchyLevel': 'issue_hierarchy_level'
                })

                df.rename(columns=column_rename_map, inplace=True)

                # list_of_columns = sorted(set(df.columns))
                # print(list_of_columns)
                upsert(pg_session, Issue, df, primary_key="id", no_update_cols=("tscv_summary_description",))
                total_time = time.time_ns() - start_time
                cummulative_time += total_time
                loop_count += 1
                update_progress(
                    start=issue_start_at, total=issue_total_records, time_taken=total_time * pow(10, -9),
                    cummulative_time=cummulative_time,
                    average_time=(cummulative_time * pow(10, -9)) / loop_count
                )
                if loop_count > 10:
                    break
            print(f"max comments found: {max_comments}")
        pg_session.commit()


def replace_none_with_null(item):
    if isinstance(item, dict):
        return {k: ('null' if v is None else v) for k, v in item.items()}
    elif isinstance(item, list):
        return [replace_none_with_null(i) for i in item]
    return item


async def test_db(project_key: str = 'plat'):
    async with Database(schema=project_key).async_session() as postgres_session:
        stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Issue")
        try:
            result = await postgres_session.execute(stmt)
            local_datetime = result.scalar_one()
            print(local_datetime)
        except sqlalchemy.exc.NoResultFound:
            time_zone = ZoneInfo('UTC')
            # Create a datetime object with the specified date, time, and timezone
            local_datetime = datetime(2024, 7, 1, 0, minute=0, second=0, tzinfo=time_zone)
    print(local_datetime)


def get_issue_type_for_project(project_id: int, **kwargs):
    kwargs['payload'] = {'projectId': project_id}
    response = make_http_request_custom("GET", **kwargs)
    response_json = [{key: value for key, value in d.items() if key in ['name', 'subtask', 'hierarchyLevel']} for d in
                     response.json()]
    print(response_json)


def make_http_request_custom(request_method: str, **kwargs) -> requests.Response:
    response = requests.request(
        request_method,
        url=f'https://api.atlassian.com/ex/jira/{kwargs["cloudId"]}/rest/api/3/issuetype/project',
        headers=kwargs['headers'],
        auth=kwargs['auth'],
        params=kwargs['payload']
    )
    return response


def get_jira_user_list(request_method, **kwargs) -> pd.DataFrame:
    response = make_http_request(request_method, **kwargs)
    response.raise_for_status()
    if response.status_code == 200:
        response_json = [{key: value for key, value in d.items() if key not in ['avatarUrls', 'self']} for d in
                         response.json()]
        return pd.DataFrame(response_json)
    else:
        print("error")

    return pd.DataFrame(None)


def init_jira_user_list(schema: str = 'public'):
    config_dict = get_env_variables()
    config_dict['url_seg'] = "/rest/api/3/users"
    start = 0
    size = 1000
    with Database(schema=schema).session() as pg_session:
        while True:
            config_dict['payload'] = {'startAt': start, 'maxResults': size}
            df = get_jira_user_list("GET", **config_dict)

            if df.shape[0] > 0:
                df.where(pd.notnull(df), other=None, inplace=True)
                # df['timeZone'] = df['timeZone'].fillna('UTC')
                # df['locale'] = df['locale'].fillna('')
                nan_rows = df[df['emailAddress'].isnull()]

                # df = df[~(df['emailAddress'].isna())]
                upsert(
                    pg_session, User, df, 'accountId',
                    no_update_cols=('otp_secret', 'otp_secret_added_on'),
                    on_conflict_update=False
                )

            if df.shape[0] < size:
                break

            start += size

        pg_session.commit()


async def produce_stats(stats_queue: Queue):
    none_count = 0
    records_processed = 0
    time_consumed = 0
    elapsed_time = 0

    # Create a dedicated console instance for stats processing
    # stats_console = Console(log_path=False, log_time=True, log_time_format="%Y%m%d %H:%M:%S.%f")

    # Initialize Progress bar with custom columns
    progress = Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        "[progress.percentage]{task.percentage:>3.0f}%",
        "•",
        TextColumn("Processed: {task.completed} / {task.total}"),
        TimeElapsedColumn(),
        refresh_per_second=2,
        console=main_console
    )

    task = None

    try:
        with progress:

            while True:
                result = await stats_queue.get()

                if task is None:
                    # Create a progress task the first time we have data
                    task = progress.add_task(
                        "Processing Records",
                        total=result['total']
                    )

                # Update the progress with the new record count
                records_processed += result['record_count']
                progress.update(task, advance=result['record_count'])

                time_consumed += result['process_time'] * pow(10, -9)
                elapsed_time += result['elapsed_time'] * pow(10, -9)

                if result.get('isLast', False):
                    none_count += 1

                if none_count == 10:
                    stats_queue.task_done()
                    break
                stats_queue.task_done()

                await asyncio.sleep(0.5)
    except Exception as e:
        handle_exception(e)

if __name__ == "__main__":
    # for _key in ['plp', 'plat', 'acq', 'ccp', 'cpp']:
    for _key in ['plat']:
        st_time = time.time_ns()
        asyncio.run(process_jira_versions(project_key=_key))
        sys.stdout.write("\033[1A\033[2K")
        sys.stdout.write(f"\nprocess_jira_versions Total Time: {(time.time_ns() - st_time) * pow(10, -9):.2f} secs")

        st_time = time.time_ns()
        asyncio.run(process_jira_issues(
            project_key='plat', scope="project", initial_load=True)
        )
        sys.stdout.write("\033[1A\033[2K")
        sys.stdout.write(f"\nprocess_jira_issues Total Time: {(time.time_ns() - st_time) * pow(10, -9):.2f} secs")

        # print("process_jira_issues done!")
        st_time = time.time_ns()
        upsert_issue_classification(_key, batch_size=50_000)

        sys.stdout.write("\033[1A\033[2K")
        sys.stdout.write(
            f"\nupsert_issue_classification Total Time: {(time.time_ns() - st_time) * pow(10, -9):.2f} secs")

        st_time = time.time_ns()
        del_deleted_worklog('plat')
        sys.stdout.write("\033[1A\033[2K")
        sys.stdout.write(f"\ndel_deleted_worklog Total Time: {(time.time_ns() - st_time) * pow(10, -9):.2f} secs")
        start_time = time.time_ns()
        asyncio.run(get_sprint_details(project_key=_key))
        print(format_time_difference(time.time_ns() - start_time))
        # insert_sprint_details(_key)
        print("insert_sprint_details done!")


def add_to_db_all_jira_boards(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
):
    config_dict = get_env_variables()
    config_dict['url_seg'] = "/rest/agile/1.0/board/"
    config_dict['payload'] = {'startAt': 0, 'maxResults': 50}
    df = get_all_boards(**config_dict)
    df.where(pd.notnull(df), other=None, inplace=True)
    df.fillna(0, inplace=True)
    with DataframeDebugger() as debugger:
        debug_dataframe = debugger.debug_dataframe
        debug_dataframe(df, f"df_http_all_boards.xlsx")

    with Database(schema='public').session() as pg_session:
        upsert(pg_session, AllBoards, df, 'id')
        pg_session.commit()


def get_all_boards(**kwargs):
    resp_list = []
    start_at = 0
    max_results = 50
    while True:
        response = make_http_request("GET", **kwargs)
        response.raise_for_status()
        data = response.json()
        values = data.get("values", [])

        resp_list.extend(
            [
                {
                    k: (
                        0 if isinstance(v, (int, float)) and pd.isnull(v) else
                        ('' if isinstance(v, str) and pd.isnull(v) else
                         (v if k != "location" else {i: j for i, j in v.items() if
                                                     i not in ['self', 'avatarURI', 'name']}))
                    )
                    for k, v in item.items() if k not in ['self', 'avatarURI']
                }
                for item in values
            ]
        )

        if data.get("isLast", True):
            break

        start_at += max_results
    df = pd.DataFrame(resp_list)
    # Extract the location column to a list of dictionaries
    location_list = df['location'].tolist()

    # Remove the location column from the DataFrame and normalize it
    location_df = pd.json_normalize(location_list)
    df = df.drop(columns=['location'])
    df = pd.concat([df, location_df], axis=1)

    return df


def insert_sprint_details(
        project_key: str,
):
    with Database(schema=project_key).session() as pg_session:
        config_dict = get_env_variables()
        payload_dict = {'maxResults': 50, 'startAt': 0}

        result = pg_session.query(AllBoards).filter(
            (AllBoards.projectKey.in_([project_key.upper()])) & (AllBoards.type.in_(['scrum']))
        ).all()

        for board in result:

            config_dict['url_seg'] = f"/rest/agile/1.0/board/{board.id}/sprint"
            sprint_list = []
            while True:
                config_dict['data'] = json.dumps(payload_dict)
                response = make_http_request("GET", **config_dict)
                sprint_list.extend(response.json()["values"])
                payload_dict['startAt'] += 50
                if json.dumps(response.json()['isLast']):
                    break

            df = pd.DataFrame(sprint_list)
            _row, _ = df.shape
            # If  no rows returned, then check next board_id proceed further
            if _row == 0:
                print(f'{project_key}: No rows found for Sprints. Skipping')
                continue

            if 'self' in df.columns:
                df.drop(columns=['self'], inplace=True)
            df.rename(columns={
                'id': 'sprint_id',
                'startDate': 'start_date',
                'endDate': 'end_date',
                'completeDate': 'complete_date',
                'originBoardId': 'origin_board_id',
                'createdDate': 'created_date'
            }, inplace=True)

            for col in ['complete_date', 'start_date', 'end_date']:
                if col in df.columns:
                    df[col] = df[col].replace(np.nan, None)

            upsert(pg_session, SprintOld, df, 'sprint_id')
            pg_session.commit()

            config_dict['url_seg'] = f"/rest/greenhopper/1.0/rapid/charts/velocity"
            config_dict['payload'] = {'rapidViewId': board.id}
            response = make_http_request("GET", **config_dict)

            velocity_list = []
            for key, value in response.json()['velocityStatEntries'].items():
                sprint_velocity = [
                    int(key), value['estimated']['value'], value['completed']['value'],
                    value['allConsideredIssueKeys']
                ]

                estimated_list = []
                completed_list = []

                for j in value['estimatedEntries']:
                    estimated_list = [v for k, v in j.items() if k in 'issueKey']
                sprint_velocity.append(estimated_list)
                for i in value['completedEntries']:
                    completed_list = [v for k, v in i.items() if k in 'issueKey']
                sprint_velocity.append(completed_list)
                velocity_list.append(sprint_velocity)
            df1 = pd.DataFrame(velocity_list, columns=[
                'sprint_id', 'estimated_velocity', 'completed_velocity', 'all_considered_issue_keys',
                'estimated_entries', 'completed_entries'
            ])
            df1['origin_board_id'] = board.id

            df_result = pd.merge(df, df1, how='left', left_on=["sprint_id", "origin_board_id"],
                                 right_on=["sprint_id", "origin_board_id"])

            # Drop boards causing integrity issues
            # if project == 'plat':
            #     for board_id in [28, 29, 34]:
            #         df_result = df_result[~(df_result['origin_board_id'] == board_id)].copy(deep=True)
            #         df1 = df1[~(df1['origin_board_id'] == board_id)].copy(deep=True)
            # elif project == 'ccp':
            #     for board_id in [4, 29]:
            #         df_result = df_result[~(df_result['origin_board_id'] == board_id)].copy(deep=True)
            #         df1 = df1[~(df1['origin_board_id'] == board_id)].copy(deep=True)

            df_result.replace({np.nan: None}, inplace=True)

            upsert(pg_session, SprintOld, df1, 'sprint_id',
                   no_update_cols=('state', 'name', 'start_date', 'end_date', 'complete_date'))
            pg_session.commit()


def del_deleted_worklog(project_key: str = "PLAT"):
    config_dict = get_env_variables()

    config_dict['url_seg'] = "/rest/api/3/myself"
    response = make_http_request("GET", **config_dict)
    response.raise_for_status()
    time_zone = ZoneInfo(response.json()['timeZone'])
    with Database(schema=project_key).session() as pg_session:
        stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Worklog")
        try:
            result = pg_session.execute(stmt)
            local_datetime = result.scalar_one()
            my_logger.debug(f"local datetime returned: {local_datetime}")
        except KeyboardInterrupt as e:
            my_logger.warning(f"User cancelled: {e}")
            exit(0)
        except sqlalchemy.exc.NoResultFound:
            # Create a datetime object with the specified date, time, and timezone
            local_datetime = datetime(
                year=2010, month=1, day=1, hour=0, minute=0, second=0, tzinfo=time_zone
            )
            my_logger.debug(f"no rows found in {RunDetailsJira.__table__}. default time: {local_datetime}")
        except Exception as e:
            handle_exception(e)

        payload_dict = {}
        _value = 0
        # Use current date to update the next fetch start time
        curr_date = datetime.now() - relativedelta(hours=1)
        # for row in _session.execute(select(run_details)):
        #     _topic, _last_run = row
        #     if _topic == "Worklog":
        #         _value = int((_last_run - datetime(1970, 1, 1)).total_seconds())
        #         break
        _value = int(
            (local_datetime - datetime(1970, 1, 1, tzinfo=time_zone)
             ).total_seconds() * 1000
        )

        last_page = False
        config_dict['url_seg'] = "/rest/api/3/worklog/deleted"
        payload_dict['since'] = _value
        config_dict['payload'] = payload_dict
        wkid_list = []
        page_number = 0
        local_datetime = datetime.now(tz=time_zone)
        while not last_page:
            my_logger.debug(f"page number {(page_number := page_number + 1)}")
            res = make_http_request("GET", **config_dict)
            last_page = res.json()['lastPage']
            df = pd.json_normalize(res.json()['values'])
            if df.shape[0] > 0:
                wkid_list.extend(df['worklogId'].tolist())
            # stmt = WorkLog.__table__.delete().where(WorkLog.id.in_(df['worklogId'].tolist()))

            # wkid = [{key: value for key, value in i.items() if key == "worklogId"} for i in res.json()["values"]]
            # wkid_list.extend([item for sublist in [list(item.values()) for item in wkid] for item in sublist])
            # urlparse returns scheme, netloc, path, params, query, fragment
            config_dict["payload"] = urlparse(res.json()['nextPage']).query if not last_page else None
        my_logger.debug(f"count of deleted workitems: {len(wkid_list)}")
        if len(wkid_list):
            stmt = WorkLog.__table__.delete().where(WorkLog.id.in_(wkid_list))
            pg_session.execute(stmt)

        run_details_jira = RunDetailsJira(topic="Worklog", last_run=local_datetime)
        pg_session.merge(run_details_jira)
        pg_session.commit()


class Database:
    def __init__(self, schema: str, title: str = "JIRA_RW", rw: bool = True):
        self.schema = schema
        self.title = title
        self.rw = rw
        self._load_credentials()
        self._create_engine_async()
        self._create_engine()

    def _load_credentials(self):
        entry: EntryDetails = get_kp_entry_details(self.title)
        self.username = entry.username
        self.password = entry.password
        self.db_server_name = entry.custom_properties["DB_SERVER_NAME"]
        self.db_server_port = entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else entry.custom_properties[
            "DB_SERVER_RO_PORT"]
        self.db_name = entry.custom_properties["DB_NAME"]

        # keepass_db = os.getenv("AIRFLOW_HOME") + "/Database.kdbx"
        # keepass_key = os.getenv("AIRFLOW_HOME") + "/Database.key"
        #
        # if not keepass_db or not keepass_key:
        #     raise ValueError("Environment variables DATABASE_PATH and MASTER_KEYFILE must be set.")
        #
        # try:
        #     ref = PyKeePass(keepass_db, keyfile=keepass_key)
        # except Exception as e:
        #     raise RuntimeError(f"Failed to initialize Keepass reference: {e}")
        #
        # try:
        #     entry = ref.find_entries(title=self.title, first=True)
        #     if not entry:
        #         raise ValueError(f"No entry found with title {self.title}.")
        # except Exception as e:
        #     raise RuntimeError(f"Failed to find entry: {e}")
        #
        # self.username = entry.username
        # self.password = entry.password
        # self.db_server_name = entry.get_custom_property("DB_SERVER_NAME")
        # self.db_server_rw_port = entry.get_custom_property("DB_SERVER_RW_PORT")
        # self.db_name = entry.get_custom_property("DB_NAME")

    def _create_engine_async(self):
        self.DATABASE_URL_ASYNC = URL.create(
            drivername="postgresql+asyncpg",
            username=self.username,
            password=self.password,
            host=self.db_server_name,
            port=self.db_server_port,
            database=self.db_name
        )
        self.engine_async = create_async_engine(
            self.DATABASE_URL_ASYNC, echo=False,
            pool_size=10,  # Adjust pool size as needed
            max_overflow=20  # Adjust max overflow as needed
        ).execution_options(
            schema_translate_map={None: self.schema, 'SCHEMA': self.schema}
        )

    def _create_engine(self):
        self.DATABASE_URL = URL.create(
            drivername="postgresql+psycopg2",
            username=self.username,
            password=self.password,
            host=self.db_server_name,
            port=self.db_server_port,
            database=self.db_name
        )

        self.engine = create_engine(
            self.DATABASE_URL,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_pre_ping=True,
        ).execution_options(
            schema_translate_map={None: self.schema, 'SCHEMA': self.schema}
        )

    @contextmanager
    def session(self) -> ContextManager[Session]:
        session_maker = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False,
        )
        with session_maker() as session:
            try:
                yield session
                # session.commit()
            except Exception:
                session.rollback()
                raise
            finally:
                session.close()

    @asynccontextmanager
    async def async_session(self) -> AsyncContextManager[AsyncSession]:
        async_session = async_scoped_session(
            sessionmaker(
                bind=self.engine_async,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False
            ), scopefunc=current_task
        )
        async with async_session() as session:
            try:
                yield session
                # await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

            # Sample
            # async with async_session() as session:
            #     async with session.begin():
            #         session.add_all(
            #             [
            #                 A(bs=[B(), B()], data="a1"),
            #                 A(bs=[B()], data="a2"),
            #                 A(bs=[B(), B()], data="a3"),
            #             ]
            #         )


@smart_retry(min_wait=2, max_wait=5, max_retries=3)
@inject
def upsert_old(
        session: Session, model: Type[Base], rows: pd.DataFrame, primary_key: str,
        no_update_cols: tuple = (), on_conflict_update=True,
        conflict_condition=None,
        my_logger=Provide[LoggerContainer.logger]
):
    if rows.shape[0] == 0:
        return

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    # rows.where(pd.notnull(rows), other=None, inplace=True)
    # df = df.astype(object).where(pd.notnull(df), None)

    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983

    table = model.__table__

    # stmt = insert(table).values(rows.to_dict(orient='records'))

    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]

    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]

    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}

    if on_conflict_update:
        if conflict_condition:
            index_where = and_(*conflict_condition)
        else:
            index_where = None
        # Example
        # conflict_condition = [
        #     column('report_date') < column('excluded.report_date')
        # ]

        stmt = stmt.on_conflict_do_update(
            index_elements=primary_keys,
            set_=update_dict,
            # index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
            index_where=index_where
        )
        # .returning(column('xmax')))

    else:
        stmt = stmt.on_conflict_do_nothing(index_elements=primary_keys)
        # .returning(column('xmax')))

    seen = set()
    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    def handle_foreignkeys_constraints(row):
        # Handle foreign key constraints
        for c_name, c_value in foreign_keys.items():
            # print(c_name, c_value, c_value.table.name, row[c_name])
            foreign_obj = row.pop(c_value.table.name, None)
            # print(f"foreign_obj = {foreign_obj}")
            # Change code
            # row[c_name] = getattr(foreign_obj, c_value.name) if foreign_obj else None
            if foreign_obj:
                row[c_name] = getattr(foreign_obj, c_value.name)
            else:
                row[c_name] = row.get(c_name)

            # print(c_name, c_value, row[c_name])

        # Handle unique constraints
        for const in unique_constraints:
            unique = tuple([const, ] + [row[col.name] for col in const.columns])
            unique_key = tuple((col.name, row[col.name]) for col in const.columns)

            if unique_key in seen:
                return None
            seen.add(unique)
        return row

    rows = list(
        filter(None, (handle_foreignkeys_constraints(row) for row in rows.to_dict(orient='records')))
    )

    # q1 = stmt.cte('upsert_statement')
    # stmt = select(
    #     func.count().label('all_rows'),
    #     func.sum(
    #         case(
    #             (column('xmax') == 0, 1),
    #             else_=0
    #         )
    #     ).label('ins'),
    #     func.sum(case(
    #         (column('xmax').cast(TEXT).cast(Integer) > 1, 1),
    #         else_=0)
    #     ).label('upd')
    # ).select_from(q1)

    if os.getenv("DEBUG", "") == "dataframe_utils":
        my_logger.debug(stmt.compile(dialect=dialect(), compile_kwargs={"literal_binds": True}))

    try:
        session.execute(stmt, rows)

    except exc.DBAPIError as e:
        my_logger.debug(f"DBAPIError Error occurred in upsert... table name: {table}")
        # print("Exception Type:", type(e).__name__)
        # print(f"Exception Code: {e.code}")
        # print(f"Exception Message: {e.detail}")
        # print(f"Exception Origin: {e.orig}")
        # print(f"Exception Statement: {e.statement}")
        # print(f"SQL Parameters: {e.params}")
        # print("Compiled Query")
        # print(stmt.compile(dialect=dialect(), compile_kwargs={"literal_binds": True}))
        # print("Traceback:")
        exc_type, exc_value, exc_tb = sys.exc_info()
        # get the line number when exception occurred
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)

        my_logger.exception(
            f"Line {line_num}: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        raise e
    except exc.StatementError as e:
        my_logger.debug(f"StatementError occurred in upsert. table name: {table}")
        # print("Exception Type:", type(e).__name__)
        # print(f"Exception Code: {e.code}")
        # print(f"Exception Message: {e.detail}")
        # print(f"Exception Origin: {e.orig}")
        # print(f"Exception Statement: {e.statement}")
        # print(f"SQL Parameters: {e.params}")

        exc_type, exc_value, exc_tb = sys.exc_info()
        # get the line number when exception occurred
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)

        my_logger.exception(
            f"Line {line_num}: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        raise e
    except exc.CompileError as e:
        exc_type, exc_value, exc_tb = sys.exc_info()
        # get the line number when exception occurred
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)

        my_logger.exception(
            f"Line {line_num}: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        raise e
    except Exception as e:
        handle_exception(e)


@smart_retry(min_wait=2, max_wait=5, max_retries=3)
@inject
async def upsert_async_old(
        session: AsyncSession, model: Type[Base], rows: pd.DataFrame, primary_key: str,
        no_update_cols: tuple = (), on_conflict_update=True,
        conflict_condition=None,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    table = model.__table__
    upsert_stats: dict = {
        'table_name': table.name,
        'input_rows': rows.shape[0]
    }
    if rows.shape[0] == 0:
        return

    rows = rows.copy()
    rows.replace({np.nan: None}, inplace=True)
    # rows.where(pd.notnull(rows), other=None, inplace=True)

    stmt = insert(table)
    primary_keys = [key.name for key in inspect(table).primary_key]

    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]

    update_dict = {k: getattr(stmt.excluded, k) for k in update_cols}

    if on_conflict_update:
        index_where = and_(*conflict_condition) if conflict_condition else None
        # Example
        # conflict_condition = [
        #     column('report_date') < column('excluded.report_date')
        # ]

        stmt = (
            stmt.on_conflict_do_update(
                index_elements=primary_keys,
                set_=update_dict,
                # index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
                index_where=index_where
            )
            .returning(column('xmax'))
            # .returning(
            #     case((column('xmax') == 0, 1), else_=0).label('is_insert'),
            #     case((cast(column('xmax'), TEXT).cast(Integer) > 0, 1), else_=0).label('is_update')
            # )
        )
    else:
        stmt = (
            stmt.on_conflict_do_nothing(index_elements=primary_keys)
            .returning(column('xmax'))
            # .returning(
            #     case((column('xmax') == 0, 1), else_=0).label('is_insert'),
            #     case((cast(column('xmax'), TEXT).cast(Integer) > 0, 1), else_=0).label('is_update')
            # )
        )

    seen = set()
    foreign_keys = {col.name: list(col.foreign_keys)[0].column for col in table.columns if col.foreign_keys}
    unique_constraints = [c for c in table.constraints if isinstance(c, UniqueConstraint)]

    def handle_foreignkeys_constraints(row):
        # Handle foreign key constraints
        for c_name, c_value in foreign_keys.items():
            foreign_obj = row.pop(c_value.table.name, None)
            if foreign_obj:
                row[c_name] = getattr(foreign_obj, c_value.name)
            else:
                row[c_name] = row.get(c_name)

        # Handle unique constraints
        for const in unique_constraints:
            unique = tuple([const, ] + [row[col.name] for col in const.columns])
            unique_key = tuple((col.name, row[col.name]) for col in const.columns)

            if unique_key in seen:
                return None
            seen.add(unique)
        return row

    row_list: list = list(
        filter(None, (handle_foreignkeys_constraints(row) for row in rows.to_dict(orient='records')))
    )

    q1 = stmt.cte('upsert_statement')
    # Step 3: Create the SELECT query to count rows and detect inserts/updates
    stmt = select(
        func.count().label('all_rows'),
        func.sum(case((column('xmax') == 0, 1), else_=0)).label('ins'),
        func.sum(case((column('xmax').cast(TEXT).cast(Integer) > 1, 1), else_=0)).label('upd')
    ).select_from(q1)  # Reference the CTE for the SELECT query

    if os.getenv("DEBUG", "") == "dataframe_utils":
        my_logger.debug(stmt.compile(dialect=session.bind.dialect, compile_kwargs={"literal_binds": True}))
        my_logger.debug(f"{compile_query(stmt)}")

    # orm_stmt = select(model).from_statement(stmt).execution_options(populate_existing=True)
    # my_logger.dataframe_utils(f"{compile_query(orm_stmt)}")

    try:
        # Use await to execute the statement asynchronously
        # Timeout in seconds
        # timeout_options = {"timeout": 60}
        # output = await session.execute(stmt.execution_options(**timeout_options), rows)
        my_logger.debug(f"table {table.name}: parameter sets passed = {len(row_list)}")
        output: CursorResult = await session.execute(stmt, row_list)

        # output_orm = await session.execute(orm_stmt)
        # my_logger.dataframe_utils(f"output_orm = {output_orm.returns_rows}")

        upsert_stats['returns_rows'] = output.returns_rows

        if output.returns_rows:
            rows_returned = output.fetchall()
            my_logger.debug(f"{upsert_stats}")
            my_logger.debug(
                f"count = {rows_returned[0][0]}, inserted = {rows_returned[0][1]}, updated = {rows_returned[0][2]}"
            )
            # upsert_stats['total_rows'] = rows_returned[0][0]
            # inserted_count = sum(row['is_insert'] for row in rows_returned)
            # updated_count = sum(row['is_update'] for row in rows_returned)

            # Log or return counts as needed
            # my_logger.dataframe_utils(f"{table.name} Inserted: {inserted_count}, Updated: {updated_count}")
        else:
            my_logger.debug(f"{output.rowcount}")

        return upsert_stats
    except exc.DBAPIError as e:
        print(f"DBAPIError Error occurred in upsert... table name: {table}")
        print("Exception Type:", type(e).__name__)
        print(f"Exception Code: {e.code}")
        print(f"Exception Message: {e.detail}")
        print(f"Exception Origin: {e.orig}")
        print(f"Exception Statement: {e.statement}")
        print(f"SQL Parameters: {e.params}")
        print("Traceback:")
        exc_type, exc_value, exc_tb = sys.exc_info()
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        print(''.join(tb.format_exception_only()))

        raise e
    except exc.StatementError as e:
        print(f"StatementError occurred in upsert. table name: {table}")
        print("Exception Type:", type(e).__name__)
        print(f"Exception Code: {e.code}")
        print(f"Exception Message: {e.detail}")
        print(f"Exception Origin: {e.orig}")
        print(f"Exception Statement: {e.statement}")

        print(f"SQL Parameters: {e.params}")
        print("Traceback:")
        exc_type, exc_value, exc_tb = sys.exc_info()
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        print(''.join(tb.format_exception_only()))

    except Exception as e:
        my_logger.debug(f"Unhandled Exception occurred in upsert. table name: {table}")
        handle_exception(e)


@inject
def upsert_single_old(
        session: Session, model: Type[Base], rows: pd.DataFrame, primary_key: str, no_update_cols: tuple = (),
        on_conflict_update=True,
        conflict_condition=None,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    table = model.__table__
    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]

    rows = rows.copy(deep=True)
    # rows.where(pd.notnull(rows), other=None, inplace=True)
    rows.replace({np.nan: None}, inplace=True)

    for index, row in rows.iterrows():
        row_dict: Dict[str, Union[float, None, str, Any]] = row.to_dict()
        # Handle nan values by converting them to None
        row_dict = {k: (None if (isinstance(v, float) and math.isnan(v)) else v) for k, v in row_dict.items()}
        # row_dict['created'] = row_dict['created'].isoformat()
        # Check if 'items' key exists before processing
        if 'items' in row_dict:
            if isinstance(row_dict['items'], (dict, list)):
                row_dict['items'] = json.dumps(row_dict['items'])
            else:
                # Handle cases where 'items' is not a dict or list
                row_dict['items'] = None

        stmt = insert(table).values(row_dict)

        if on_conflict_update:
            index_where = and_(*conflict_condition) if conflict_condition else None

            # Example
            # conflict_condition = [
            #     column('report_date') < column('excluded.report_date')
            # ]

            on_conflict_stmt = stmt.on_conflict_do_update(
                index_elements=table.primary_key.columns,
                set_={k: getattr(stmt.excluded, k) for k in update_cols},
                # index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
                index_where=index_where
            )
        else:
            on_conflict_stmt = stmt.on_conflict_do_nothing(index_elements=table.primary_key.columns)

        try:
            # compiled = stmt.compile(dialect=dialect(), compile_kwargs={"literal_binds": True})
            # print(str(compiled))
            if session is None:
                raise ValueError("Session is None. Ensure the session is properly initialized.")
            session.execute(on_conflict_stmt)
        except exc.CompileError as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            # get the line number when exception occurred
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)

            my_logger.exception(
                f"Line {line_num}: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            my_logger.debug(f"index = {index}")
            my_logger.debug(f"row")
            my_logger.debug(f"{row}")
            raise e
        except Exception as e:
            my_logger.debug(f"index = {index}")
            my_logger.debug(f"row")
            my_logger.debug(f"{row}")
            handle_exception(e)


def smart_retry_old(min_wait=5, max_wait=10, max_delay=60000):
    def decorator(func_local):
        @functools.wraps(func_local)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            async def async_retry_logic():
                while True:
                    try:
                        return await func_local(*args, **kwargs)  # Await for async functions
                    except Exception as e:
                        elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                        if elapsed_time >= max_delay:
                            raise TimeoutError(f"Retry limit of {max_delay} milliseconds reached.") from e

                        wait_time = random.uniform(min_wait, max_wait)
                        print(f"Retrying in {wait_time:.2f} seconds due to {e}...")
                        await asyncio.sleep(wait_time)

            def sync_retry_logic():
                while True:
                    try:
                        return func_local(*args, **kwargs)  # Direct call for sync functions
                    except Exception as e:
                        elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                        if elapsed_time >= max_delay:
                            raise TimeoutError(f"Retry limit of {max_delay} milliseconds reached.") from e

                        wait_time = random.uniform(min_wait, max_wait)
                        print(f"Retrying in {wait_time:.2f} seconds due to {e}...")
                        time.sleep(wait_time)

            if asyncio.iscoroutinefunction(func_local):
                return async_retry_logic()
            else:
                return sync_retry_logic()

        return wrapper

    return decorator


@inject
def make_http_request(
        request_method: str, my_logger: Logger = Provide[LoggerContainer.logger], **kwargs,
) -> requests.Response:
    try:
        response = requests.request(
            request_method,
            url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
            headers=kwargs['headers'],
            # auth=kwargs['auth'],
            params=kwargs['payload']
        )
        response.raise_for_status()
        if "X-Ratelimit-Limit" in response.headers:
            my_logger.warning(
                response.headers["X-Ratelimit-Limit"],
                response.headers["X-Ratelimit-Remaining"]
            )
        return response
    except requests.exceptions.ConnectionError as errc:
        my_logger.error(f'Connection error: {errc}')
        raise errc
    except requests.exceptions.HTTPError as errh:
        my_logger.error(f'http error: {errh}')
        raise errh
    except requests.exceptions.Timeout as errt:
        my_logger.error(f'Timeout: {errt}')
        raise errt
    except requests.exceptions.RequestException as err:
        my_logger.error(f'{err.response.text}')
        raise err


@inject
def make_http_request_data(my_logger: Logger = Provide[LoggerContainer.logger], **kwargs) -> requests.Response:
    try:
        response = requests.request(
            "POST",
            url=f"{kwargs['baseurl']}{kwargs['url_seg']}",
            headers=kwargs['headers'],
            auth=kwargs['auth'],
            data=kwargs['data']
        )
        response.raise_for_status()
        if "X-Ratelimit-Limit" in response.headers:
            my_logger.warning(
                response.headers["X-Ratelimit-Limit"],
                response.headers["X-Ratelimit-Remaining"]
            )
        return response
    except requests.exceptions.ConnectionError as errc:
        my_logger.error(f'Connection error: {errc}')
        raise errc
    except requests.exceptions.HTTPError as errh:
        my_logger.error(f'http error: {errh}')
        raise errh
    except requests.exceptions.Timeout as errt:
        my_logger.error(f'Timeout: {errt}')
        raise errt
    except requests.exceptions.RequestException as err:
        my_logger.error(f'{err.response.text}')
        raise err


def get_env_variables(title: str = "corecard Jira") -> dict:
    # if os.name == "nt":
    #     filename = "config.json"
    # elif os.uname().nodename == "airflow":
    #     filename = "/home/<USER>/airflow/dags/data_pipeline/config.json"
    # else:
    #     filename = "/opt/airflow/dags/airflow/data_pipeline/config.json"
    #
    # # Using context manager.
    # with open(filename, "r") as fp:
    #     data = json.load(fp)
    #     # my_logger.info("%s", data)
    # keedb = f'KeePassDB_{os.name}'
    # keepass = f'KeyPassKey_{os.name}'
    #
    # ref = PyKeePass(filename=data[keedb], keyfile=data[keepass])
    # entry = ref.find_entries(title=data['KpCoreCardJira'], first=True)
    entry: EntryDetails = get_kp_entry_details(title)
    data = {
        'baseurl': entry.url,
        # 'username': entry.username, 'pwd': entry.password,
        # 'auth': HTTPBasicAuth(entry.username, entry.password),
        'payload': None
    }

    # data['aio_auth'] = BasicAuth(entry.username, entry.password, encoding='utf-8')
    auth_token = f'{entry.username}:{entry.password}'
    encoded_token = base64.b64encode(auth_token.encode()).decode()

    data['headers'] = {
        'Accept': "application/json", 'Content-Type': "application/json",
        'Authorization': f'Basic {encoded_token}'
    }

    return data


def get_logger(logger_name, log_file, use_formatter=False):
    log_queue = queue.Queue(-1)  # No limit on size

    logger = logging.getLogger(logger_name)

    queue_handler = logging.handlers.QueueHandler(log_queue)
    logger.addHandler(queue_handler)

    # logger.addHandler(get_console_handler(use_formatter))
    # logger.addHandler(get_file_handler(log_file, use_formatter))
    # console_handler_out = get_console_handler(use_formatter, stream_choice=1)
    console_handler_err = get_console_handler(use_formatter, stream_choice=2)
    file_handler = get_file_handler(log_file, use_formatter)

    logger.setLevel(logging.DEBUG)

    listener = QueueListener(log_queue, file_handler, respect_handler_level=True)
    listener.start()

    # Capture warnings into logging
    logging.captureWarnings(True)

    # Stop listener on application exit
    atexit.register(listener.stop)

    # with this pattern, it's rarely necessary to propagate the error up to parent
    logger.propagate = False

    return logger, log_queue


def get_file_handler(log_file, formatter=False):
    # source: https://stackoverflow.com/questions/3220284/how-to-customize-the-time-format-for-python-logging
    # source: https://stackoverflow.com/questions/533048/how-to-log-source-file-name-and-line-number-in-python
    # file_handler = TimedRotatingFileHandler(log_file, when='midnight', backupCount=3)
    # Using custom class to avoid error
    # PermissionError: [WinError 32] The process cannot access the file because it is being used by another process:
    # 'C:\\Users\\<USER>\\PycharmProjects\\airflow\\data_pipeline\\log\\main.log' ->
    # 'C:\\Users\\<USER>\\PycharmProjects\\airflow\\data_pipeline\\log\\main.log.2024-10-28'
    try:
        file_handler = SafeTimedRotatingFileHandler(log_file, when='midnight', backupCount=3, delay=True)
        file_handler.setLevel(logging.DEBUG)
        if formatter:
            format_setting = logging.Formatter(
                # "%(asctime)s |:| %(name)s - %(levelname)s - %(message)s",
                'D%(asctime)s.%(msecs)03d - %(levelname)-8s - %(funcName)-31s %(lineno)-4d | %(message)s',
                "%Y%m%dT%H:%M:%S%z"
            )
            # file_handler.setFormatter(format_setting)
            file_handler.setFormatter(CustomFormatterFile(
                'D%(asctime)s.%(msecs)03d%(timezone)s - %(levelname)-8s - %(funcName)-31s %(lineno)-4d | %(message)s',
                "%Y%m%dT%H:%M:%S"))

        return file_handler
    except PermissionError as e:
        print("Got Permission error while rotating log file")
        raise e
    except Exception as e:
        handle_exception(e)


class SafeTimedRotatingFileHandler(TimedRotatingFileHandler):
    def doRollover(self):
        try:
            super().doRollover()
        except PermissionError as e:
            retries = 5
            while retries > 0:
                time.sleep(1)
                try:
                    super().doRollover()
                    break
                except PermissionError:
                    retries -= 1
                    if retries == 0:
                        raise e


def get_console_handler(formatter=False, stream_choice: int = 1):
    if stream_choice == 1:
        console_handler = RichHandler(rich_tracebacks=True, markup=True)
    else:
        console_handler = CustomConsoleHandler(stream_choice)
    if formatter:
        format_setting = CustomFormatter(
            "%(asctime)s — %(name)s — %(levelname)s — %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S %Z"
        )
        console_handler.setFormatter(format_setting)
    return console_handler


class CustomConsoleHandler(logging.StreamHandler):
    def __init__(self, stream_choice: int = 1):
        super().__init__(stream=sys.stdout if stream_choice == 1 else sys.stderr)
        self.stream_choice = stream_choice

    def emit(self, record):
        if self.stream_choice == 2:
            if record.levelno < logging.WARNING:
                return
        elif self.stream_choice == 1:
            if record.levelno != logging.INFO:
                return
        super().emit(record)


class CustomFormatterFile(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = self.converter(record.created)
        if datefmt:
            s = time.strftime(datefmt, ct)
        else:
            t = time.strftime("%Y%m%dT%H:%M:%S", ct)
            s = "%s.%03d" % (t, record.msecs)
        return s

    def format(self, record):
        record.timezone = time.strftime('%z', self.converter(record.created))
        return super().format(record)


class CustomFormatter(logging.Formatter):
    def formatTime(self, record, date_format=None):
        tz_map = {"India Standard Time": "Asia/Kolkata"}
        local_time = datetime.now().astimezone()
        # local_timezone = local_time.tzinfo

        # Convert the timezone to a string
        timezone_str = local_time.strftime('%Z')

        local_timezone = ZoneInfo(tz_map[timezone_str])
        # Convert the timestamp to a timezone-aware datetime object
        dt = datetime.fromtimestamp(record.created, local_timezone)
        # ct = self.converter(record.created)
        if date_format:
            # s = time.strftime(date_format, ct)
            s = dt.strftime(date_format)
        else:
            # t = time.strftime("%Y-%m-%d %H:%M:%S", ct)
            # s = f"{t} {time.strftime('%z', ct)}"
            s = dt.strftime("%Y-%m-%d %H:%M:%S %Z")
        return s

# Global declaration of logger
# log_file_name = "log/main.log" if os.name == "nt" else os.getenv("AIRFLOW_HOME", "/tmp") + "/main.log"
# my_logger, print_queue = get_logger(__name__, log_file_name, use_formatter=True)


# Set SQLAlchemy engine logging level
# logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
async def fetch_with_retries_post_old(
        session: aiohttp.ClientSession, url: str,

        json_payload, retries: int = MAX_RETRIES,
        my_logger: Logger = Provide[LoggerContainer.logger],

) -> Union[ClientResponse, None]:
    """
    Perform a POST request with retry logic.

    Args:
        session (aiohttp.ClientSession): The HTTP session to use for requests.
        url (str): The URL to send the POST request to.
        json_payload (dict): The JSON payload to send with the request.
        retries (int): The number of retries to attempt.
        my_logger
    Returns:
        ClientResponse: The response object from the final request attempt.
    """
    global commit_transaction
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    while retry_count <= retries:
        try:
            async with session.post(url, data=json.dumps(json_payload)) as response:

                if response.status == 200:
                    my_logger.debug(f"url: {response.url}")
                    return await response.json()  # Successfully retrieved data
                elif response.status == 201:
                    my_logger.info("Resource created successfully.")
                    return await response.json()
                elif response.status == 204:
                    my_logger.info("Request successful, but no content to return.")
                    my_logger.debug(f"url: {response.url}")
                    return None
                elif response.status in (429, 503) or "Retry-After" in response.headers:
                    # Handle 429 rate limit errors and Retry-After header
                    retry_delay = await calculate_retry_delay(response, retry_delay)

                    my_logger.info(f"Rate limit hit. retry_delay = {retry_delay} milli seconds.")
                else:
                    my_logger.warning(f"Request failed with status {response.status}.")
                    # my_logger.dataframe_utils(await response.text())
                    async with lock:
                        commit_transaction = False

                    # my_logger.dataframe_utils(f"commit_transaction set to {commit_transaction}")
                    response.raise_for_status()
                    return None

        except (aiohttp.ClientError, ConnectionResetError) as e:
            async with lock:
                commit_transaction = False
            handle_exception(e)
        except asyncio.TimeoutError as to:
            async with lock:
                commit_transaction = False
            my_logger.debug(f"Connection timed out")
            my_logger.error(f"{to}")
        except Exception as e:
            async with lock:
                commit_transaction = False
            handle_exception(e)

        # Wait before retrying
        if (retry_count := retry_count + 1) > retries:
            my_logger.warning("Maximum retry limit reached.")
            break

        # Add jitter to retry delay
        jitter = retry_delay * (
                JITTER_MULTIPLIER_RANGE[0] +
                (JITTER_MULTIPLIER_RANGE[1] - JITTER_MULTIPLIER_RANGE[0]) * random.random()
        )
        my_logger.debug(f"value of jitter = {jitter} milli seconds")
        await asyncio.sleep(jitter / 1000)

    return None


@inject
async def fetch_with_retries_get_old(
        session: aiohttp.ClientSession, url: str,
        # auth: BasicAuth,
        # headers: dict,
        params: Union[Dict[str, Any], None] = None,
        my_logger: Logger = Provide[LoggerContainer.logger],

) -> Any:
    """
    Fetch data from the specified URL with retry logic for rate limiting.
    :param session: aiohttp ClientSession.
    :param url: API endpoint URL.
    :param params: HTTP request parameters.
    :param my_logger: Logger file instance
    :return: JSON response data.

    """
    global commit_transaction
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    while retry_count <= MAX_RETRIES:
        try:
            async with session.get(url, params=params or None) as response:
                if response.status == 200:
                    # Check for the X-RateLimit-NearLimit header
                    # my_logger.dataframe_utils(f"url: {response.url}")
                    if response.headers.get("X-RateLimit-NearLimit") == "true":
                        my_logger.warning("Warning: Less than 20% of the rate limit budget remains.")
                    return await response.json()
                elif response.status == 201:
                    my_logger.info("Resource created successfully.")
                    return await response.json()
                elif response.status == 204:
                    my_logger.info("Request successful, but no content to return.")
                    my_logger.debug(f"url: {response.url}")
                    return None
                elif response.status == 429 or "Retry-After" in response.headers:
                    # Handle 429 rate limit errors and Retry-After header
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                else:
                    my_logger.error(f"Request failed with status {response.status}.")
                    my_logger.debug(await response.text())  # For additional debugging
                    async with lock:
                        commit_transaction = False
                    return None

                # Raise an exception for other HTTP errors
                response.raise_for_status()

        except ClientResponseError as e:
            my_logger.error(f"Request failed with status {e.status}.")
            if e.status != 429:
                # Only retry on 429 status
                async with lock:
                    commit_transaction = False
                raise e
        except (aiohttp.client_exceptions.ConnectionTimeoutError, asyncio.TimeoutError) as e:
            my_logger.error(f"Request error: {e}. Retrying {retry_count}/{MAX_RETRIES} times.")
            async with lock:
                commit_transaction = False
        except Exception as e:
            my_logger.error(f"An unexpected error occurred: {e}")
            async with lock:
                commit_transaction = False
            handle_exception(e)

        if retry_delay > 0:
            retry_count += 1
            my_logger.debug(f"Retrying in {retry_delay / 1000:.2f} seconds...retry_count = {retry_count}")
            await asyncio.sleep(retry_delay / 1000)  # Convert to seconds

    raise Exception(f"Failed to fetch data after {MAX_RETRIES} retries.")


@inject
async def delete_worklog(
        project_key: str,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    return_string = ""
    try:
        task = asyncio.current_task()
        task.set_name("delete_worklog")

        # Aliases for models
        worklog_alias = aliased(WorkLog)
        deleted_worklog_alias = aliased(DeletedWorklog)

        cte_query = (
            select(worklog_alias.id.label('worklog_id'))
            .join(deleted_worklog_alias, worklog_alias.id == deleted_worklog_alias.worklogId)
            .cte('matching_worklogs')
        )

        # Create the delete query using the subquery
        delete_query = (
            delete(WorkLog)
            .where(WorkLog.id.in_(select(cte_query.c.worklog_id)))
            .returning(column('xmax'))
        )
        my_logger.debug(f"{compile_query(delete_query)}")
        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            async with pg_async_session.begin():
                result = await pg_async_session.execute(delete_query.execution_options(synchronize_session='fetch'))
                deleted_count = result.rowcount
                await pg_async_session.commit()
        my_logger.debug(f"{project_key} deleted count = {deleted_count}")


    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)

    return return_string


@inject
async def process_task_old(
        display_rich: bool = True,
        projects: tuple = (),
        q_container: DynamicContainer = Provide[QueueContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    # Define the tasks with direct function calls

    # project_list = ["plat", "acq", "plp"]

    my_current_task = asyncio.current_task()
    my_current_task.set_name("process_task")
    schemas = queue_container.schemas()

    # project_list = list(set(schemas + list(projects)) - {'information_schema', 'public'})
    # my_logger.dataframe_utils(f"project list = {project_list}")
    project_list = projects
    tasks = [
        (create_db_extension, [], {}),
        *[(create_schema_tables_ddl, [project], {}) for project in project_list],
        (get_deleted_worklog, [], {}),
        (get_fields, [], {}),
        (get_jira_users, [], {}),
        (get_all_jira_boards, [], {}),
        *[
            (process_jira_versions, [project], {})
            for project in project_list
        ],
        *[
            (get_sprint_details, [project], {})
            for project in project_list
        ],
        *[
            (process_jira_issues, [project, 'project', True], {})
            # (process_jira_issues, [project, 'recon', True], {})
            for project in project_list
        ],
        *[
            (upsert_issue_classification, [project], {})
            for project in project_list
        ],
        *[
            (delete_worklog, [project], {})
            for project in project_list
        ],
        (create_refresh_mv, [project_list], {})
    ]

    # Set up the progress bars
    overall_progress = Progress(
        "{task.description}",
        MofNCompleteColumn(separator="/")
    )
    overall_task = overall_progress.add_task("Overall Task Progress", total=len(tasks))

    job_progress = Progress(
        "{task.description}",
        TextColumn("[bold]Params:[/bold] {task.fields[parameters]}", justify="left"),
        SpinnerColumn(),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        " ",
        TimeElapsedColumn(),
        TextColumn("{task.fields[return_value]}", justify="left")
    )

    # Create a separate progress for `produce_stats`
    stats_progress = Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        "[progress.percentage]{task.percentage:>3.0f}%",
        "•",
        TextColumn("Processed: {task.completed} / {task.total}"),
        TimeElapsedColumn(),
    )

    stats_progress_centered = Align.center(stats_progress, vertical="middle")
    job_progress_centered = Align.center(job_progress, vertical="middle")
    overall_progress_centered = Align.center(overall_progress, vertical="middle")

    # Initialize the progress task for stats
    stats_task_id = stats_progress.add_task(
        description="Processing Records", total=None, start=False
    )

    # Enhanced stats tracking variables
    stats_start_time = time.time()
    total_records_processed = 0
    last_update_time = time.time()
    processing_rates = []  # Store recent TPS values for averaging

    # Create enhanced stats table
    stats_table = Table(
        show_footer=False, show_header=True, title="Processing Statistics"
    )
    stats_table.add_column("Metric", justify="left", style="cyan", no_wrap=True)
    stats_table.add_column("Value", justify="right", style="green")
    stats_table.add_column("Unit", justify="left", style="dim")

    # Initialize stats table with default values
    stats_table.add_row("Records Processed", "0", "records")
    stats_table.add_row("Total Time", "0.0", "seconds")
    stats_table.add_row("Current TPS", "0.0", "records/sec")
    stats_table.add_row("Average TPS", "0.0", "records/sec")
    stats_table.add_row("Completion", "0.0", "%")

    async def monitor_stats(schema: str):
        """Monitor the stats_queue and update the progress with enhanced reporting."""
        nonlocal total_records_processed, last_update_time, processing_rates
        none_count = 0
        # active_queues = q_container.active_queues(schema)
        q_container.config.override({"schema_name": f"{schema}"})

        while True:
            try:
                stats_result = await q_container.queue_selector()["queue_stats"].get()
                if stats_result.get('isLast', True):
                    none_count += 1


                # Reset progress if 10 None entries are detected
                if none_count == stats_result.get('producer_count', 10):
                    my_logger.info(f"none_count = {none_count}. Stats values: {stats_progress.tasks[stats_task_id].total}")
                    my_logger.info(f"Final processing report - Total Records: {total_records_processed:,}, "
                                 f"Total Time: {time.time() - stats_start_time:.1f}s, "
                                 f"Average TPS: {sum(processing_rates) / len(processing_rates) if processing_rates else 0:.1f}")

                    # stats_progress.reset(stats_task_id)
                    # none_count = 0
                    if q_container.queue_selector()["queue_stats"].qsize() == 0:
                        break

                # Update the progress with the new record count
                stats_progress.update(stats_task_id, completed=stats_progress.tasks[stats_task_id].total)
                records_processed = stats_result['record_count']
                total_records_processed += records_processed
                stats_progress.update(stats_task_id, advance=records_processed)
                stats_progress.update(stats_task_id, total=max(stats_result['total'], total_records_processed))

                # Calculate timing and TPS metrics
                current_time = time.time()
                elapsed_time = current_time - stats_start_time
                time_since_last_update = current_time - last_update_time

                # Calculate current TPS (transactions per second)
                current_tps = records_processed / time_since_last_update if time_since_last_update > 0 else 0
                processing_rates.append(current_tps)

                # Keep only last 10 TPS values for moving average
                if len(processing_rates) > 10:
                    processing_rates.pop(0)

                # Calculate average TPS
                avg_tps = sum(processing_rates) / len(processing_rates) if processing_rates else 0

                # Calculate completion percentage
                completion_pct = (total_records_processed /max(stats_result['total'], total_records_processed) * 100) if stats_result['total'] > 0 else 0

                # Update stats table
                stats_table.columns[1]._cells[0] = f"{total_records_processed:,}"
                stats_table.columns[1]._cells[1] = f"{elapsed_time:.1f}"
                stats_table.columns[1]._cells[2] = f"{current_tps:.1f}"
                stats_table.columns[1]._cells[3] = f"{avg_tps:.1f}"
                stats_table.columns[1]._cells[4] = f"{completion_pct:.1f}"

                last_update_time = current_time

            finally:
                # async with debug_queue_operation(q_container.queue_selector()["queue_stats"], "task_done", "queue_stats"):
                #     pass
                q_container.queue_selector()["queue_stats"].task_done()

        my_logger.info(f"Stats task completed. none_count = {none_count}")


    # Add tasks to the progress with argc and argv details
    job_tasks = []
    for function, args, kwargs in tasks:
        argc = len(args) + len(kwargs)
        argv = ""
        if len(args) > 0:
            # argv = f"{", ".join(args)} "
            argv = f"{', '.join(map(str, args))} "
        if len(kwargs) > 0:
            tmp_str = ""
            for k, v in kwargs.items():
                tmp_str += f"{k}={v} "
            argv += f"{tmp_str}"
        # argv = f"Args: {args}, Kwargs: {kwargs}"
        task_description = f"[cyan]{function.__name__}"
        if display_rich:
            job_task_id = job_progress.add_task(
                task_description, total=100, parameters=argv, return_value="Pending...",
                start=False,
            )
            job_tasks.append(job_task_id)

    # Create the layout
    layout = create_layout()
    layout.split_row(
        Layout(name="left"),
        Layout(name="right", ratio=2)
    )

    # Divide the left side into 4 parts to accommodate the new stats table
    layout["left"].split_column(
        Layout(name="left-first", ratio=1),
        Layout(name="left-second", ratio=2),
        Layout(name="left-third", ratio=1),
        Layout(name="left-fourth", ratio=2)
    )
    layout["right"].split_column(
        Layout(name="right-first", ratio=3),
        Layout(name="right-second", ratio=2),
    )

    # Set up the right-second panel with a placeholder initially
    layout["right-second"].update(Panel("No tasks exceeding the limit.", title="Task Monitor", border_style="red"))

    # Initialize the queue status table
    queue_status_table = Table(
        show_footer=False, show_header=False
    )
    queue_status_table.pad_edge = False
    queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
    queue_status_table.add_column("qsize", style="magenta", justify="right")
    queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
    queue_status_table.add_column("qsize", style="magenta", justify="right")

    # queues = [
    #     (queue_issues, "queue_issues"), (queue_stats, "queue_stats"),
    #     (queue_issue, "queue_issue"), (queue_changelog, "queue_changelog"),
    #     (queue_worklog, "queue_worklog"), (queue_comment, "queue_comment"),
    #     (queue_issue_links, "queue_issue_links"),
    # ]

    # Helper function to create rows for the table

    def initialize_queue_status_table(
            schema: str,

    ):
        """Initialize the table with rows in a 4-column format."""
        # Dynamically fetch schema-specific queues
        # active_queues = q_container.active_queues(schema)
        # queue_items = list(active_queues.items())
        q_container.config.override({"schema_name": f"{schema}"})
        queue_items = list(q_container.queue_selector().items())

        # Iterate over the queue list in steps of 2 to fill two columns per row
        for i in range(0, len(queue_items), 2):
            # Retrieve the first queue details
            # First queue
            q1_name, q1_queue = queue_items[i]
            q1_size = str(q1_queue.qsize())

            # Second queue (if exists)
            if i + 1 < len(queue_items):
                q2_name, q2_queue = queue_items[i + 1]
                q2_size = str(q2_queue.qsize())
            else:
                q2_name, q2_size = "", ""  # Empty cells for the last row if uneven

            # Add a row with two queues' information
            queue_status_table.add_row(q1_name, q1_size, q2_name, q2_size)

    initialize_queue_status_table(project_list[0])

    async def update_queue_counts(schema: str):
        """Update the queue status table periodically."""
        # active_queues = q_container.active_queues(schema)
        # queue_items = list(active_queues.items())
        q_container.config.override({"schema_name": f"{schema}"})
        queue_items = list(q_container.queue_selector().items())

        while True:
            for i in range(0, len(queue_items), 2):
                # First queue in the pair

                q1_name, q1_queue = queue_items[i]
                q1_size = str(q1_queue.qsize())

                # Second queue in the pair (if exists)
                if i + 1 < len(queue_items):
                    q2_name, q2_queue = queue_items[i + 1]
                    q2_size = str(q2_queue.qsize())
                else:
                    q2_name, q2_size = "", ""  # Empty values for the last row if odd

                # Update the table rows dynamically
                queue_status_table.columns[0]._cells[i // 2] = q1_name
                queue_status_table.columns[1]._cells[i // 2] = q1_size
                queue_status_table.columns[2]._cells[i // 2] = q2_name
                queue_status_table.columns[3]._cells[i // 2] = q2_size

            # for queue_no in range(0, len(queue_items), 2):
            #     # Update first queue's size
            #     q1_inst, q1_name = queues[queue_no]
            #     queue_status_table.columns[1]._cells[queue_no // 2] = str(q1_inst.qsize())
            #
            #
            #     # Update second queue's size if it exists
            #     if queue_no + 1 < len(queues):
            #         q2_inst, q2_name = queues[queue_no + 1]
            #         queue_status_table.columns[3]._cells[queue_no // 2] = str(q2_inst.qsize())
            await asyncio.sleep(1)

    # Map layout sections to progress and queue status
    layout["left-first"].update(Panel(overall_progress_centered, title="Overall Progress", border_style="green"))
    layout["left-second"].update(Panel(queue_status_table, title="Queue Status", border_style="cyan"))
    layout["left-third"].update(Panel(stats_progress_centered, title="Stats Progress", border_style="yellow"))
    layout["left-fourth"].update(Panel(stats_table, title="Processing Statistics", border_style="magenta"))
    layout["right-first"].update(Panel(job_progress_centered, title="[b]Task Progress", border_style="red"))

    # Initialize monitor task
    monitor_task = asyncio.create_task(
        monitor(
            task_limit_sec=10, exclude_names=("monitor",),
            monitor_panel=layout["right-second"]
        ),
        name="monitor"
    )

    async def execute_tasks():
        for i, (function, args, kwargs) in enumerate(tasks):
            try:
                my_logger.debug(f"Processing {function.__name__}")
                result = None
                if display_rich:
                    job_progress.start_task(job_tasks[i])
                if function.__name__ == "process_jira_issues":
                    queue_task = asyncio.create_task(update_queue_counts(args[0]), name="queue_monitor")
                    stats_task = asyncio.create_task(monitor_stats(args[0]), name="monitor_stats")
                    # deadlock_task = asyncio.create_task(monitor_deadlocks(), name="monitor_deadlocks")
                    # my_logger.info(f"deadlock_task = {deadlock_task}")
                    my_logger.info(f"starting function = {function.__name__}")
                    result = await function(*args, **kwargs)
                    my_logger.info(f"{function.__name__} completed. Cancelling queue_task & stats_task")

                    queue_task.cancel()
                    stats_task.cancel()
                    # deadlock_task.cancel()
                else:
                    if asyncio.iscoroutinefunction(function):
                        result = await function(*args, **kwargs)
                    else:
                        result = function(*args, **kwargs)
                if display_rich:
                    job_progress.update(job_tasks[i], advance=100, return_value=str(result))
                    job_progress.stop_task(job_tasks[i])
                    overall_progress.update(overall_task, completed=i + 1)
                my_logger.debug(f"Processed {function.__name__}")
            except Exception as e:
                my_logger.error(f"Error executing {function.__name__}: {e}")
                handle_exception(e)

    # Conditional Rich UI handling
    if display_rich:
        with Live(layout, auto_refresh=True, screen=False, transient=False):
            await execute_tasks()
    else:
        await execute_tasks()
    monitor_task.cancel()

# @inject
# async def consume_changelog_old(
#         queue_id: int,
#         name: str,
#         queue_changelog: Queue,
#         queue_upsert_issue: Queue,
#         project_key: str,
#         pg_async_session: AsyncSession,
#         # pg_session: Session,
#         # headers: dict,
#         http_session: aiohttp.ClientSession,
#         my_logger: Logger = Provide[LoggerContainer.logger],
#         # queue_changelog: asyncio.Queue = Provide[QueueContainer.queue_changelog],
# ):
#     try:
#         loop_count = 0
#
#         while True:
#             loop_count += 1
#             async with debug_queue_operation(queue_changelog, "get", "queue_changelog") as item:
#                 # df_changelog: pd.DataFrame = await queue_changelog.get()
#                 df_changelog: pd.DataFrame = item
#             try:
#                 if df_changelog is None:
#                     await queue_upsert_issue.put(None)
#                     break
#
#                 if df_changelog.shape[0] > 0:
#                     df_changelog = df_changelog.join(
#                         pd.json_normalize(df_changelog.changelog)
#                     ).drop(columns=["changelog"])
#                     df_changelog.rename(columns={'id': 'issue_id', 'key': 'issue_key'}, inplace=True)
#
#                     changelog_record: list = df_changelog.query("total > maxResults")[
#                         ['issue_id', 'issue_key', 'total']].to_dict(orient='records')
#                     my_logger.debug(f"{name} changelog_record = {len(changelog_record)}")
#                     if len(changelog_record) > 0:
#                         async def fetch_changelog_data(issue_id, issue_key, start_at):
#                             """
#                             Fetch changelog data for a specific issue with proper error handling.
#
#                             Returns a dictionary with consistent structure regardless of success/failure.
#                             """
#                             asyncio.current_task().set_name("fetch_changelog_data")
#                             base_result = {
#                                 "issue_id": issue_id,
#                                 "issue_key": issue_key,
#                                 "start_at": start_at,
#                                 "success": False,
#                                 "error": None
#                             }
#                             try:
#                                 changelog = await fetch_changelog(http_session, issue_key, params={'startAt': start_at})
#                                 if not changelog.get('success', False):
#                                     error_msg = changelog.get('exception', 'Unknown error from fetch_changelog')
#                                     my_logger.error(
#                                         f"Failed to fetch changelog for {issue_key} (start_at={start_at}): {error_msg}")
#
#                                     base_result.update({
#                                         "error": error_msg,
#                                         "startAt": start_at,
#                                         "maxResults": 0,
#                                         "total": 0,
#                                         "histories": []
#                                     })
#                                     return base_result
#                                 # Success case - extract the data
#                                 result_data = changelog.get('result', {})
#                                 base_result.update({
#                                     "success": True,
#                                     "startAt": result_data.get('startAt', start_at),
#                                     "maxResults": result_data.get('maxResults', 0),
#                                     "total": result_data.get('total', 0),
#                                     "histories": result_data.get('values', [])
#                                 })
#                                 return base_result
#                             except aiohttp.ClientResponseError as err:
#                                 error_msg = f"HTTP error {err.status}: {err.message}"
#                                 my_logger.error(
#                                     f"ClientResponseError for {issue_key} (start_at={start_at}): {error_msg}")
#                                 base_result.update({
#                                     "error": error_msg,
#                                     "startAt": start_at,
#                                     "maxResults": 0,
#                                     "total": 0,
#                                     "histories": []
#                                 })
#                                 # Don't re-raise, handle gracefully
#                                 return base_result
#
#                             except asyncio.TimeoutError as err:
#                                 error_msg = f"Request timeout: {str(err)}"
#                                 my_logger.error(f"Timeout error for {issue_key} (start_at={start_at}): {error_msg}")
#                                 base_result.update({
#                                     "error": error_msg,
#                                     "startAt": start_at,
#                                     "maxResults": 0,
#                                     "total": 0,
#                                     "histories": []
#                                 })
#                                 return base_result
#
#                             except Exception as err:
#                                 error_msg = f"Unexpected error: {str(err)}"
#                                 my_logger.error(f"Unexpected error for {issue_key} (start_at={start_at}): {error_msg}")
#                                 base_result.update({
#                                     "error": error_msg,
#                                     "startAt": start_at,
#                                     "maxResults": 0,
#                                     "total": 0,
#                                     "histories": []
#                                 })
#                                 return base_result
#                             finally:
#                                 my_logger.info(f"queue_changelog Queue size: {queue_changelog.qsize()}")
#
#                         # changelog end point returns changelogs in ascending order
#                         # issue search returns changelogs in descending order.
#                         # Therefore, start range needs to be 0 and end range needs to be total records - 100
#
#                         tasks_changelog = [
#                             fetch_changelog_data(record['issue_id'], record['issue_key'], start_at)
#                             for record in changelog_record
#                             for start_at in range(0, record['total'] - 100, 100)
#                         ]
#
#                         changelog_results = await asyncio.gather(*tasks_changelog, return_exceptions=True)
#                         # Filter out exceptions, None values, and invalid data
#                         valid_results = []
#                         for result in changelog_results:
#                             if (isinstance(result, dict) and
#                                     'issue_id' in result and
#                                     'issue_key' in result and
#                                     'histories' in result):
#                                 valid_results.append(result)
#                             elif isinstance(result, Exception):
#                                 my_logger.error(f"Exception in changelog fetch: {result}")
#                             else:
#                                 my_logger.warning(f"Invalid result: {result}")
#                         if valid_results:
#                             df = pd.DataFrame(valid_results)
#                             df_changelog = pd.concat([df_changelog, df], ignore_index=True)
#
#                     df_changelog = df_changelog.explode(column="histories")
#                     df_changelog.dropna(subset=["histories"], inplace=True)
#
#                     df_changelog.histories = df_changelog.apply(
#                         lambda x: add_issue_key_issue_id(x['histories'], x['issue_key'], x['issue_id']), axis=1
#                     )
#
#                     df_changelog.drop(columns=["startAt", "maxResults", "total", "issue_id", "issue_key"], inplace=True)
#                     df_changelog = pd.json_normalize(df_changelog.histories)[
#                         ["id", "created", "author.accountId", "items", "issue_key", "issue_id"]]
#
#                     df_changelog.rename(columns={"author.accountId": "author"}, inplace=True)
#
#                     # df_changelog[['id', 'issue_id']] = df_changelog[['id', 'issue_id']].astype(pd.Int64Dtype())
#                     df_changelog = cast_columns(df_changelog, ['id', 'issue_id'], pd.Int64Dtype())
#                     df_changelog = cast_columns(df_changelog, ['created'], "datetime")
#                     await queue_upsert_issue.put(
#                         {
#                             "model": ChangelogJSON,
#                             "df": df_changelog,
#                             "on_conflict_update": False,
#                             "no_update_cols": ("items",)
#                         }
#                     )
#
#                     # df_changelog['created'] = pd.to_datetime(df_changelog['created'])
#
#                     # upsert_single(
#                     #     pg_session, ChangelogJSON, df_changelog,
#                     #     primary_key="id", on_conflict_update=True,
#                     #     no_update_cols=("items",)
#                     # )
#                     # my_logger.dataframe_utils(f"{name} upsert single")
#                     # upsert(
#                     #     pg_session, ChangelogJSON, df_changelog, primary_key="id",
#                     #     on_conflict_update=False, no_update_cols=("items",)
#                     # )
#                     # async with issue_changelog_lock:
#                     #     _ = await upsert_async(
#                     #         pg_async_session, ChangelogJSON, df_changelog,
#                     #         on_conflict_update=False,
#                     #         no_update_cols=("items",)
#                     #     )
#
#             finally:
#                 async with debug_queue_operation(queue_changelog, "task_done", "queue_changelog"):
#                     # queue_changelog.task_done()
#                     pass
#     except Exception as e:
#         global commit_transaction
#         async with lock:
#             commit_transaction = False
#         handle_exception(e)
#     finally:
#         my_logger.debug(f"Queue size queue_changelog: {queue_changelog.qsize()} ")
#
#     my_logger.info(f"{name} is done!!!")


# @inject
# async def consume_worklog_old(
#         queue_id: int,
#         name: str,
#         queue_worklog: Queue,
# queue_upsert_issue: Queue,
#
#         project_key, pg_async_session,
#         # pg_session,
#         # headers: dict,
#         http_session: aiohttp.ClientSession,
#         my_logger: Logger = Provide[LoggerContainer.logger],
#         # queue_worklog: asyncio.Queue = Provide[QueueContainer.queue_worklog],
# ):
#     # Your implementation here
#     loop_count = 0
#
#     try:
#         while True:
#             try:
#                 async with debug_queue_operation(queue_worklog, "get", "queue_worklog") as item:
#                     # df_worklog: pd.DataFrame | None = await queue_worklog.get()
#                     df_worklog: pd.DataFrame | None = item
#
#                 loop_count += 1
#
#                 if df_worklog is None:
#                     await queue_upsert_issue.put(None)
#                     break
#
#                 _ = await quick_save_async(
#                     df_worklog,
#                     f"df_worklog_first_{name}_{loop_count}.xlsx",
#                     path=f"c:/vishal/log/worklog/{name}_{loop_count}"
#                 )
#
#                 df_worklog = df_worklog.join(
#                     pd.json_normalize(df_worklog.worklog)
#                 ).drop(columns=["worklog"])
#                 _ = await quick_save_async(
#                     df_worklog,
#                     f"df_worklog_normalize_first_{name}_{loop_count}.xlsx",
#                     path=f"c:/vishal/log/worklog/{name}_{loop_count}"
#                 )
#
#                 df_worklog.query("total > 0", inplace=True)
#
#                 if df_worklog.shape[0] > 0:
#                     worklog_keys: list = df_worklog.query("total > maxResults")[['key', 'id']].to_dict(
#                         orient='records')
#                     my_logger.debug(f"worklog keys = {worklog_keys}")
#                     df_worklog.drop(
#                         columns=["startAt", "maxResults", "total"],
#                         inplace=True
#                     )
#                     df_worklog.rename(
#                         columns={"id": "issue_id", "key": "issue_key"},
#                         inplace=True
#                     )
#
#
#                     if len(worklog_keys) > 0:
#                         async def fetch_worklog_data(issue: Dict[str, str]):
#                             task = asyncio.current_task()
#                             task.set_name(f"fetch_worklog_data_name")
#
#                             issue_key = issue['key']
#                             issue_id = issue['id']
#
#                             try:
#                                 response = await fetch_worklog(http_session, issue_key)
#                                 if response["success"]:
#                                     worklogs = response["result"]
#                                     return {"issue_key": issue_key, "issue_id": issue_id, "worklogs": worklogs}
#                                 else:
#                                     exception = response["exception"]
#                                     my_logger.error(f"Failed to fetch worklogs: {exception}")
#
#                             except aiohttp.ClientResponseError as err:
#                                 handle_exception(err)
#                             finally:
#                                 my_logger.info(f"queue_worklog Queue size: {queue_worklog.qsize()}")
#
#                         tasks_worklog = [fetch_worklog_data(issue_key) for issue_key in worklog_keys]
#                         try:
#                             worklog_results = await asyncio.gather(*tasks_worklog, return_exceptions=True)
#
#                             if len(worklog_results) > 0:
#                                 # Filter out exceptions, None values, and invalid data
#                                 valid_results = []
#                                 for result in worklog_results:
#                                     if (isinstance(result, dict) and
#                                             'issue_key' in result and
#                                             'issue_id' in result and
#                                             'worklogs' in result):
#                                         valid_results.append(result)
#                                     elif isinstance(result, Exception):
#                                         my_logger.error(f"Exception in worklog fetch: {result}")
#                                     else:
#                                         my_logger.warning(f"Invalid result: {result}")
#                                 my_logger.debug(f"valid_results len = {len(valid_results)}:{valid_results}")
#                                 if valid_results:
#                                     df = pd.DataFrame(valid_results)
#                                     _ = await quick_save_async(
#                                         df_worklog,
#                                         f"df_worklog_after_valid_results_{name}_{loop_count}.xlsx",
#                                         path=f"c:/vishal/log/worklog/{name}_{loop_count}"
#                                     )
#
#                                     if df.shape[0] > 0:
#                                         df_worklog = pd.concat([df_worklog, df])
#                                     else:
#                                         my_logger.info(f"df_worklog is empty")
#                                         my_logger.info(f"worklog_results = {worklog_results}")
#
#                         except aiohttp.ClientResponseError as e:
#                             handle_exception(e)
#                         except Exception as e:
#                             handle_exception(e)
#
#
#                     df_worklog = df_worklog.explode(column="worklogs").reset_index(drop=True)
#
#
#                     df_worklog = df_worklog.join(
#                         pd.json_normalize(df_worklog['worklogs'])
#                     ).drop(columns=["worklogs"])
#
#
#
#                     df_worklog = df_worklog[df_worklog['id'].notna() & (df_worklog['id'] != '')]
#
#
#                     drop_columns_prefixes = [
#                         "self", "issueId", "author.", "updateAuthor."
#                     ]
#                     drop_column_exception = {
#                         "author.accountId", "updateAuthor.accountId",
#                     }
#
#                     try:
#                         df_worklog.drop(
#                             columns=[
#                                 col for col in df_worklog.columns if any(map(col.startswith, drop_columns_prefixes))
#                                                                      and col not in drop_column_exception
#                             ], inplace=True
#                         )
#                     except AttributeError as e:
#                         my_logger.error(f"Failed to drop columns: {e}")
#                         _ = await quick_save_async(
#                             df_worklog,
#                             f"df_worklog_after_fetch_error_{name}_{loop_count}.xlsx",
#                             path=f"c:/vishal/log/worklog/{name}_{loop_count}"
#                         )
#
#
#                     if 'comment.version' in df_worklog.columns:
#                         df_worklog.loc[pd.notnull(df_worklog['comment.version']), 'comment'] = df_worklog.loc[
#                             pd.notnull(df_worklog['comment.version'])].apply(
#                             lambda x: {
#                                 "version": x["comment.version"],
#                                 "type": x["comment.type"],
#                                 "content": x["comment.content"]
#                             },
#                             axis=1
#                         )
#                         df_worklog['comment'] = df_worklog['comment'].apply(lambda x: x if pd.notnull(x) else None)
#
#                         df_worklog.drop(
#                             columns=["comment.version", "comment.type", "comment.content"],
#                             inplace=True
#                         )
#                         _ = await quick_save_async(
#                             df_worklog,
#                             f"df_worklog_after_fetch_comment_updated_{name}_{loop_count}.xlsx",
#                             path=f"c:/vishal/log/worklog/{name}_{loop_count}"
#                         )
#
#                     df_worklog.rename(
#                         columns={
#                             "author.accountId": "author",
#                             "updateAuthor.accountId": "updateauthor"
#                         }, inplace=True
#                     )
#                     df_worklog = cast_columns(df_worklog, ['id', 'timeSpentSeconds', 'issue_id'], pd.Int64Dtype())
#                     df_worklog = cast_columns(df_worklog, ['created', 'updated', 'started'], "datetime")
#                     await queue_upsert_issue.put(
#                         {
#                             "model": WorkLog,
#                             "df": df_worklog,
#                             "no_update_cols": ("timeSpentHours",)
#                         }
#                     )
#
#                     # df_worklog[['id', 'timeSpentSeconds', 'issue_id']] = df_worklog[
#                     #     ['id', 'timeSpentSeconds', 'issue_id']
#                     # ].astype(pd.Int64Dtype())
#
#                     # Specify the date format
#                     # date_format = '%Y-%m-%dT%H:%M:%S.%f%z'
#                     #
#                     # for col in ['created', 'updated', 'started']:
#                     #     df_worklog[col] = pd.to_datetime(df_worklog[col], format=date_format)
#
#                     # upsert(
#                     #     pg_session, WorkLog, df_worklog, primary_key="id",
#                     #     no_update_cols=("timeSpentHours",)
#                     # )
#                     # async with issue_worklog_lock:
#                     #     _ = await upsert_async(
#                     #         pg_async_session, WorkLog, df_worklog,
#                     #         no_update_cols=("timeSpentHours",)
#                     #     )
#
#             finally:
#                 async with debug_queue_operation(queue_worklog, "task_done", "queue_worklog"):
#                     # queue_worklog.task_done()
#                     pass
#
#
#     except Exception as e:
#         global commit_transaction
#         async with lock:
#             commit_transaction = False
#         handle_exception(e)
#     finally:
#         my_logger.info(f"Worklog {name} is done!!!. Queue Size: {queue_worklog.qsize()}")


# @inject
# async def consume_comment_old(
#         queue_id: int,
#         queue_comment: Queue,
#         queue_upsert_issue: Queue,
#         project_key: str,
#         pg_async_session: AsyncSession,
#         # pg_session: Session,
#         # headers: dict,
#         http_session: aiohttp.ClientSession,
#         my_logger: Logger = Provide[LoggerContainer.logger],
#         # queue_comment: asyncio.Queue = Provide[QueueContainer.queue_comment],
# ):
#     loop_count = 0
#     try:
#         while True:
#             try:
#                 loop_count += 1
#                 async with debug_queue_operation(queue_comment, "get", "queue_comment") as item:
#                     # df_comment: pd.DataFrame = await queue_comment.get()
#                     df_comment: pd.DataFrame | None = item
#
#                 if df_comment is None:
#                     await queue_upsert_issue.put(None)
#                     break
#
#                 if df_comment.shape[0] > 0:
#                     df_comment = df_comment.join(
#                         pd.json_normalize(df_comment.comment)
#                     ).drop(columns=["comment", "self"])
#                     df_comment.query("total > 0", inplace=True)
#
#                     if df_comment.shape[0] > 0:
#                         comment_keys: list = df_comment.query("total > maxResults")[['key', 'id']].to_dict(
#                             orient='records')
#
#                         if len(comment_keys) > 0:
#                             # Send these to async process
#                             async def fetch_comments_data(issue: Dict[str, str]):
#                                 issue_key = issue['key']
#                                 issue_id = issue['id']
#                                 result = await fetch_comments(http_session, issue_key, my_logger=my_logger)
#                                 return {
#                                     "issue_key": issue_key, "issue_id": issue_id,
#                                     "comments": result["comments"], "success": result["success"]
#                                 }
#                             tasks_comment = [fetch_comments_data(issue_key) for issue_key in comment_keys]
#                             comment_results = await asyncio.gather(*tasks_comment, return_exceptions=True)
#                             valid_results = []
#                             failures = 0
#                             for result in comment_results:
#                                 if isinstance(result, dict) and result.get('success', False):
#                                     valid_results.append(result)
#                                 else:
#                                     failures += 1
#                                     my_logger.error(f"Comment fetch failed: {result}, failures = {failures}")
#                             FAILURE_THRESHOLD = max(1, int(0.5 * len(tasks_comment)))
#                             my_logger.debug(f"FAILURE_THRESHOLD: {FAILURE_THRESHOLD}, failures = {failures}")
#                             if failures > FAILURE_THRESHOLD:
#                                 my_logger.critical(f"Too many comment fetch failures ({failures}/{len(tasks_comment)}), triggering shutdown.")
#                                 global commit_transaction
#                                 async with lock:
#                                     commit_transaction = False
#                                 raise RuntimeError("Graceful shutdown: too many comment fetch failures")
#
#
#                     if df_comment.shape[0] > 0:
#                         df_comment.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
#                         df_comment = df_comment.explode(column="comments").reset_index(drop=True)
#                         try:
#                             df_comment = df_comment.join(
#                                 pd.json_normalize(df_comment["comments"])
#                                 [
#                                     [
#                                         "id", "author.accountId", "updateAuthor.accountId",
#                                         "created", "updated", "jsdPublic", "body.version", "body.type", "body.content"
#                                     ]
#                                 ]
#                             ).drop(columns=["comments", "maxResults", "total", "startAt"])
#                         except KeyError as e:
#                             exc_type, exc_value, exc_tb = sys.exc_info()
#                             tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
#                             my_logger.error(''.join(tb.format_exception_only()))
#                             my_logger.error(e)
#
#                         df_comment["body"] = df_comment.apply(
#                             lambda x: {
#                                 "version": x.get("body.version", 1),
#                                 "type": x.get("body.type", "doc"),
#                                 "content": x.get("body.content", [])
#                             }, axis=1
#                         )
#                         df_comment.drop(columns=["body.version", "body.type", "body.content"], inplace=True)
#
#                         df_comment.rename(
#                             columns={"author.accountId": "author", "updateAuthor.accountId": "updateAuthor"},
#                             inplace=True
#                         )
#                         df_comment = cast_columns(df_comment, ['id', 'issue_id'], pd.Int64Dtype())
#                         df_comment = cast_columns(df_comment, ['created', 'updated'], "datetime")
#                         await queue_upsert_issue.put(
#                             {
#                                 "model": IssueComments,
#                                 "df": df_comment,
#                                 "on_conflict_update": True,
#                                 "conflict_condition": ["updated"]
#                             }
#                         )
#
#                         # df_comment[['id', 'issue_id']] = df_comment[['id', 'issue_id']].astype(pd.Int64Dtype())
#                         # df_comment[['created', 'updated']] = df_comment[['created', 'updated']].apply(pd.to_datetime)
#
#                         # upsert(
#                         #     pg_session, IssueComments, df_comment,
#                         #     primary_key="id",
#                         # )
#                         # async with issue_comment_lock:
#                         #     _ = await upsert_async(
#                         #         pg_async_session, IssueComments, df_comment, conflict_condition=["updated"]
#                         #     )
#                 # queue_comment.task_done()
#
#             finally:
#                 async with debug_queue_operation(queue_comment, "task_done", "queue_comment"):
#                     # queue_comment.task_done()
#                     pass
#
#         my_logger.debug(f"Queue size queue_issues: {queue_comment.qsize()}")
#     except Exception as e:
#         handle_exception(e)
#     finally:
#         my_logger.debug(f"{queue_id} consume_comment is done!!!!. Qsize {queue_comment.qsize()}")


# @inject
# async def consume_issue_links_old(
#         queue_id: int,
#         name: str,
#         queue_issue_links: Queue,
#         queue_upsert_issue: Queue,
#         project_key: str,
#         pg_async_session: AsyncSession,
#         # pg_session: Session,
#         my_logger: Logger = Provide[LoggerContainer.logger],
#         # queue_issue_links: asyncio.Queue = Provide[QueueContainer.queue_issue_links],
# ):
#     try:
#         loop_count = 0
#
#         while True:
#             loop_count += 1
#             try:
#                 async with debug_queue_operation(queue_issue_links, "get", "queue_issue_links") as item:
#                     # df_issue_links: pd.DataFrame | None = await queue_issue_links.get()
#                     df_issue_links: pd.DataFrame | None = item
#
#                 if df_issue_links is None:
#                     await queue_upsert_issue.put(None)
#                     break
#
#
#
#                 df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
#
#                 # Drop rows with empty issuelinks
#                 df_issue_links = df_issue_links[df_issue_links['issuelinks'].apply(lambda x: len(x) > 0)]
#                 df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
#
#
#                 if not df_issue_links.empty:
#                     df_issue_links = df_issue_links.explode("issuelinks")
#
#                     df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
#                     df_issue_links = pd.concat(
#                         [
#                             df_issue_links.drop(columns="issuelinks").reset_index(drop=True),
#                             pd.json_normalize(
#                                 df_issue_links['issuelinks'],
#                                 max_level=0
#                             ).drop(columns="self")
#                         ], axis=1
#                     )
#
#                     for col in ['outwardIssue', 'inwardIssue']:
#                         if col in df_issue_links.columns:
#                             df_issue_links = df_issue_links.join(
#                                 pd.json_normalize(
#                                     df_issue_links[col],
#                                 )[['id', 'key']].rename(
#                                     columns={"id": f"{col}_id", "key": f"{col}_key"}
#                                 )
#                             ).drop(columns=col)
#
#                     # columns_to_cast = ['id', 'outwardIssue_id', 'inwardIssue_id', 'issue_id']
#                     # existing_columns = [col for col in columns_to_cast if col in df_issue_links.columns]
#                     # df_issue_links[existing_columns] = df_issue_links[existing_columns].astype(pd.Int64Dtype())
#                     df_issue_links = cast_columns(
#                         df_issue_links,
#                         ['id', 'outwardIssue_id', 'inwardIssue_id', 'issue_id'],
#                         pd.Int64Dtype()
#                     )
#
#                     await queue_upsert_issue.put(
#                         {
#                             "model": IssueLinks,
#                             "df": df_issue_links,
#                         }
#                     )
#                     # ret_value = await upsert_async(
#                     #     pg_async_session, IssueLinks, df_issue_links,
#                     # )
#                     # my_logger.dataframe_utils(f"return value: {ret_value}")
#             finally:
#                 async with debug_queue_operation(queue_issue_links, "task_done", "queue_issue_links"):
#                     # queue_issue_links.task_done()
#                     pass
#
#     except Exception as e:
#         global commit_transaction
#         async with lock:
#             commit_transaction = False
#         handle_exception(e)
#     finally:
#         my_logger.debug(f"Queue size queue_issue_links: {queue_issue_links.qsize()} ")
#         my_logger.info(f"{name} consume_issue_links is done")


# @inject
# async def consume_issue_old(
#     queue_id: int,
#     name: str,
#     queue_issue: Queue,
#     queue_upsert_issue: Queue,
#     project_key: str,
#     pg_async_session,
#     # pg_session,
#     my_logger: Logger = Provide[LoggerContainer.logger],
#     # queue_issue: asyncio.Queue = Provide[QueueContainer.queue_issue],
# ):
#     try:
#         loop_count = 0
#         while True:
#             loop_count += 1
#             try:
#                 async with debug_queue_operation(queue_issue, "get", "queue_issue") as item:
#                     # df: pd.DataFrame | None = await queue_issue.get()
#                     df: pd.DataFrame | None = item
#                 if df is None:
#                     await queue_upsert_issue.put(None)
#                     break
#
#                 df.dropna(how='all', axis=1, inplace=True)
#
#                 if df.shape[0] > 0:
#                     for col in [
#                         'components', 'fixVersions', 'versions',
#                     ]:
#                         if col in df.columns:
#                             df[col] = df[col].apply(
#                                 lambda x: [value['name'] for value in x] if isinstance(x, list) else None)
#
#                         # Define a condition to check if 'description.type' is not NaN
#                     if 'description.type' in df.columns:
#                         condition = df['description.type'].notna()
#
#                         df.loc[condition, 'description'] = df.loc[condition].apply(
#                             lambda row:
#                             {
#                                 'type': row['description.type'],
#                                 "version": row['description.version'],
#                                 "content": row['description.content']
#                             },
#                             axis=1
#                         )
#                     drop_columns_prefixes = [
#                         'parent.',
#                         'customfield_10001.',
#                         'worklog.',
#                         'comment.comments',
#                         'description.', 'assignee.', 'customfield_10006.',
#                         'customfield_10049.', 'reporter.',
#                         "customfield_10056.",
#                         "customfield_10071.",
#                         "customfield_10078.",
#                         'customfield_10146.',
#                         "customfield_10179.", "issuetype.", "priority.",
#                         "resolution.", 'status.',
#                         'customfield_10092.'
#                     ]
#                     exceptions = {
#                         'parent.id', 'parent.key', 'assignee.accountId', 'customfield_10006.value',
#                         'customfield_10001.name',
#                         'customfield_10049.value', 'reporter.accountId',
#                         "customfield_10056.value",
#                         "customfield_10071.value",
#                         "customfield_10078.value",
#                         'customfield_10146.value',
#                         "customfield_10179.value", 'issuetype.name', 'issuetype.subtask', 'issuetype.hierarchyLevel',
#                         'priority.name',
#                         'resolution.name', 'status.name', 'status.statusCategory.name',
#                         'customfield_10092.value'
#                     }
#
#                     df.drop(
#                         columns=[
#                             col for col in df.columns if any(map(col.startswith, drop_columns_prefixes))
#                                                          and col not in exceptions
#                         ],
#
#                         inplace=True
#                     )
#
#                     # col_prefix_rename = [
#                     #     'aggregateprogress.'
#                     # ]
#
#                     column_rename_map = {
#                         'aggregateprogress.percent': 'aggregateprogress_percent',
#                         'aggregateprogress.progress': 'aggregateprogress_progress',
#                         'aggregateprogress.total': 'aggregateprogress_total',
#                         'progress.percent': 'progress_percent',
#                         'progress.progress': 'progress_progress',
#                         'progress.total': 'progress_total',
#                         'assignee.accountId': 'assignee', 'reporter.accountId': "reporter",
#                         "customfield_10006.value": "change_risk",
#                         'customfield_10049.value': "severity",
#                         'customfield_10015': 'startdate',
#                         'customfield_10019': 'Rank',
#                         "customfield_10056.value": "category_type",
#                         "customfield_10071.value": "initiated_by",
#                         "customfield_10078.value": 'approvalstatus',
#                         'customfield_10146.value': 'reqfinalized',
#                         "customfield_10179.value": "qc_check",
#                         'customfield_10059': 'testcaseno',
#                         'customfield_10060': 'testcasesuite',
#                         'customfield_10061': 'teststepno',
#                         'customfield_10062': 'scenariono',
#                         'customfield_10067': 'ClientJira',
#                         'customfield_10120': 'totaleffort',
#                         'customfield_10121': 'totaldeveffort',
#                         'customfield_10122': 'baeffort',
#                         'customfield_10123': 'adeffort',
#                         'customfield_10124': 'rdeffort',
#                         'customfield_10125': 'qaeffort',
#                         'customfield_10126': 'contingency',
#                         "customfield_10147": "reopen_count",
#                         "customfield_10256": "initiative_detail",
#                         'issuetype.name': 'issuetype', 'priority.name': 'priority',
#                         'resolution.name': "resolution", 'status.name': "status",
#                         'issuetype.subtask': 'isSubTask',
#                         'status.statusCategory.name': 'statusCategory',
#                         'parent.id': 'parent_id',
#                         'parent.key': 'parent_key',
#                         "customfield_10199": "cvss_score",
#                         'customfield_10024': 'storypoints',
#                         'customfield_10020': 'sprint',
#                         'customfield_10092.value': 'urgency'
#
#                     }
#
#                     # Update specific mappings if needed
#                     column_rename_map.update({
#                         'customfield_10001.name': 'Team',
#                         'issuetype.hierarchyLevel': 'issue_hierarchy_level'
#                     })
#
#                     df.rename(columns=column_rename_map, inplace=True)
#
#                     df_initiative_attribute = df[df['issuetype'] == "Initiative"].copy()
#                     my_logger.debug(f"{queue_id} df_initiative_attribute size = {df_initiative_attribute.shape[0]}")
#
#                     if df_initiative_attribute.shape[0] > 0:
#                         required_columns = [
#                             'id', 'key', 'customfield_10182', 'customfield_10183', 'customfield_10184',
#                             'created', 'updated'
#                         ]
#
#                         # Check if the required columns are present in the DataFrame
#                         missing_columns = [col for col in required_columns if
#                                            col not in df_initiative_attribute.columns]
#
#                         # Add missing columns with NaN values if they are not present
#                         for col in missing_columns:
#                             df_initiative_attribute[col] = float('nan')
#
#                         df_initiative_attribute = df_initiative_attribute[required_columns]
#
#                         df_initiative_attribute.rename(
#                             columns={
#                                 'id': 'initiative_id',
#                                 'key': 'initiative_key',
#                                 'customfield_10182': 'project',
#                                 'customfield_10183': 'release',
#                                 'customfield_10184': 'feature'
#                             }, inplace=True
#                         )
#
#                         df_initiative_attribute = cast_columns(df_initiative_attribute, ['initiative_id'],
#                                                                pd.Int64Dtype())
#
#                         # df_initiative_attribute['initiative_id'] = (
#                         #     df_initiative_attribute['initiative_id'].astype(pd.Int64Dtype())
#                         # )
#
#                         # List of columns to drop
#                         columns_to_drop = ["customfield_10182", "customfield_10183", "customfield_10184"]
#
#                         # Check which columns actually exist in the DataFrame
#                         existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
#
#                         # Drop only the existing columns
#                         if existing_columns_to_drop:
#                             df.drop(columns=existing_columns_to_drop, inplace=True)
#
#                     df = cast_columns(
#                         df,
#                         [
#                             'adeffort', 'aggregateprogress_percent', 'aggregateprogress_progress',
#                             'aggregateprogress_total',
#                             'aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent', 'baeffort',
#                             'contingency', 'cvss_score', 'progress_percent', 'progress_progress', 'progress_total',
#                             'qaeffort',
#                             'rdeffort', 'timeestimate', 'timeoriginalestimate', 'timespent', 'totaldeveffort',
#                             'totaleffort'
#
#                         ],
#                         pd.Float64Dtype()
#                     )
#
#                     columns_to_cast = ['id', 'parent_id', 'reopen_count', 'storypoints', 'issue_hierarchy_level']
#
#                     for col in columns_to_cast:
#                         if col in df.columns:
#                             # Check for non-numeric values
#                             if df[col].dtype == 'object':
#                                 non_numeric = df[col][pd.to_numeric(df[col], errors='coerce').isna() & df[col].notna()]
#
#                                 df[col] = df[col].astype(str).str.strip()  # Remove whitespace
#                                 df[col] = df[col].replace(['', 'nan', 'null', 'None'], pd.NA)  # Replace empty strings
#                                 df[col] = df[col].replace(r'\.0$', '', regex=True)  # Remove trailing .0
#                             df[col] = pd.to_numeric(df[col], errors='coerce')
#
#                     # Then cast to Int64 (nullable integer)
#                     # Cast only existing columns
#                     cast_dict = {
#                         'id': 'Int64',
#                         'parent_id': 'Int64',
#                         'reopen_count': 'Int64',
#                         'storypoints': 'Int64',
#                         'issue_hierarchy_level': 'Int64'
#                     }
#                     existing_cast_dict = {k: v for k, v in cast_dict.items() if k in df.columns}
#                     try:
#                         df = df.astype(existing_cast_dict)
#                     except TypeError as e:
#                         my_logger.error(f"Failed to cast columns: {e}")
#                         _ = await quick_save_async(
#                             df,
#                             f"df_issue_error_{name}_{loop_count}.xlsx",
#                             path=f"c:/vishal/log/issue/{name}_{loop_count}"
#                         )
#
#
#                     df['description_markdown'] = df['description_markdown'].apply(
#                         lambda x: markdownify.markdownify(x, heading_style='ATX')
#                     )
#
#                     timetracking_columns = [
#                         'timetracking.timeSpent',
#                         'timetracking.remainingEstimate',
#                         'timetracking.originalEstimate',
#                         'timetracking.timeSpentSeconds',
#                         'timetracking.remainingEstimateSeconds',
#                         'timetracking.originalEstimateSeconds'
#                     ]
#
#                     missing_columns = [col for col in timetracking_columns if col not in df.columns]
#                     if missing_columns:
#                         my_logger.debug(f"Missing columns: {missing_columns}")
#
#                     for col in missing_columns:
#                         df[col] = None  # or any default value
#
#                     df['timetracking'] = df[timetracking_columns].apply(
#                         lambda row: {} if row.isna().all() else {
#                             timetracking.split('.')[1]: row[timetracking]
#                             for timetracking in timetracking_columns
#                             if pd.notna(row[timetracking]) and row[timetracking] is not None
#
#                         },
#                         axis=1
#                     )
#
#                     # Drop the original timetracking columns
#                     df.drop(columns=timetracking_columns, inplace=True)
#
#                     # Convert the 'timetracking' column to JSON format
#                     # df['timetracking'] = df['timetracking'].apply(json.dumps)
#
#                     # REFERENCE CODE
#                     # Define a wrapper function to call `upsert`
#                     # async def run_upsert():
#                     #     if df.shape[0] > 0:
#                     #         # noinspection PyTypeChecker
#                     #         await asyncio.to_thread(
#                     #             upsert_single,
#                     #             pg_session, Issue, df, primary_key="id",
#                     #             no_update_cols=("tscv_summary_description",),
#                     #             on_conflict_update=True
#                     #         )
#                     #
#                     #     if df_initiative_attribute.shape[0] > 0:
#                     #         my_logger.dataframe_utils(f"doing upsert {df_initiative_attribute.shape[0]}")
#                     #         # noinspection PyTypeChecker
#                     #         await asyncio.to_thread(
#                     #             upsert,
#                     #             pg_session, InitiativeAttribute, df_initiative_attribute, primary_key="initiative_id",
#                     #             no_update_cols=("attr",),
#                     #             on_conflict_update=True
#                     #         )
#                     #     return
#
#                     # Call the wrapper function with `await`
#                     try:
#                         # await run_upsert()
#
#                         # List of columns to convert to datetime
#                         datetime_columns = ['statuscategorychangedate', 'resolutiondate', 'created', 'updated']
#
#                         # Filter columns to include only those present in the DataFrame
#                         datetime_columns = [col for col in datetime_columns if col in df.columns]
#
#                         # Apply pd.to_datetime with errors='coerce' to each column
#                         df[datetime_columns] = df[datetime_columns].apply(pd.to_datetime, errors='coerce')
#
#                         for col in ['startdate', 'duedate']:
#                             if col in df.columns:
#                                 df[col] = pd.to_datetime(df[col], errors="coerce").dt.date
#
#                         # df = reduce_mem_usage(df)
#                         conflict_condition = and_(Issue.updated < insert(Issue.__table__).excluded.updated)
#                         # my_logger.info(f"conflict_condition type = {type(conflict_condition)}")
#                         # my_logger.dataframe_utils(f"{conflict_condition}")
#
#                         # ret_value = await upsert_async(
#                         #     pg_async_session, Issue, df,
#                         #     no_update_cols=("tscv_summary_description",),
#                         #     on_conflict_update=True,
#                         #     conflict_condition=["updated"]
#                         # )
#                         await queue_upsert_issue.put(
#                             {
#                                 "model": Issue,
#                                 "df": df,
#                                 "no_update_cols": ("tscv_summary_description",),
#                                 "on_conflict_update": True,
#                                 "conflict_condition": ["updated"]
#                             }
#                         )
#
#                         if df_initiative_attribute.shape[0] > 0:
#                             conflict_condition = and_(
#                                 InitiativeAttribute.updated < insert(InitiativeAttribute.__table__).excluded.updated
#                             )
#                             df_initiative_attribute['created'] = pd.to_datetime(df_initiative_attribute['created'])
#                             df_initiative_attribute['updated'] = pd.to_datetime(df_initiative_attribute['updated'])
#                             await queue_upsert_issue.put(
#                                 {
#                                     "model": InitiativeAttribute,
#                                     "df": df_initiative_attribute,
#                                     "primary_key": "initiative_id",
#                                     "no_update_cols": ("attr",),
#                                     "on_conflict_update": True,
#                                     "conflict_condition": conflict_condition
#                                 }
#                             )
#
#                             # ret_value = await upsert_async(
#                             #     pg_async_session, InitiativeAttribute, df_initiative_attribute,
#                             #     no_update_cols=("attr",),
#                             #     on_conflict_update=True,
#                             #     conflict_condition=conflict_condition
#                             # )
#                             # my_logger.dataframe_utils(f"result InitiativeAttribute = {ret_value}")
#                     finally:
#                         my_logger.debug(f"Queue size queue_issue: {queue_issue.qsize()}")
#             finally:
#                 async with debug_queue_operation(queue_issue, "task_done", "queue_issue"):
#                     pass
#                     # queue_issue.task_done()
#             my_logger.debug(f"{queue_id} marking queue_issue done.")
#
#     except Exception as e:
#         handle_exception(e)
#     finally:
#         my_logger.debug(f"Queue size queue_issue: {queue_issue.qsize()}")
#         my_logger.debug(f"{queue_id} consume_issue is done")
def check_progress(percent=0, width=30):
    left = width * percent // 100
    right = width - left
    print('\r[', '#' * int(left), ' ' * int(right), ']', f' {percent:.1f}%', sep='', end='', flush=True)


def update_progress(
        start, total, time_taken: float = 0, cummulative_time: int = None,
        average_time: float = None
):
    # Source: https://stackoverflow.com/questions/3002085/python-to-print-out-status-bar-and-percentage
    # Modify this to change the length of the progress bar
    bar_length = 30
    status = ""

    if total == 0:
        progress = 0
    else:
        progress = round(start / total, 3)

    if isinstance(progress, int):
        progress = float(progress)

    if not isinstance(progress, float):
        progress = 0
        status = "error: progress var must be float\r\n"

    if progress < 0:
        progress = 0
        status = "Halt...\r\n"

    if progress >= 1:
        progress = 1
        status = "Done...\r\n"
    block = int(round(bar_length * progress))
    # text = ("\rPercent: [{0}] {1}% {2} {3} of {4} processed".
    #         format("=" * block + " " * (barLength - block), round((progress * 100), 3),
    #                status, start, total
    #                )
    #         )
    text_new = (
        f"\r[{'=' * block}{' ' * (bar_length - block)}] "
        f"{round(progress * 100, 3)}% {status} ({start} of {total} processed in {time_taken:.1f} sec)"
    )
    if cummulative_time:
        text_new += f" Total Time: {format_time_difference(cummulative_time)}"
    if average_time:
        text_new += f" Avg. Time: {average_time:.2f} sec"
    sys.stdout.write(text_new)
    sys.stdout.flush()


class GlobalCircuitBreaker:
    """
    Enhanced global circuit breaker that coordinates different error types across multiple async processes.
    Features cancellable sleep, error classification, and health monitoring.
    Thread-safe and works on Windows/Unix.
    """

    @inject
    def __init__(
            self, config: CircuitBreakerConfig = None,
            logger_circuit: logging.Logger = Provide[LoggerContainer.logger]

    ):
        self.config = config or CircuitBreakerConfig()
        self.logger = logger_circuit
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0
        self._half_open_calls = 0

        # Track different error types separately
        self._consecutive_rate_limits = 0
        self._connection_pool_errors = 0
        self._network_errors = 0

        # Enhanced asyncio events for coordination with cancellation support
        # Event is SET when circuit is healthy/recovered, CLEARED when circuit is open
        self._circuit_healthy_event = asyncio.Event()
        self._circuit_healthy_event.set()  # Start in healthy state

        # Cancellable sleep events for different error types
        self._rate_limit_recovery_event = asyncio.Event()
        self._rate_limit_recovery_event.set()  # Start ready
        self._connection_pool_recovery_event = asyncio.Event()
        self._connection_pool_recovery_event.set()  # Start ready
        self._network_recovery_event = asyncio.Event()
        self._network_recovery_event.set()  # Start ready

        # Global rate limit coordination
        self._rate_limit_warning_event = asyncio.Event()
        self._rate_limit_warning_until = 0
        self._global_rate_limit_backoff = 0

        # Lock for thread safety
        self._lock = asyncio.Lock()

        # Rate limiting coordination
        self._active_requests = 0
        self._backoff_until = 0

        # Connection pool monitoring
        self._active_connections = 0
        self._connection_pool_backoff_until = 0

        # Network error tracking
        self._network_backoff_until = 0
        self._network_retry_multiplier = 1

        # Health monitoring
        self._health_check_task = None
        self._shutdown_event = asyncio.Event()
        self._start_health_monitoring()

    def _start_health_monitoring(self):
        """Start the health monitoring task."""
        if self._health_check_task is None or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_monitor())

    async def _health_monitor(self):
        """
        Health monitoring task that runs periodically to check system health
        and proactively manage circuit state.
        """
        asyncio.current_task().set_name("GlobalCircuitBreaker._health_monitor")
        try:
            from dags.data_pipeline.utils.async_utils import cancellable_sleep

            # Track idle time for auto-shutdown
            last_activity_time = time.time()
            idle_shutdown_threshold = 60.0  # Shutdown after 60 seconds of no activity

            while not self._shutdown_event.is_set():
                # Use cancellable sleep instead of wait_for
                try:
                    sleep_completed = await cancellable_sleep(
                        self.config.health_check_interval,
                        self._shutdown_event
                    )
                except asyncio.CancelledError:
                    break

                if not sleep_completed:
                    # Shutdown event was set
                    break

                # Check if there are any active connections or requests
                current_time = time.time()
                if self._active_connections == 0 and self._active_requests == 0:
                    # No active connections or requests
                    if current_time - last_activity_time > idle_shutdown_threshold:
                        self.logger.info(
                            f"No active connections or requests for {idle_shutdown_threshold} seconds. "
                            f"Triggering graceful shutdown of health check."
                        )
                        self._shutdown_event.set()
                        break
                else:
                    # Reset last activity time if there are active connections
                    last_activity_time = current_time

                await self._perform_health_check()

        except asyncio.CancelledError:
            self.logger.debug("Health monitor cancelled")
            raise  # Re-raise to properly handle cancellation
        except Exception as e:
            self.logger.error(f"Health monitor error: {e}")

    async def _perform_health_check(self):
        """Perform health checks and update circuit state accordingly."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._perform_health_check")
        try:
            import psutil
            import gc
            asyncio.current_task().set_name("_perform_health_check")

            # Check system resources
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent(interval=1)

            # Check asyncio task count
            all_tasks = asyncio.all_tasks()
            active_task_count = len([t for t in all_tasks if not t.done()])

            self.logger.debug(
                f"Health check - Memory: {memory_percent:.1f}%, "
                f"CPU: {cpu_percent:.1f}%, Active tasks: {active_task_count}, "
                f"Active connections: {self._active_connections}"
            )

            # Proactive circuit management based on system load
            should_activate_warning = False
            high_connection_usage = False
            current_time = time.time()

            async with self._lock:
                if memory_percent > 95 or cpu_percent > 90:
                    if self._state == CircuitState.CLOSED:
                        # Check if rate limit warning is already active
                        if current_time >= self._rate_limit_warning_until:
                            should_activate_warning = False
                            self.logger.debug(
                                f"High system load detected, no active warning. "
                                f"Current time: {current_time}, warning until: {self._rate_limit_warning_until}"
                            )
                        else:
                            self.logger.debug(
                                f"High system load detected, but rate limit warning already active until "
                                f"{datetime.fromtimestamp(self._rate_limit_warning_until, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}. "
                                f"Skipping new warning activation."
                            )

                # Check for connection pool pressure
                if self._active_connections > self.config.max_concurrent_connections * 0.8:
                    high_connection_usage = True

            # Handle actions outside the lock to avoid deadlock
            if should_activate_warning:
                self.logger.warning(
                    f"High system load detected (Memory: {memory_percent:.1f}%, "
                    f"CPU: {cpu_percent:.1f}%). Activating rate limit warning."
                )
                await self._activate_rate_limit_warning(5.0)  # 5 second pause

            if high_connection_usage:
                self.logger.warning(
                    f"High connection usage: {self._active_connections}/"
                    f"{self.config.max_concurrent_connections}"
                )

        except ImportError:
            # psutil not available, skip system monitoring
            pass
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")

    def classify_error(self, error: Exception) -> ErrorType:
        """
        Classify an error to determine appropriate handling strategy.

        Args:
            error: The exception to classify

        Returns:
            ErrorType: The classification of the error
        """
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()

        # Rate limit errors
        if "rate limit" in error_str or "429" in error_str or "retry-after" in error_str:
            return ErrorType.RATE_LIMIT

        # Connection pool exhaustion
        if any(keyword in error_str for keyword in [
            "connection pool", "too many connections", "pool exhausted",
            "connection limit", "connector limit"
        ]) or any(keyword in error_type for keyword in [
            "connectionpoolerror", "poolexhaustederror"
        ]):
            return ErrorType.CONNECTION_POOL

        # Network/connectivity issues
        if any(keyword in error_str for keyword in [
            "connection timeout", "connection reset", "connection refused",
            "network", "dns", "ssl", "certificate", "handshake"
        ]) or any(keyword in error_type for keyword in [
            "connectiontimeouterror", "clientconnectionreseterror",
            "clientconnectionerror", "timeouterror", "sslerror"
        ]):
            return ErrorType.NETWORK

        # Service errors (HTTP 5xx, service unavailable)
        if any(keyword in error_str for keyword in [
            "503", "502", "504", "500", "service unavailable",
            "internal server error", "bad gateway"
        ]):
            return ErrorType.SERVICE

        # Default to service error for unknown errors
        return ErrorType.SERVICE

    async def can_execute(self) -> bool:
        """Check if request can be executed based on circuit state and error-specific backoffs."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.can_execute")
        async with self._lock:
            current_time = time.time()

            if self._state == CircuitState.OPEN:
                if current_time - self._last_failure_time > self.config.recovery_timeout:
                    self._state = CircuitState.HALF_OPEN
                    self._half_open_calls = 0
                    # Don't set healthy event yet - wait for successful requests
                    self.logger.debug("can_execute: Circuit breaker transitioning to HALF_OPEN state.")
                    return True
                return False

            elif self._state == CircuitState.HALF_OPEN:
                if self._half_open_calls >= self.config.half_open_max_calls:
                    return False
                return True

            # CLOSED state - check for different types of backoffs

            # Check for active rate limiting
            if current_time < self._backoff_until:
                self.logger.debug(f"can_execute: Rate limited. Waiting until {self._backoff_until}.")
                return False

            # Check for global rate limit warning
            if current_time < self._rate_limit_warning_until:
                self.logger.debug(f"can_execute: Global rate limit warning active. Waiting until {self._rate_limit_warning_until}.")
                return False

            # Check for connection pool backoff
            if current_time < self._connection_pool_backoff_until:
                self.logger.debug(f"can_execute: Connection pool backoff active. Waiting until {self._connection_pool_backoff_until}.")
                return False

            # Check for network error backoff
            if current_time < self._network_backoff_until:
                self.logger.debug(f"can_execute: Network error backoff active. Waiting until {self._network_backoff_until}.")
                return False

            return True


    async def record_success(self):
        """Record successful request and trigger recovery events."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.record_success")
        async with self._lock:
            if self._state == CircuitState.HALF_OPEN:
                self._failure_count = 0
                self._consecutive_rate_limits = 0
                self._state = CircuitState.CLOSED
                self._circuit_healthy_event.set()  # Signal circuit is healthy
                self.logger.debug(f"record_success: state= {self._state}")
            elif self._state == CircuitState.OPEN:
                # Allow immediate recovery from OPEN state on success
                self._failure_count = 0
                self._consecutive_rate_limits = 0
                self._state = CircuitState.CLOSED
                self._circuit_healthy_event.set()  # Signal circuit is healthy
                self.logger.debug(f"record_success: Circuit recovered from OPEN to CLOSED")
            elif self._state == CircuitState.CLOSED:
                self._failure_count = max(0, self._failure_count - 1)
                self._consecutive_rate_limits = 0  # Reset on success
                self.logger.debug(f"record_success: _failure_count = {self._failure_count}")

            # Reset error counters and trigger recovery events on success
            self._connection_pool_errors = max(0, self._connection_pool_errors - 1)
            self._network_errors = max(0, self._network_errors - 1)
            self._network_retry_multiplier = max(1, self._network_retry_multiplier - 1)

            # Always trigger recovery events on success to unblock waiting processes
            current_time = time.time()

            # Clear all backoff times and set recovery events
            self._backoff_until = 0
            self._rate_limit_warning_until = 0
            self._connection_pool_backoff_until = 0
            self._network_backoff_until = 0

            # Set all recovery events to unblock waiting processes
            self._rate_limit_recovery_event.set()
            self._connection_pool_recovery_event.set()
            self._network_recovery_event.set()


    async def record_error(self, error: Exception, retry_delay: float = 0):
        """
        Record an error with appropriate handling based on error type.

        Args:
            error: The exception that occurred
            retry_delay: Delay in milliseconds for rate limit errors
        """
        asyncio.current_task().set_name("GlobalCircuitBreaker.record_error")
        error_type = self.classify_error(error)

        async with self._lock:
            current_time = time.time()

            if error_type == ErrorType.RATE_LIMIT:
                await self._handle_rate_limit_error(retry_delay, current_time)
            elif error_type == ErrorType.CONNECTION_POOL:
                await self._handle_connection_pool_error(current_time)
            elif error_type == ErrorType.NETWORK:
                await self._handle_network_error(current_time)
            elif error_type == ErrorType.SERVICE:
                await self._handle_service_error(current_time)
            else:
                # Unrecoverable error - trigger shutdown
                await self._handle_unrecoverable_error(error)

    async def _handle_rate_limit_error(self, retry_delay: float, current_time: float):
        """Handle rate limit errors - processes should wait and resume."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._handle_rate_limit_error")
        self._backoff_until = current_time + (retry_delay / 1000)
        self._consecutive_rate_limits += 1

        # Clear the rate limit recovery event to make processes wait
        self._rate_limit_recovery_event.clear()

        # Schedule recovery event
        asyncio.create_task(self._schedule_recovery_event(
            retry_delay / 1000,
            self._rate_limit_recovery_event,
            "rate limit backoff"
        ))

        # Only open circuit if we have excessive consecutive rate limits
        if self._consecutive_rate_limits >= self.config.max_consecutive_rate_limits:
            self._state = CircuitState.OPEN
            self._last_failure_time = current_time
            self._circuit_healthy_event.clear()
            self.logger.warning(f"Circuit breaker OPEN due to {self._consecutive_rate_limits} consecutive rate limits")
        else:
            self.logger.debug(f"Rate limit hit. Backing off until {datetime.fromtimestamp(self._backoff_until, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}")

    async def _handle_connection_pool_error(self, current_time: float):
        """Handle connection pool errors - retry with exponential backoff."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._handle_connection_pool_error")
        self._connection_pool_errors += 1
        backoff_delay = min(30.0, self.config.connection_pool_retry_multiplier ** self._connection_pool_errors)
        self._connection_pool_backoff_until = current_time + backoff_delay

        # Clear the connection pool recovery event
        self._connection_pool_recovery_event.clear()

        # Schedule recovery event
        asyncio.create_task(self._schedule_recovery_event(
            backoff_delay,
            self._connection_pool_recovery_event,
            "connection pool backoff"
        ))

        self.logger.warning(f"Connection pool error #{self._connection_pool_errors}. Backing off for {backoff_delay:.1f} seconds")

    async def _handle_network_error(self, current_time: float):
        """Handle network errors - retry with increased attempts."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._handle_network_error")
        self._network_errors += 1
        self._network_retry_multiplier = min(4, self._network_retry_multiplier * self.config.network_error_retry_multiplier)
        backoff_delay = min(60.0, 5.0 * self._network_retry_multiplier)
        self._network_backoff_until = current_time + backoff_delay

        # Clear the network recovery event
        self._network_recovery_event.clear()

        # Schedule recovery event
        asyncio.create_task(self._schedule_recovery_event(
            backoff_delay,
            self._network_recovery_event,
            "network backoff"
        ))

        self.logger.warning(f"Network error #{self._network_errors}. Retry multiplier: {self._network_retry_multiplier}, backing off for {backoff_delay:.1f} seconds")

    async def _handle_service_error(self, current_time: float):
        """Handle service errors - standard circuit breaker logic."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._handle_service_error")
        self._failure_count += 1
        self._consecutive_rate_limits = 0  # Reset since we got a response

        if self._failure_count >= self.config.failure_threshold:
            self._state = CircuitState.OPEN
            self._last_failure_time = current_time
            self._circuit_healthy_event.clear()
            self.logger.warning(f"Circuit breaker OPEN due to {self._failure_count} service failures")

    async def _handle_unrecoverable_error(self, error: Exception):
        asyncio.current_task().set_name("GlobalCircuitBreaker._handle_unrecoverable_error")
        """Handle unrecoverable errors - trigger graceful shutdown."""
        self.logger.error(f"Unrecoverable error detected: {error}")
        await self.trigger_graceful_shutdown(f"Unrecoverable error: {error}")

    async def record_rate_limit(self, retry_delay: float):
        """Legacy method - redirect to new error handling."""
        asyncio.current_task().set_name("record_rate_limit")
        await self.record_error(Exception("Rate limit"), retry_delay)


    async def record_failure(self, error: Exception = None):
        """Record actual service failures (not rate limits)."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.record_failure")
        if error:
            await self.record_error(error)
        else:
            # Legacy behavior for backward compatibility
            await self.record_error(Exception("Service failure"))


    async def wait_for_recovery(self, timeout: Optional[float] = None, error_type: Optional[ErrorType] = None):
        """
        Wait for circuit breaker to recover with cancellable sleep support.

        Args:
            timeout: Maximum time to wait for recovery
            error_type: Specific error type to wait for (if known)
        """
        asyncio.current_task().set_name("GlobalCircuitBreaker.wait_for_recovery")

        # If error type is specified, wait for specific recovery
        if error_type:
            await self._wait_for_specific_recovery(error_type, timeout)
            return

        # General recovery waiting - check all conditions
        await self._wait_for_general_recovery(timeout)

    async def _wait_for_specific_recovery(self, error_type: ErrorType, timeout: Optional[float]):
        """Wait for recovery from a specific error type."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._wait_for_specific_recovery")
        if error_type == ErrorType.RATE_LIMIT:
            await self._wait_with_cancellation(
                self._rate_limit_recovery_event,
                timeout,
                "rate limit recovery"
            )
        elif error_type == ErrorType.CONNECTION_POOL:
            await self._wait_with_cancellation(
                self._connection_pool_recovery_event,
                timeout,
                "connection pool recovery"
            )
        elif error_type == ErrorType.NETWORK:
            await self._wait_with_cancellation(
                self._network_recovery_event,
                timeout,
                "network recovery"
            )
        else:
            # For service errors, wait for circuit recovery
            await self._wait_with_cancellation(
                self._circuit_healthy_event,
                timeout,
                "circuit recovery"
            )

    async def _wait_for_general_recovery(self, timeout: Optional[float]):
        """Wait for general recovery - check all backoff conditions."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._wait_for_general_recovery")
        start_time = time.time()

        while True:
            current_time = time.time()

            # Check if we've exceeded timeout
            if timeout and (current_time - start_time) >= timeout:
                self.logger.debug("wait_for_recovery: Timeout reached")
                break

            # Check circuit state
            if self._state == CircuitState.OPEN:
                try:
                    remaining_timeout = None
                    if timeout:
                        remaining_timeout = timeout - (current_time - start_time)
                        if remaining_timeout <= 0:
                            break

                    await self._wait_with_cancellation(
                        self._circuit_healthy_event,
                        remaining_timeout,
                        "circuit recovery"
                    )
                except asyncio.TimeoutError:
                    # Try to transition to HALF_OPEN if enough time has passed
                    await self._try_transition_to_half_open()
                    break

            # Check all backoff conditions and wait for the earliest to clear
            wait_times = []
            events_to_wait = []

            if current_time < self._backoff_until:
                wait_times.append(self._backoff_until - current_time)
                events_to_wait.append(self._rate_limit_recovery_event)

            if current_time < self._rate_limit_warning_until:
                wait_times.append(self._rate_limit_warning_until - current_time)
                events_to_wait.append(self._rate_limit_recovery_event)

            if current_time < self._connection_pool_backoff_until:
                wait_times.append(self._connection_pool_backoff_until - current_time)
                events_to_wait.append(self._connection_pool_recovery_event)

            if current_time < self._network_backoff_until:
                wait_times.append(self._network_backoff_until - current_time)
                events_to_wait.append(self._network_recovery_event)

            if not wait_times:
                # No backoffs active
                break

            # Wait for the shortest backoff or any recovery event
            min_wait_time = min(wait_times)
            remaining_timeout = None
            if timeout:
                remaining_timeout = timeout - (current_time - start_time)
                if remaining_timeout <= 0:
                    break
                min_wait_time = min(min_wait_time, remaining_timeout)

            try:
                # Wait for either time to pass or any recovery event
                await asyncio.wait_for(
                    self._wait_for_any_event(events_to_wait),
                    timeout=min_wait_time
                )
                # An event was set, check conditions again
                continue
            except asyncio.TimeoutError:
                # Time elapsed, check conditions again
                continue

    async def _wait_for_any_event(self, events: list):
        """Wait for any of the provided events to be set."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._wait_for_any_event")
        if not events:
            # No events to wait for, wait indefinitely
            await asyncio.Event().wait()

        # Create tasks for all events
        tasks = [asyncio.create_task(event.wait()) for event in events]

        try:
            # Wait for the first event to complete
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)

            # Cancel remaining tasks
            for task in pending:
                task.cancel()

        except Exception:
            # Cancel all tasks on error
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise

    async def _wait_with_cancellation(self, event: asyncio.Event, timeout: Optional[float], description: str):
        """Wait for an event with timeout and cancellation support."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._wait_with_cancellation")
        try:
            await asyncio.wait_for(event.wait(), timeout=timeout)
            self.logger.debug(f"wait_for_recovery: {description} completed")
        except asyncio.TimeoutError:
            self.logger.debug(f"wait_for_recovery: Timeout waiting for {description}")

    async def _try_transition_to_half_open(self):
        """Try to transition circuit from OPEN to HALF_OPEN if enough time has passed."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._try_transition_to_half_open")
        current_time = time.time()
        if self._state == CircuitState.OPEN and (current_time - self._last_failure_time > self.config.recovery_timeout):
            async with self._lock:
                if self._state == CircuitState.OPEN:  # Double-check after acquiring lock
                    self._state = CircuitState.HALF_OPEN
                    self._half_open_calls = 0
                    self._circuit_healthy_event.set()
                    self.logger.debug("wait_for_recovery: Manually transitioned circuit to HALF_OPEN state.")

    async def record_rate_limit_warning(self, warning_duration: float = 2.0):
        """Record a rate limit warning and coordinate global backoff."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.record_rate_limit_warning")
        await self._activate_rate_limit_warning(warning_duration)

    async def _activate_rate_limit_warning(self, warning_duration: float):
        """Internal method to activate rate limit warning."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._activate_rate_limit_warning")
        current_time = time.time()
        self.logger.debug(f"record_rate_limit_warning: Activating rate limit warning for {warning_duration} seconds")
        self.logger.debug(f"pause will be until {datetime.fromtimestamp(current_time + warning_duration, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}")

        async with self._lock:
            self.logger.debug(f"lock acquired for rate limit warning activation")

            # Check if warning is already active (double-check inside lock)
            if current_time < self._rate_limit_warning_until:
                self.logger.debug(
                    f"Rate limit warning already active until "
                    f"{datetime.fromtimestamp(self._rate_limit_warning_until, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}. "
                    f"Skipping duplicate activation."
                )
                return

            self._rate_limit_warning_until = current_time + warning_duration
            self._rate_limit_warning_event.set()
            self._rate_limit_recovery_event.clear()  # Make processes wait

            self.logger.warning(
                f"Global rate limit warning activated. All threads will pause until {datetime.fromtimestamp(self._rate_limit_warning_until, tz=ZoneInfo('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M:%S %z')}."
            )

            # Schedule recovery event
            asyncio.create_task(self._schedule_recovery_event(
                warning_duration,
                self._rate_limit_recovery_event,
                "rate limit warning"
            ))

    async def _schedule_recovery_event(self, delay: float, event: asyncio.Event, description: str):
        """Schedule a recovery event to be set after a delay."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._schedule_recovery_event")
        try:
            self.logger.debug(f"Scheduling recovery event for {description} in {delay} seconds")
            await asyncio.sleep(delay)
            event.set()
            self.logger.debug(f"Recovery event set for {description}")
        except asyncio.CancelledError:
            self.logger.debug(f"Recovery event scheduling cancelled for {description}")
        except Exception as e:
            self.logger.error(f"Error in recovery event scheduling for {description}: {e}")

    async def _schedule_all_recovery_events(self):
        """Schedule recovery events for all active backoffs."""
        asyncio.current_task().set_name("GlobalCircuitBreaker._schedule_all_recovery_events")
        current_time = time.time()

        # Schedule rate limit recovery
        if current_time < self._backoff_until:
            delay = self._backoff_until - current_time
            asyncio.create_task(self._schedule_recovery_event(
                delay,
                self._rate_limit_recovery_event,
                "rate limit backoff"
            ))

        # Schedule rate limit warning recovery
        if current_time < self._rate_limit_warning_until:
            delay = self._rate_limit_warning_until - current_time
            asyncio.create_task(self._schedule_recovery_event(
                delay,
                self._rate_limit_recovery_event,
                "rate limit warning"
            ))

        # Schedule connection pool recovery
        if current_time < self._connection_pool_backoff_until:
            delay = self._connection_pool_backoff_until - current_time
            asyncio.create_task(self._schedule_recovery_event(
                delay,
                self._connection_pool_recovery_event,
                "connection pool backoff"
            ))

        # Schedule network recovery
        if current_time < self._network_backoff_until:
            delay = self._network_backoff_until - current_time
            asyncio.create_task(self._schedule_recovery_event(
                delay,
                self._network_recovery_event,
                "network backoff"
            ))

    @inject
    async def enter_request(self, logger: logging.Logger = Provide[LoggerContainer.logger]):
        """Enter a request context (for half-open state tracking and connection monitoring)."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.enter_request")
        async with self._lock:
            self._active_requests += 1
            self._active_connections += 1
            if self._state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
            logger.debug(f"enter_request: Entered request context. Active requests: {self._active_requests}, Active connections: {self._active_connections}")

    @inject
    async def exit_request(self, logger: logging.Logger = Provide[LoggerContainer.logger]):
        """Exit a request context."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.exit_request")
        async with self._lock:
            self._active_requests = max(0, self._active_requests - 1)
            self._active_connections = max(0, self._active_connections - 1)
            logger.debug(f"exit_request: Exited request context. Active requests: {self._active_requests}, Active connections: {self._active_connections}")

    async def get_circuit_status(self) -> dict:
        """Get comprehensive circuit breaker status for monitoring."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.get_circuit_status")
        async with self._lock:
            current_time = time.time()
            return {
                "state": self._state.value,
                "failure_count": self._failure_count,
                "consecutive_rate_limits": self._consecutive_rate_limits,
                "connection_pool_errors": self._connection_pool_errors,
                "network_errors": self._network_errors,
                "network_retry_multiplier": self._network_retry_multiplier,
                "active_requests": self._active_requests,
                "active_connections": self._active_connections,
                "backoffs": {
                    "rate_limit_until": self._backoff_until,
                    "rate_limit_warning_until": self._rate_limit_warning_until,
                    "connection_pool_until": self._connection_pool_backoff_until,
                    "network_until": self._network_backoff_until,
                },
                "recovery_events": {
                    "circuit_healthy": self._circuit_healthy_event.is_set(),
                    "rate_limit_recovery": self._rate_limit_recovery_event.is_set(),
                    "connection_pool_recovery": self._connection_pool_recovery_event.is_set(),
                    "network_recovery": self._network_recovery_event.is_set(),
                },
                "current_time": current_time
            }

    @property
    def state(self) -> CircuitState:
        return self._state

    @property
    def is_open(self) -> bool:
        return self._state == CircuitState.OPEN

    def is_processing_complete(self) -> bool:
        """
        Check if all processing is complete (no active connections or requests).

        Returns:
            bool: True if no active connections or requests, False otherwise
        """
        return self._active_connections == 0 and self._active_requests == 0

    async def get_activity_status(self) -> Dict[str, Any]:
        """
        Get current activity status for monitoring.

        Returns:
            Dict containing activity metrics
        """
        asyncio.current_task().set_name("GlobalCircuitBreaker.get_activity_status")
        return {
            "active_connections": self._active_connections,
            "active_requests": self._active_requests,
            "circuit_state": self._state.name,
            "health_check_running": self._health_check_task and not self._health_check_task.done(),
            "shutdown_requested": self._shutdown_event.is_set()
        }

    async def trigger_graceful_shutdown(self, reason: str = "Unrecoverable error encountered"):
        """Trigger a graceful shutdown of the application."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.trigger_graceful_shutdown")
        self.logger.error(f"Triggering graceful shutdown: {reason}")

        # Signal shutdown to health monitor
        self._shutdown_event.set()

        # Cancel health monitoring task
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()

        # Set all recovery events to unblock waiting processes
        self._circuit_healthy_event.set()
        self._rate_limit_recovery_event.set()
        self._connection_pool_recovery_event.set()
        self._network_recovery_event.set()

        await shutdown_handler.shutdown()

    async def cleanup(self):
        """Cleanup circuit breaker resources."""
        asyncio.current_task().set_name("GlobalCircuitBreaker.cleanup")
        self.logger.debug("Cleaning up circuit breaker resources")

        # Signal shutdown
        self._shutdown_event.set()

        # Cancel health monitoring
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        # Set all events to unblock any waiting coroutines
        self._circuit_healthy_event.set()
        self._rate_limit_recovery_event.set()
        self._connection_pool_recovery_event.set()
        self._network_recovery_event.set()

        self.logger.debug("Circuit breaker cleanup completed")
