import os
import logging
import logging.config
from asyncio import current_task
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass
from typing import Union, AsyncContextManager, ContextManager

from pykeepass import PyKeePass
from dependency_injector import containers, providers
from pykeepass.entry import Entry
from sqlalchemy import create_engine
from sqlalchemy.engine import URL, Engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, AsyncEngine, async_scoped_session
from sqlalchemy.orm import sessionmaker, Session


def find_entry_by_title(kp: PyKeePass, cc_jira_title: str):
    # Use the cc_jira_title directly
    return kp.find_entries(title=cc_jira_title, first=True)


class GetKeepassEntryDetails:
    def __init__(self, entry: Entry):
        self.entry = entry

    @property
    def username(self):
        return self.entry.username

    @property
    def password(self):
        return self.entry.password

    @property
    def url(self):
        return self.entry.url

    @property
    def driver_name(self):
        return self.entry.get_custom_property("DB_DRIVER")

    @property
    def server_name(self):
        return self.entry.get_custom_property("DB_SERVER_NAME")

    @property
    def server_port(self):
        return self.entry.get_custom_property("DB_SERVER_RW_PORT")

    @property
    def db(self):
        return self.entry.get_custom_property("DB_NAME")


# @dataclass
# class GetKeepassEntryDetailsTest:
#     entry: Entry
#
#     def get_username(self):
#         return self.entry.username
#
#     def get_password(self):
#         return self.entry.password
#
#     def get_url(self):
#         return self.entry.url
#
#     def get_driver_name(self):
#         return self.entry.get_custom_property("DB_DRIVER")
#
#     def get_server_name(self):
#         return self.entry.get_custom_property("DB_SERVER_NAME")
#
#     def get_server_port(self):
#         return self.entry.get_custom_property("DB_SERVER_RW_PORT")
#
#     def get_db(self):
#         return self.entry.get_custom_property("DB_NAME")


# def extract_keepass_params(details):
#     return {
#         'driver_name': details.driver_name,
#         'username': details.username,
#         'password': details.password,
#         'host': details.server_name,
#         'port': details.server_port,
#         'database': details.db
#     }


class ConnectionStringProvider(providers.Provider):
    def __init__(self, details_provider):
        super().__init__()
        self.details_provider = details_provider

    def _provide(self):
        details = self.details_provider()
        return URL.create(
            drivername=details.driver_name,
            username=details.username,
            password=details.password,
            host=details.server_name,
            port=details.server_port,
            database=details.db
        )


def build_connection_string(details: GetKeepassEntryDetails, use_async: bool = False):
    drivername = 'postgresql+asyncpg' if use_async else 'postgresql+psycopg2'
    return URL.create(
        drivername=drivername,
        username=details.username,
        password=details.password,
        host=details.server_name,
        port=details.server_port,
        database=details.db
    )


class DatabaseSessionManager:
    def __init__(self, schema: str):
        self.schema = schema
        self.engine: Engine = None
        self.engine_async: AsyncEngine = None

    def create_engine(self, url: str, is_async: bool) -> Engine | AsyncEngine:
        if is_async:
            self.engine_async = create_async_engine(url, echo=True).execution_options(schema_translate_map={None: self.schema})
            return self.engine_async
        else:
            self.engine = create_engine(url)
            return self.engine.execution_options(schema_translate_map={None: self.schema})

    @contextmanager
    def session(self) -> ContextManager[Session]:
        if not self.engine:
            raise RuntimeError("Engine not created. Call create_engine() first.")
        session_maker = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False
        )
        with session_maker() as session:
            yield session

    @asynccontextmanager
    async def async_session(self) -> AsyncContextManager[AsyncSession]:
        if not self.engine_async:
            raise RuntimeError("Async engine not created. Call create_engine() with is_async=True first.")
        async_session_maker = async_scoped_session(
            sessionmaker(
                bind=self.engine_async,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False
            ), scopefunc=current_task
        )
        async with async_session_maker() as session:
            yield session

    # def create_session(engine, is_async: bool) -> sessionmaker:
    #     if is_async:
    #         return sessionmaker(
    #             bind=engine,
    #             class_=AsyncSession,
    #             autoflush=True,
    #             autocommit=False,
    #             expire_on_commit=False
    #         )
    #     else:
    #         return sessionmaker(
    #             bind=engine,
    #             autoflush=True,
    #             autocommit=False
    #         )


class Container(containers.DeclarativeContainer):
    config = providers.Configuration(yaml_files=["./logging_config.yaml"])

    logging = providers.Resource(
        logging.config.dictConfig,
        config.logging
    )

    keepass_instance = providers.Singleton(
        PyKeePass,
        filename=config.KeePass.KEYPASS_DB,
        keyfile=config.KeePass.KEYPASS_KEY_FILE

    )

    # This provider gets the entry using the keepass_instance
    # cc_jira_entry = providers.Callable(
    #     lambda kp: kp.find_entries(title='corecard Jira', first=True),
    #     keepass_instance
    # )

    # Use the extracted value in the callable function
    cc_jira_entry = providers.Callable(
        find_entry_by_title,
        keepass_instance,
        config.KeePass.CC_JIRA
    )

    pg_jira_rw_entry = providers.Callable(
        find_entry_by_title, keepass_instance, config.KeePass.PG_JIRA_RW
    )

    pg_jira_ro_entry = providers.Callable(
        find_entry_by_title,
        keepass_instance,
        config.KeePass.PG_JIRA_RW
    )

    pg_jira_rw_details = providers.Factory(GetKeepassEntryDetails, pg_jira_rw_entry)
    pg_jira_ro_details = providers.Factory(GetKeepassEntryDetails, pg_jira_ro_entry)

    # Dictionary provider for connection strings
    connection_strings = providers.Dict({
        'jira_ro': providers.Callable(
            build_connection_string,
            pg_jira_ro_details,
            use_async=False
        ),
        'jira_ro_async': providers.Callable(
            build_connection_string,
            pg_jira_ro_details,
            use_async=True
        ),
        'jira_rw': providers.Callable(
            build_connection_string,
            pg_jira_rw_details,
            use_async=False
        ),
        'jira_rw_async': providers.Callable(
            build_connection_string,
            pg_jira_rw_details,
            use_async=True
        )
    })

    # Schema provider
    schema_provider = providers.Configuration()
    # is_async_provider = providers.Configuration()

    # Create DatabaseSessionManager with schema
    database_session_manager = providers.Singleton(
        DatabaseSessionManager,
        schema_provider
    )


if __name__ == "__main__":
    container = Container()
    # container.config.from_yaml("./logging_config.yaml", loader=SafeLoader, envs_required=True)
    container.schema_provider.override('plat')
    # container.is_async_provider.override(False)
    print("schema provider")
    print(container.schema_provider())

    container.init_resources()
    print(container.config()['KeePass'])

    print("-" * 10)
    x = find_entry_by_title(container.keepass_instance(), "corecard Jira")
    print(x.url)
    print("-" * 10)
    print(container.keepass_instance().find_entries(title=container.config()['KeePass']['CC_JIRA'], first=True))
    print(container.pg_jira_rw_entry().get_custom_property("DB_SERVER_NAME"))
    print("driver name")
    print(container.pg_jira_rw_details().driver_name)

    logger = logging.getLogger(os.getenv("ENVIRONMENT", "development"))
    logger.info("Resources are initialized")
    print("user entry")
    container.check_dependencies()


    # Initialize engines
    container.database_session_manager().create_engine(
        url=container.connection_strings()['jira_ro'],
        is_async=False
    )
    container.database_session_manager().create_engine(
        url=container.connection_strings()['jira_ro_async'],
        is_async=True
    )

    # Synchronous session usage
    with container.database_session_manager().session() as session:
        with session.begin():
            # Perform synchronous database operations here
            print(session)




