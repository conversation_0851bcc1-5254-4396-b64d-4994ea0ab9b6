"""
Test to verify that process_upsert_queue shuts down gracefully after all producers and consumers are done.
"""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, MagicMock, patch
from asyncio import Queue

# Import the functions we want to test
from dags.data_pipeline.utility_code import process_upsert_queue
from dags.data_pipeline.containers import CircuitBreakerConfig
from archived.archived_code import GlobalCircuitBreaker


@pytest.mark.asyncio
async def test_process_upsert_queue_timeout_shutdown():
    """Test that process_upsert_queue shuts down after timeout when no termination signals arrive."""
    print("🔧 Testing process_upsert_queue timeout shutdown")
    print("=" * 50)
    
    # Create a mock queue that never returns items
    mock_queue = AsyncMock()
    mock_queue.qsize.return_value = 0
    
    # Mock the priority queue manager to simulate timeout
    with patch('dags.data_pipeline.utility_code.priority_queue_manager') as mock_priority_manager:
        # Make get_priority_message raise TimeoutError to simulate no activity
        mock_priority_manager.get_priority_message.side_effect = asyncio.TimeoutError()
        
        # Mock logger
        mock_logger = MagicMock()
        mock_logger.debug = MagicMock()
        mock_logger.warning = MagicMock()
        mock_logger.info = MagicMock()
        
        # Mock session
        mock_session = AsyncMock()
        
        start_time = time.time()
        
        # This should complete due to timeout, not hang indefinitely
        await process_upsert_queue(
            pg_async_session=mock_session,
            queue_upsert_issue=mock_queue,
            my_logger=mock_logger
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"   Process completed in {elapsed_time:.2f} seconds")
        
        # Should complete within reasonable time (much less than 120 seconds idle timeout)
        assert elapsed_time < 60, f"Process took too long: {elapsed_time:.2f} seconds"
        
        # Verify warning was logged about shutdown due to inactivity
        mock_logger.warning.assert_called()
        warning_calls = [call for call in mock_logger.warning.call_args_list 
                        if "No activity in upsert queue" in str(call)]
        assert len(warning_calls) > 0, "Expected warning about no activity"
        
        print("   ✅ process_upsert_queue shut down correctly due to timeout")


@pytest.mark.asyncio
async def test_process_upsert_queue_partial_termination_signals():
    """Test that process_upsert_queue shuts down when receiving partial termination signals."""
    print("\n🔧 Testing process_upsert_queue with partial termination signals")
    print("=" * 50)
    
    # Create a mock queue
    mock_queue = AsyncMock()
    mock_queue.qsize.return_value = 0
    
    # Create a sequence of responses: some None values, then timeout
    none_responses = [None] * 20  # Only 20 out of expected 25
    timeout_responses = [asyncio.TimeoutError()] * 5  # Then timeouts
    
    response_sequence = none_responses + timeout_responses
    response_iter = iter(response_sequence)
    
    def get_next_response(*args, **kwargs):
        try:
            response = next(response_iter)
            if isinstance(response, Exception):
                raise response
            return response
        except StopIteration:
            raise asyncio.TimeoutError()
    
    with patch('dags.data_pipeline.utility_code.priority_queue_manager') as mock_priority_manager:
        mock_priority_manager.get_priority_message.side_effect = get_next_response
        
        # Mock logger
        mock_logger = MagicMock()
        mock_logger.debug = MagicMock()
        mock_logger.warning = MagicMock()
        mock_logger.info = MagicMock()
        
        # Mock session
        mock_session = AsyncMock()
        
        start_time = time.time()
        
        # This should complete when it gets 20 None values and waits for more
        await process_upsert_queue(
            pg_async_session=mock_session,
            queue_upsert_issue=mock_queue,
            my_logger=mock_logger
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"   Process completed in {elapsed_time:.2f} seconds")
        
        # Should complete within reasonable time
        assert elapsed_time < 60, f"Process took too long: {elapsed_time:.2f} seconds"
        
        # Verify appropriate logs were made
        info_calls = [call for call in mock_logger.info.call_args_list 
                     if "termination signals" in str(call)]
        warning_calls = [call for call in mock_logger.warning.call_args_list 
                        if "Only received" in str(call)]
        
        # Should have logged about receiving partial signals and proceeding with shutdown
        assert len(info_calls) > 0 or len(warning_calls) > 0, "Expected log about termination signals"
        
        print("   ✅ process_upsert_queue handled partial termination signals correctly")


@pytest.mark.asyncio
async def test_health_monitor_auto_shutdown():
    """Test that health monitor shuts down automatically when no activity is detected."""
    print("\n🔧 Testing health monitor auto-shutdown")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=3.0,
        health_check_interval=0.1,  # Very short interval for testing
        max_concurrent_connections=10
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate some activity first
    print("   Simulating initial activity...")
    await circuit_breaker.enter_request()
    await asyncio.sleep(0.2)  # Let health monitor run a few cycles
    await circuit_breaker.exit_request()
    
    # Now wait for auto-shutdown (should happen after 60 seconds of no activity)
    # We'll use a shorter timeout for testing by checking the shutdown event
    print("   Waiting for auto-shutdown...")
    
    start_time = time.time()
    timeout = 65.0  # Slightly longer than the 60-second threshold
    
    while not circuit_breaker._shutdown_event.is_set() and (time.time() - start_time) < timeout:
        await asyncio.sleep(0.5)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"   Health monitor shutdown after {elapsed_time:.2f} seconds")
    
    # Verify shutdown event was set
    assert circuit_breaker._shutdown_event.is_set(), "Health monitor should have shut down automatically"
    
    # Verify it happened within reasonable time (should be around 60 seconds)
    assert 55 < elapsed_time < 70, f"Auto-shutdown took unexpected time: {elapsed_time:.2f} seconds"
    
    print("   ✅ Health monitor shut down automatically after idle period")


@pytest.mark.asyncio
async def test_circuit_breaker_activity_status():
    """Test the new activity status methods in circuit breaker."""
    print("\n🔧 Testing circuit breaker activity status methods")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=3.0,
        max_concurrent_connections=10
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Test initial state
    assert circuit_breaker.is_processing_complete(), "Should be complete initially"
    
    status = await circuit_breaker.get_activity_status()
    assert status["active_connections"] == 0
    assert status["active_requests"] == 0
    assert not status["shutdown_requested"]
    
    print("   Initial status: ✅")
    
    # Simulate activity
    await circuit_breaker.enter_request()
    
    assert not circuit_breaker.is_processing_complete(), "Should not be complete with active request"
    
    status = await circuit_breaker.get_activity_status()
    assert status["active_connections"] == 1
    assert status["active_requests"] == 1
    
    print("   Active status: ✅")
    
    # End activity
    await circuit_breaker.exit_request()
    
    assert circuit_breaker.is_processing_complete(), "Should be complete after ending request"
    
    status = await circuit_breaker.get_activity_status()
    assert status["active_connections"] == 0
    assert status["active_requests"] == 0
    
    print("   Completed status: ✅")
    print("   ✅ Activity status methods work correctly")


if __name__ == "__main__":
    asyncio.run(test_process_upsert_queue_timeout_shutdown())
    asyncio.run(test_process_upsert_queue_partial_termination_signals())
    asyncio.run(test_health_monitor_auto_shutdown())
    asyncio.run(test_circuit_breaker_activity_status())
    print("\n🎉 All tests passed!")
