"""
Test to verify that the health monitoring deadlock is fixed.
"""

import asyncio
import pytest
import time
from unittest.mock import patch, MagicMock

# Import the enhanced circuit breaker
from dags.data_pipeline.containers import (
    CircuitBreakerConfig, CircuitState
)
from archived.archived_code import GlobalCircuitBreaker


@pytest.mark.asyncio
async def test_health_monitoring_no_deadlock():
    """Test that health monitoring doesn't cause deadlock when activating rate limit warning."""
    print("🔧 Testing Health Monitoring Deadlock Fix")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=5.0,
        health_check_interval=0.1,  # Very short interval for testing
        max_concurrent_connections=10
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Mock psutil to simulate high system load
    mock_memory = MagicMock()
    mock_memory.percent = 90.0  # High memory usage
    
    mock_cpu_percent = MagicMock(return_value=95.0)  # High CPU usage
    
    with patch('psutil.virtual_memory', return_value=mock_memory), \
         patch('psutil.cpu_percent', mock_cpu_percent):
        
        # Let the health monitor run for a short time
        print("   Starting health monitor with high system load simulation...")
        await asyncio.sleep(0.5)  # Let health check run
        
        # Verify that the circuit breaker is still responsive
        can_execute = await circuit_breaker.can_execute()
        print(f"   ✅ Circuit breaker responsive: can_execute = {can_execute}")
        
        # Try to record an error to ensure no deadlock
        start_time = time.time()
        await circuit_breaker.record_error(Exception("Test error"))
        elapsed = time.time() - start_time
        
        print(f"   ✅ record_error completed in {elapsed:.3f} seconds (no deadlock)")
        assert elapsed < 1.0, "record_error should complete quickly without deadlock"
        
        # Try to record success
        start_time = time.time()
        await circuit_breaker.record_success()
        elapsed = time.time() - start_time
        
        print(f"   ✅ record_success completed in {elapsed:.3f} seconds (no deadlock)")
        assert elapsed < 1.0, "record_success should complete quickly without deadlock"
    
    await circuit_breaker.cleanup()
    print("🎉 Health monitoring deadlock fix test passed!")


@pytest.mark.asyncio
async def test_concurrent_health_check_and_operations():
    """Test that health checks can run concurrently with circuit breaker operations."""
    print("\n🔧 Testing Concurrent Health Check and Operations")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=2,
        recovery_timeout=2.0,
        health_check_interval=0.1,
        max_concurrent_connections=5
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Mock high system load
    mock_memory = MagicMock()
    mock_memory.percent = 88.0
    mock_cpu_percent = MagicMock(return_value=92.0)
    
    async def simulate_operations():
        """Simulate concurrent circuit breaker operations."""
        for i in range(10):
            await circuit_breaker.enter_request()
            await asyncio.sleep(0.05)
            
            if i % 3 == 0:
                await circuit_breaker.record_error(Exception(f"Error {i}"))
            else:
                await circuit_breaker.record_success()
                
            await circuit_breaker.exit_request()
            await asyncio.sleep(0.02)
    
    with patch('psutil.virtual_memory', return_value=mock_memory), \
         patch('psutil.cpu_percent', mock_cpu_percent):
        
        # Run operations concurrently with health monitoring
        print("   Running concurrent operations with health monitoring...")
        start_time = time.time()
        
        # Start the operations task
        operations_task = asyncio.create_task(simulate_operations())
        
        # Let both health monitoring and operations run
        await operations_task
        
        elapsed = time.time() - start_time
        print(f"   ✅ All operations completed in {elapsed:.2f} seconds")
        
        # Verify circuit breaker is still functional
        status = await circuit_breaker.get_circuit_status()
        print(f"   ✅ Final circuit state: {status['state']}")
        print(f"   ✅ Active connections: {status['active_connections']}")
        
        assert elapsed < 5.0, "Operations should complete without hanging"
    
    await circuit_breaker.cleanup()
    print("🎉 Concurrent operations test passed!")


@pytest.mark.asyncio
async def test_rate_limit_warning_activation():
    """Test that rate limit warning can be activated without deadlock."""
    print("\n🔧 Testing Rate Limit Warning Activation")
    print("=" * 50)
    
    config = CircuitBreakerConfig(failure_threshold=5, recovery_timeout=3.0)
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Test direct activation
    print("   Testing direct rate limit warning activation...")
    start_time = time.time()
    await circuit_breaker.record_rate_limit_warning(2.0)
    elapsed = time.time() - start_time
    
    print(f"   ✅ Rate limit warning activated in {elapsed:.3f} seconds")
    assert elapsed < 0.5, "Rate limit warning should activate quickly"
    
    # Verify that requests are blocked
    can_execute = await circuit_breaker.can_execute()
    print(f"   ✅ Requests blocked during warning: {not can_execute}")
    assert not can_execute, "Requests should be blocked during rate limit warning"
    
    # Wait for warning to clear
    print("   Waiting for rate limit warning to clear...")
    await asyncio.sleep(2.5)
    
    can_execute_after = await circuit_breaker.can_execute()
    print(f"   ✅ Requests allowed after warning: {can_execute_after}")
    assert can_execute_after, "Requests should be allowed after warning clears"
    
    await circuit_breaker.cleanup()
    print("🎉 Rate limit warning activation test passed!")


@pytest.mark.asyncio
async def test_health_monitor_connection_tracking():
    """Test that health monitor correctly tracks connection usage."""
    print("\n🔧 Testing Health Monitor Connection Tracking")
    print("=" * 50)
    
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=3.0,
        max_concurrent_connections=5
    )
    
    circuit_breaker = GlobalCircuitBreaker(config=config)
    
    # Simulate high connection usage
    print("   Simulating high connection usage...")
    for i in range(6):  # Exceed 80% of max_concurrent_connections (5)
        await circuit_breaker.enter_request()
    
    status = await circuit_breaker.get_circuit_status()
    print(f"   ✅ Active connections: {status['active_connections']}")
    assert status['active_connections'] == 6
    
    # Mock system resources to trigger health check
    mock_memory = MagicMock()
    mock_memory.percent = 70.0  # Normal memory
    mock_cpu_percent = MagicMock(return_value=60.0)  # Normal CPU
    
    with patch('psutil.virtual_memory', return_value=mock_memory), \
         patch('psutil.cpu_percent', mock_cpu_percent):
        
        # Trigger health check manually
        await circuit_breaker._perform_health_check()
        print("   ✅ Health check completed without deadlock")
    
    # Clean up connections
    for i in range(6):
        await circuit_breaker.exit_request()
    
    final_status = await circuit_breaker.get_circuit_status()
    print(f"   ✅ Final active connections: {final_status['active_connections']}")
    assert final_status['active_connections'] == 0
    
    await circuit_breaker.cleanup()
    print("🎉 Connection tracking test passed!")


async def main():
    """Run all deadlock fix tests."""
    print("🚀 Testing Enhanced GlobalCircuitBreaker Deadlock Fixes")
    print("=" * 60)
    
    await test_health_monitoring_no_deadlock()
    await test_concurrent_health_check_and_operations()
    await test_rate_limit_warning_activation()
    await test_health_monitor_connection_tracking()
    
    print("\n" + "=" * 60)
    print("🎉 All Deadlock Fix Tests Passed!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
