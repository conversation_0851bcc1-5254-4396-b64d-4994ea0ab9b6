# coding=utf-8
import sys
import os
import sqlalchemy
from pytest_postgresql.executor import PostgreSQLExecutor


def pytest_configure(config):
    # Add the project root to the sys.path
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data_pipeline')))
    print(f"SQLAlchemy is being loaded from: {sqlalchemy.__file__}")

PostgreSQLExecutor.BASE_PROC_START_COMMAND = PostgreSQLExecutor.BASE_PROC_START_COMMAND.replace("'stderr'", "stderr")