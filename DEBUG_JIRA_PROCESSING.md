# JIRA Data Processing Debug Tools

This document explains how to use the debugging tools to identify and fix DataFrame creation issues in the consume_* functions.

## 🎯 **Problem Statement**

The consume functions (`consume_changelog`, `consume_worklog`, `consume_comment`, `consume_issue_links`, `consume_issue`) are experiencing exceptions during DataFrame creation when processing real JIRA data. These tools help identify the root cause by:

1. Fetching real JIRA data using JQL queries
2. Testing each consume function individually
3. Providing detailed logging of data structures and transformation steps
4. Catching and analyzing exceptions with full context

## 🔧 **Available Tools**

### **1. Main Debug Module** (`dags/data_pipeline/debug_queue_processor.py`)
- **JiraDataFetcher**: Fetches real JIRA data using JQL
- **QueueDebugProcessor**: Tests consume functions with detailed logging
- **Debug Functions**: Various test scenarios

### **2. Test Runner** (`test_queue_debug.py`)
- **Test 1**: Test all consume functions (safe)
- **Test 2**: Test specific consume function (safe)
- **Test 3**: Test with custom JQL query (safe)
- **Test 4**: Test with database processing (caution)

## 🚀 **Quick Start**

### **Basic Usage**
```bash
# Test all consume functions with recent PLAT project data
python test_queue_debug.py 1

# Test a specific consume function
python test_queue_debug.py 2

# Test with custom JQL
python test_queue_debug.py 3
```

### **Direct Module Usage**
```bash
# Test specific function
python dags/data_pipeline/debug_queue_processor.py specific consume_changelog PLAT

# Test with custom JQL
python dags/data_pipeline/debug_queue_processor.py jql "project = PLAT AND key = PLAT-1234" PLAT
```

## 📊 **What the Debug Output Shows**

### **Raw JIRA Data Analysis**
- Number of issues fetched
- Available fields in JIRA response
- Structure of problematic fields (worklog, comment, changelog, issuelinks)
- Data types and nested structures

### **DataFrame Creation Analysis**
- DataFrame shape and columns
- Data types of each column
- Null value counts
- Mixed type detection
- Sample data rows

### **Error Context**
- Full exception tracebacks
- Line numbers where errors occur
- DataFrame state when error happens
- Raw data that caused the error

## 🔍 **Common Issues to Look For**

### **1. Mixed Data Types**
```
Column 'worklog' has mixed types: {<class 'dict'>: 5, <class 'list'>: 2}
```
**Solution**: Handle both dict and list formats in transformation code

### **2. Missing Fields**
```
KeyError: 'changelog' not found in DataFrame columns
```
**Solution**: Check if field exists before accessing

### **3. Nested Structure Issues**
```
TypeError: 'NoneType' object is not subscriptable
```
**Solution**: Add null checks before accessing nested data

### **4. JSON Normalization Problems**
```
ValueError: arrays must all be same length
```
**Solution**: Handle variable-length arrays in JSON normalization

## 🛠 **Debugging Workflow**

### **Step 1: Identify the Problem Function**
```bash
# Test all functions to see which one fails
python test_queue_debug.py 1
```

### **Step 2: Focus on the Failing Function**
```bash
# Test specific function with more detail
python dags/data_pipeline/debug_queue_processor.py specific consume_changelog PLAT
```

### **Step 3: Test with Specific Data**
```bash
# Use a specific issue that's causing problems
python dags/data_pipeline/debug_queue_processor.py jql "project = PLAT AND key = PLAT-1234" PLAT
```

### **Step 4: Analyze the Output**
Look for:
- Raw JIRA data structure
- DataFrame creation steps
- Exception details
- Data type mismatches

## 📝 **Example Debug Session**

```bash
$ python test_queue_debug.py 2

=== Raw JIRA Data Analysis for consume_changelog ===
Number of issues: 2
Issue keys: ['id', 'key', 'fields', 'changelog']
changelog structure: <class 'dict'>
changelog keys: ['startAt', 'maxResults', 'total', 'histories']

=== DataFrame Info Initial DataFrame from JIRA ===
Shape: (2, 4)
Columns: ['id', 'key', 'fields', 'changelog']
Data types:
id        object
key       object
fields    object
changelog object

=== DataFrame Info Extracted data for consume_changelog ===
Shape: (2, 3)
Columns: ['id', 'key', 'changelog']

ERROR: Exception in consume_changelog
TypeError: 'NoneType' object has no attribute 'get'
Line 2555: df_changelog.histories = df_changelog.apply(...)
```

This shows that the `changelog` field contains None values that need to be handled.

## ⚠️ **Safety Notes**

- **Tests 1-3 are safe** - they only inspect data, don't modify database
- **Test 4 processes to database** - use with caution
- **Always test with small datasets first** (max_issues=1-5)
- **Use non-production projects** for testing when possible

## 🔧 **Customization**

### **Change Project**
Modify the project_key parameter:
```python
await debug_consume_functions_with_jira_data(project_key="PLP")
```

### **Custom JQL**
Test with specific conditions:
```python
jql = "project = PLAT AND worklog is not EMPTY AND updated > '2024-01-01'"
```

### **Specific Functions**
Test only problematic functions:
```python
consume_functions = ["consume_worklog", "consume_comment"]
```

## 📞 **Getting Help**

If you encounter issues:
1. Check the full debug output for error details
2. Look at the raw JIRA data structure
3. Compare with expected DataFrame structure
4. Check for null values and mixed types
5. Test with simpler JQL queries first

The debug tools provide comprehensive logging to help identify exactly where and why DataFrame creation is failing.
