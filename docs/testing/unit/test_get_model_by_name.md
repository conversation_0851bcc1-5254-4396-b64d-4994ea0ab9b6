# Test Documentation: get_model_by_name Function

## Overview

This document explains the comprehensive test suite for the `get_model_by_name` function, which retrieves SQLAlchemy model classes by name and optional schema from the SQLAlchemy class registry.

## Function Under Test

The `get_model_by_name` function:
- Searches through SQLAlchemy's class registry for model classes
- Supports optional schema filtering
- Handles weak references in the registry
- Returns the matching model class or raises `ValueError`

## Test Structure

The test suite consists of four main test methods organized under the `TestGetModelByName` class:

### 1. `test_get_model_by_name_not_found`
**Purpose**: Tests error handling for non-existent models

**What it tests**:
- Function raises `ValueError` when model doesn't exist
- Error message contains the correct model name

**Why this test matters**: Ensures proper error handling and user feedback when invalid model names are provided.

---

### 2. `test_get_model_by_name_function_exists`
**Purpose**: Basic smoke test for function existence and callable behavior

**What it tests**:
- Function is callable
- Function properly raises errors for invalid input

**Why this test matters**: Validates basic function contract and ensures it's properly importable and executable.

---

### 3. `test_model_lookup_by_schema` (Parameterized)
**Purpose**: Core functionality testing with schema-aware model retrieval

**Test Cases**:
```python
("Issue", "public", None, True)       # No schema → match any
("Issue", "public", "public", True)   # Exact schema match
("Issue", "public", "acq", False)     # Schema mismatch
("Issue", "acq", "acq", True)         # Custom schema match
("Issue", "acq", None, True)          # No schema → match any
("Issue", "acq", "public", False)     # Schema mismatch
("Unknown", "acq", "acq", False)      # Model doesn't exist
```

**What it tests**:
- Schema filtering logic (`schema=None` matches any, specific schema must match exactly)
- Model name matching
- Proper error handling for non-existent models with schema specifications

---

### 4. `test_snake_case_tablename_conversion` (Parameterized)
**Purpose**: Tests snake_case conversion feature in tablename generation

**Test Cases**:
```python
("IssueClassification", True, "issue_classification", ...)   # Snake case ON
("IssueClassification", False, "issueclassification", ...)   # Snake case OFF
("UserAccount", True, "user_account", ...)                  # Multi-word conversion
("SimpleClass", True, "simple_class", ...)                  # Basic conversion
```

**What it tests**:
- `use_snake_case` flag functionality
- CamelCase to snake_case conversion
- Tablename generation behavior

---

### 5. `test_table_args_schema_definition` (Parameterized)
**Purpose**: Tests models with explicit schema definitions via `__table_args__`

**Test Cases**:
```python
("User", "public", None, True)        # Public schema, no filter
("User", "public", "public", True)    # Explicit public match
("User", "public", "private", False)  # Schema mismatch
("Admin", "admin", "admin", True)     # Custom schema match
```

**What it tests**:
- Models with `__table_args__ = ({'schema': 'public'})`
- Schema enforcement for models with predefined schemas
- Interaction between `__table_args__` and function schema parameter

## Complex Mock Setup Explanation

### Why We Avoid SQLAlchemy Inheritance

The tests use a sophisticated mocking approach that **doesn't inherit from the actual `Base` class**. Here's why:

#### The Problem
```python
# This FAILS because it triggers SQLAlchemy's declarative machinery
from unittest.mock import Mock
from dags.data_pipeline.dbmodels.base import Base
class MockModel(Base):
    __tablename__ = "test"
    __table__ = Mock()  # ❌ SQLAlchemy expects real Table object
```

**Error**: `ArgumentError: FROM expression, such as a Table or alias() object expected, got <Mock>`

#### The Solution
```python
# This WORKS because it simulates a class without triggering SQLAlchemy
from unittest.mock import Mock
model_name: str = "IssueClassification"
mock_table: str = "issue_classification"
MockModel = Mock(spec=type)
MockModel.__name__ = model_name
MockModel.__table__ = mock_table
MockModel.__bases__ = (Base,)  # For isinstance checks

# Mock issubclass to return True for our mock
def mock_issubclass(cls, base_class):
    if cls is MockModel and base_class is Base:
        return True
    return original_issubclass(cls, base_class)
```

### Key Components of the Mock Setup

#### 1. **Mock Class Creation**
```python
MockModel = Mock(spec=type)
```
- Creates a mock that behaves like a `type` object
- Avoids triggering SQLAlchemy's metaclass machinery

#### 2. **Essential Attributes**
```python
MockModel.__name__ = model_name          # For name matching
MockModel.__table__ = mock_table         # For schema access
MockModel.__bases__ = (Base,)            # For type checking
```

#### 3. **issubclass Patching**
```python
patch('builtins.issubclass', side_effect=mock_issubclass)
```
- The function uses `issubclass(cls, Base)` to validate models
- We patch this to return `True` for our mock classes

#### 4. **Registry Simulation**
```python
mock_weakref = Mock()
mock_weakref.return_value = MockModel
registry_items = [(model_name, mock_weakref)]
```
- Simulates SQLAlchemy's weak reference registry
- Registry contains `(name, weakref)` pairs

## Why This Approach Works

### 1. **Avoids SQLAlchemy Complexity**
- No table creation or validation
- No declarative metaclass processing
- No database connection requirements

### 2. **Provides Necessary Interface**
- All attributes the function checks are present
- Behavior matches real SQLAlchemy models
- Type checking and inheritance checks work correctly

### 3. **Enables Comprehensive Testing**
- Can test edge cases without database setup
- Full control over model attributes and behavior
- Fast execution without SQLAlchemy overhead

### 4. **Maintains Test Isolation**
- Each test creates fresh mocks
- No side effects between tests
- Predictable and repeatable results

## Test Categories and Coverage

| Test Category | Coverage | Purpose |
|---------------|----------|---------|
| **Error Handling** | Model not found scenarios | Validates proper exception handling |
| **Basic Functionality** | Function existence and basic calls | Smoke testing |
| **Schema Filtering** | All schema matching combinations | Core business logic |
| **Snake Case** | Tablename conversion logic | Feature-specific testing |
| **Table Args** | Explicit schema definitions | Advanced configuration testing |

## Benefits of This Test Design

1. **Comprehensive Coverage**: Tests all major code paths and edge cases
2. **Fast Execution**: No database or SQLAlchemy setup overhead
3. **Isolated Testing**: Each test is independent and predictable
4. **Clear Documentation**: Parameterized tests serve as living documentation
5. **Maintainable**: Mock setup is reusable and well-structured
6. **Realistic**: Simulates actual SQLAlchemy registry behavior accurately

## Running the Tests

```bash
# Run all tests
pytest test_get_model_by_name.py

# Run with Allure reporting
pytest test_get_model_by_name.py --alluredir=results

# Run specific test categories
pytest test_get_model_by_name.py -k "schema"
pytest test_get_model_by_name.py -k "snake_case"
```

This test suite ensures the `get_model_by_name` function works correctly across all supported scenarios while maintaining fast, reliable, and maintainable test execution.