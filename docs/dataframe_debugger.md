# DataFrame Debugger Documentation

## Overview

The DataFrame Debugger is a refactored, SOLID-compliant utility for saving pandas DataFrames to various file formats with advanced features like async processing, progress tracking, and robust error handling.

## Key Features

- **Dual Execution Modes**: Synchronous and asynchronous processing
- **Multiple File Formats**: CSV, Excel, Parquet, Feather, JSON
- **Progress Tracking**: Real-time callbacks and monitoring
- **Robust Error Handling**: Retry mechanisms and graceful failures
- **Memory Efficient**: Optimized for large DataFrames
- **Thread-Safe**: Concurrent operations with proper synchronization
- **SOLID Principles**: Protocol-based interfaces, mixins, and factory patterns

## Architecture

### Core Components

1. **Protocols (Interfaces)**
   - `DataFrameProcessor`: DataFrame preprocessing operations
   - `FileHandler`: File saving operations
   - `TaskProcessor`: Task processing logic
   - `ProgressNotifier`: Progress notification system

2. **Mixins**
   - `LoggerMixin`: Shared logging functionality with file descriptor management
   - `PathMixin`: Path handling and validation
   - `DataFrameProcessorMixin`: DataFrame preprocessing logic
   - `FileHandlerMixin`: File format handling
   - `ProgressNotifierMixin`: Progress callback management

3. **Implementations**
   - `SyncDataFrameDebugger`: Synchronous processing with threading
   - `AsyncDataFrameDebugger`: Asynchronous processing with asyncio

4. **Factory Pattern**
   - `DataFrameDebuggerFactory`: Easy instantiation of debuggers

## Quick Start

### Synchronous Usage

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import DataframeDebugger
import pandas as pd

# Create sample data
df = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

# Basic usage
with DataframeDebugger() as debugger:
    success = debugger.save_dataframe(df, "output.xlsx")
    debugger.wait_for_completion()
    print(f"Success: {success}")

# Quick save utility
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_dataframe
success = quick_save_dataframe(df, "quick_output.xlsx")
```

### Asynchronous Usage

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import AsyncDataframeDebugger
import asyncio

async def async_example():
    df = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    async with AsyncDataframeDebugger(max_workers=4) as debugger:
        task_id = await debugger.save_dataframe(df, "async_output.xlsx")
        success = await debugger.wait_for_completion()
        stats = await debugger.get_stats()
        print(f"Stats: {stats}")

# Quick async save
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_async
success = await quick_save_async(df, "quick_async.xlsx")
```

### Factory Pattern Usage

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import (
    DataFrameDebuggerFactory, ExecutionMode
)

# Create debuggers using factory
sync_debugger = DataFrameDebuggerFactory.create_sync_debugger(max_workers=2)
async_debugger = DataFrameDebuggerFactory.create_async_debugger(max_workers=4)

# Or use execution mode
debugger = DataFrameDebuggerFactory.create_debugger(
    ExecutionMode.ASYNC, 
    path="/output/path",
    max_workers=6
)
```

## Advanced Features

### Progress Callbacks

```python
def progress_callback(data):
    print(f"Task {data['task_id']}: {data['status']} - {data['filename']}")

with DataframeDebugger() as debugger:
    debugger.add_progress_callback(progress_callback)
    debugger.save_dataframe(df, "monitored.xlsx")
    debugger.wait_for_completion()
```

### Batch Operations

```python
# Synchronous batch
with DataframeDebugger() as debugger:
    with debugger.batch_save():
        debugger.save_dataframe(df1, "batch1.xlsx")
        debugger.save_dataframe(df2, "batch2.csv")
        # All files saved before context exit

# Asynchronous batch
async with AsyncDataframeDebugger() as debugger:
    async with debugger.batch_save():
        await debugger.save_dataframe(df1, "async_batch1.xlsx")
        await debugger.save_dataframe(df2, "async_batch2.csv")
```

### Multiple File Formats

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import SaveFormat

with DataframeDebugger() as debugger:
    debugger.save_dataframe(df, "data.csv", SaveFormat.CSV)
    debugger.save_dataframe(df, "data.xlsx", SaveFormat.EXCEL)
    debugger.save_dataframe(df, "data.parquet", SaveFormat.PARQUET)
    debugger.save_dataframe(df, "data.feather", SaveFormat.FEATHER)
    debugger.save_dataframe(df, "data.json", SaveFormat.JSON)
```

### Priority-Based Processing (Async Only)

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import Priority

async with AsyncDataframeDebugger() as debugger:
    # High priority task
    await debugger.save_dataframe(critical_df, "critical.xlsx", priority=Priority.HIGH)
    
    # Normal priority task
    await debugger.save_dataframe(normal_df, "normal.xlsx", priority=Priority.NORMAL)
    
    # Low priority task
    await debugger.save_dataframe(bulk_df, "bulk.xlsx", priority=Priority.LOW)
```

## Configuration Options

### Constructor Parameters

#### Common Parameters (Both Sync/Async)
- `path`: Output directory path
- `log_level`: Logging level (default: INFO)
- `max_retries`: Maximum retry attempts (default: 3)
- `retry_delay`: Delay between retries in seconds (default: 1.0)
- `auto_compress`: Enable automatic compression (default: False)
- `enable_progress_callback`: Enable progress notifications (default: True)

#### Sync-Specific Parameters
- `max_workers`: Number of worker threads (default: 2)

#### Async-Specific Parameters
- `max_workers`: Number of async workers (default: 4)
- `use_process_pool`: Use ProcessPoolExecutor instead of ThreadPoolExecutor (default: False)
- `max_queue_size`: Maximum queue size (default: 1000)

### Example Configuration

```python
# Sync configuration
sync_debugger = DataframeDebugger(
    path="/output/path",
    max_workers=4,
    max_retries=5,
    retry_delay=2.0,
    log_level=logging.WARNING,
    auto_compress=True
)

# Async configuration
async_debugger = AsyncDataframeDebugger(
    path="/output/path",
    max_workers=8,
    use_process_pool=True,
    max_queue_size=2000,
    max_retries=3,
    enable_progress_callback=True
)
```

## Utility Functions

### High-Frequency Operations

For operations that need minimal overhead:

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_dataframe_minimal

# Minimal logging, optimized for speed
success = quick_save_dataframe_minimal(df, "minimal.xlsx", "/path", "xlsx")
```

### Parallel Processing

```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import save_dataframes_parallel

# Save multiple DataFrames in parallel
dataframes = {
    "file1.xlsx": df1,
    "file2.xlsx": df2,
    "file3.xlsx": df3
}

results = await save_dataframes_parallel(
    dataframes, 
    path="/output", 
    max_workers=6
)
print(f"Results: {results}")  # {'file1.xlsx': True, 'file2.xlsx': True, ...}
```

## Migration Guide

### From dataframe_debug_sync.py

**Old Code:**
```python
from dags.data_pipeline.dataframe_utils.dataframe_debug_sync import (
    DataframeDebugger, quick_save_dataframe, quick_save_dataframe_minimal
)

with DataframeDebugger() as debugger:
    debugger.debug_dataframe(df, "output.xlsx")  # Old method name
```

**New Code:**
```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import (
    DataframeDebugger, quick_save_dataframe, quick_save_dataframe_minimal
)

with DataframeDebugger() as debugger:
    debugger.save_dataframe(df, "output.xlsx")  # New method name
    # OR use backward compatibility method
    debugger.debug_dataframe(df, "output.xlsx")  # Still works
```

### From dataframe_debug_async.py

**Old Code:**
```python
from dags.data_pipeline.dataframe_utils.dataframe_debug_async import (
    AsyncDataframeDebugger, quick_save_async
)
```

**New Code:**
```python
from dags.data_pipeline.dataframe_utils.dataframe_debugger import (
    AsyncDataframeDebugger, quick_save_async
)
```

## Error Handling

The debugger includes comprehensive error handling:

```python
try:
    with DataframeDebugger(max_retries=5) as debugger:
        success = debugger.save_dataframe(df, "output.xlsx")
        if not success:
            print("Failed to save DataFrame")
except Exception as e:
    print(f"Error: {e}")
```

## Performance Considerations

1. **For High-Frequency Operations**: Use `quick_save_dataframe_minimal`
2. **For Large DataFrames**: Use async version with process pool
3. **For Multiple Files**: Use batch operations or parallel processing
4. **Memory Usage**: The debugger creates DataFrame copies for thread safety

## Logging

The debugger uses shared logging to prevent file descriptor leaks:

- Shared log file: `dataframe_debugger_shared.log`
- Configurable log levels
- Automatic cleanup on exit
- Integration with LoggerContainer when available

## Best Practices

1. **Always use context managers** (`with` statements) for proper cleanup
2. **Use appropriate execution mode** (sync for simple operations, async for complex workflows)
3. **Configure appropriate worker counts** based on your system resources
4. **Use progress callbacks** for long-running operations
5. **Handle errors gracefully** with try-catch blocks
6. **Use minimal functions** for high-frequency debug operations

## Troubleshooting

### Common Issues

1. **File Descriptor Leaks**: Fixed in refactored version with shared logging
2. **Memory Issues**: Use process pool for large DataFrames
3. **Performance**: Adjust worker counts and use appropriate execution mode
4. **Path Issues**: Use pathlib.Path for cross-platform compatibility

### Debug Information

```python
# Get processing statistics
with DataframeDebugger() as debugger:
    debugger.save_dataframe(df, "test.xlsx")
    debugger.wait_for_completion()
    stats = debugger.stats
    print(f"Files saved: {stats.files_saved}")
    print(f"Files failed: {stats.files_failed}")
    print(f"Avg processing time: {stats.avg_processing_time}")
```
