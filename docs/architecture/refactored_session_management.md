# Refactored Session Management Architecture

## Overview

This document describes the refactored session management architecture that provides enhanced reliability, testability, and maintainability for database operations in the Airflow data pipeline.

## Architecture Components

### 1. Base Classes

#### BaseSessionManager (Abstract)
```python
class BaseSessionManager(ABC):
    """Abstract base class for database session managers."""
    
    @abstractmethod
    def close(self) -> None:
        """Synchronous cleanup method."""
        pass
        
    @abstractmethod
    async def aclose(self) -> None:
        """Asynchronous cleanup method."""
        pass
        
    @abstractmethod
    def update_schema(self, new_schema: str) -> Self:
        """Update the database schema."""
        pass
```

#### AsyncSessionManager
```python
class AsyncSessionManager(BaseSessionManager):
    """Base class for asynchronous database session management."""
    
    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Create async session context manager."""
        # Automatic rollback on exceptions
        # Proper resource cleanup
        pass
```

### 2. Composable Mixins

#### ConnectionRecoveryMixin
Provides automatic retry logic with exponential backoff for connection failures:

```python
class ConnectionRecoveryMixin:
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 1.0
    
    @asynccontextmanager
    async def async_session_with_retry(self) -> AsyncGenerator[AsyncSession, None]:
        """Context manager with automatic retry on connection failures."""
        # Implements exponential backoff: 1s, 2s, 4s
        # Automatically recreates database engine
        # Handles InterfaceError, DisconnectionError, ConnectionError
        pass
```

#### LifecycleManagementMixin
Provides automatic registration for cleanup during application shutdown:

```python
class LifecycleManagementMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Auto-register with lifecycle manager
        if hasattr(self, '_register_for_cleanup'):
            self._register_for_cleanup()
```

#### SchemaManagementMixin
Provides enhanced schema switching with history tracking:

```python
class SchemaManagementMixin:
    def update_schema(self, new_schema: str) -> Self:
        """Update schema with history tracking."""
        # Tracks schema changes
        # Supports schema reversion
        pass
        
    def get_schema_history(self) -> List[str]:
        """Get the history of schema changes."""
        pass
        
    def revert_schema(self) -> Self:
        """Revert to the previous schema."""
        pass
```

### 3. Concrete Session Managers

#### RefactoredPostgresSessionManager
```python
class RefactoredPostgresSessionManager(AsyncSessionManager, SchemaManagementMixin):
    """Base PostgreSQL session manager with sync and async support."""
    
    # Features:
    # - Both sync and async session support
    # - Schema management with history
    # - Automatic instance tracking for cleanup
    # - Proper resource management
```

#### EnhancedSessionManager
```python
class EnhancedSessionManager(RefactoredPostgresSessionManager, ConnectionRecoveryMixin):
    """Session manager with connection recovery capabilities."""
    
    # Features:
    # - All RefactoredPostgresSessionManager features
    # - Automatic retry on connection failures
    # - Exponential backoff strategy
    # - Engine recreation on persistent failures
```

#### ManagedSessionManager
```python
class ManagedSessionManager(LifecycleManagementMixin, RefactoredPostgresSessionManager):
    """Session manager with automatic lifecycle management."""
    
    # Features:
    # - All RefactoredPostgresSessionManager features
    # - Automatic registration for cleanup
    # - Lifecycle coordination with application shutdown
```

#### FullyEnhancedSessionManager
```python
class FullyEnhancedSessionManager(LifecycleManagementMixin, ConnectionRecoveryMixin, RefactoredPostgresSessionManager):
    """Session manager with all capabilities."""
    
    # Features:
    # - Connection recovery with retry logic
    # - Automatic lifecycle management
    # - Enhanced schema management
    # - Both sync and async session support
```

## TaskLifecycleCoordinator

### Purpose
Coordinates task lifecycle and graceful shutdown of monitors using `asyncio.Condition` for better synchronization.

### Key Features

#### Task Registration and Tracking
```python
coordinator = TaskLifecycleCoordinator(logger)

# Register task
task_id = await coordinator.register_task("process_jira_issues")

# Mark completion
await coordinator.mark_task_completed(task_id, result)

# Mark failure
await coordinator.mark_task_failed(task_id, error)
```

#### Monitor Coordination
```python
# Register monitors
monitor_task = asyncio.create_task(monitor())
await coordinator.register_monitor(monitor_task, "monitor")

# Automatic shutdown when all tasks complete
await coordinator.wait_for_all_tasks_completion()
```

#### Graceful Shutdown
```python
# Force shutdown if needed
await coordinator.force_shutdown(timeout=5.0)

# Check shutdown status
if coordinator.is_shutdown_requested():
    # Handle shutdown
    pass
```

### Implementation Details

#### asyncio.Condition Usage
```python
class TaskLifecycleCoordinator:
    def __init__(self, logger: Logger):
        self._condition = asyncio.Condition()
        self._active_tasks: set = set()
        self._monitors: Dict[str, asyncio.Task] = {}
        
    async def mark_task_completed(self, task_id: str, result: Any = None):
        async with self._condition:
            # Update task state
            # Check if all tasks complete
            if not self._active_tasks:
                self._shutdown_requested = True
            # Notify waiting monitors
            self._condition.notify_all()
```

## Enhanced Dependency Injection

### Container Architecture

#### CoreSessionManagerContainer
```python
class CoreSessionManagerContainer(containers.DeclarativeContainer):
    """Core container for session manager dependencies."""
    
    # Factory pattern for easy testing
    base_session_manager_rw = providers.Factory(RefactoredPostgresSessionManager, ...)
    enhanced_session_manager_rw = providers.Factory(EnhancedSessionManager, ...)
    fully_enhanced_session_manager_rw = providers.Factory(FullyEnhancedSessionManager, ...)
```

#### TaskCoordinationContainer
```python
class TaskCoordinationContainer(containers.DeclarativeContainer):
    """Container for task coordination components."""
    
    task_lifecycle_coordinator = providers.Callable(_get_task_lifecycle_coordinator, ...)
    enhanced_shutdown_handler = providers.Singleton(EnhancedApplicationShutdownHandler)
```

#### EnhancedApplicationContainer
```python
class EnhancedApplicationContainer(containers.DeclarativeContainer):
    """Enhanced application container with improved separation of concerns."""
    
    # Direct dependency injection
    enhanced_session_manager_rw = providers.Factory(EnhancedSessionManager, ...)
    task_coordinator = providers.Callable(_get_task_lifecycle_coordinator, ...)
    shutdown_handler = providers.Singleton(EnhancedApplicationShutdownHandler)
```

## Usage Patterns

### Basic Session Usage
```python
@inject
async def my_function(
    db_session=Provide[DatabaseSessionManagerContainer.database_rw_enhanced]
):
    async with db_session.async_session() as session:
        # Database operations with automatic retry
        result = await session.execute(query)
        await session.commit()
```

### Task Coordination
```python
@inject
async def process_task(
    task_coordinator=Provide[EnhancedApplicationContainer.task_coordinator]
):
    # Register monitors
    monitor_task = asyncio.create_task(monitor(task_coordinator=task_coordinator))
    await task_coordinator.register_monitor(monitor_task, "monitor")
    
    # Execute tasks with coordination
    await _execute_all_tasks_with_coordinator(
        task_definitions, job_tasks, task_executor,
        progress_manager, task_coordinator, logger
    )
    
    # Automatic graceful shutdown
    await task_coordinator.wait_for_all_tasks_completion()
```

### Schema Management
```python
# Schema switching with history
session_manager = FullyEnhancedSessionManager(entry, "initial_schema")
session_manager.update_schema("new_schema")
session_manager.update_schema("another_schema")

# Check history
history = session_manager.get_schema_history()
# ['initial_schema', 'new_schema', 'another_schema']

# Revert to previous schema
session_manager.revert_schema()  # Back to 'new_schema'
```

## Error Handling Patterns

### Connection Recovery
```python
# Automatic retry with exponential backoff
async with enhanced_session_manager.async_session() as session:
    # If connection fails:
    # 1. Detect connection-related error
    # 2. Wait 1 second, recreate engine, retry
    # 3. If fails again, wait 2 seconds, retry
    # 4. If fails again, wait 4 seconds, retry
    # 5. If still fails, raise exception
    result = await session.execute(query)
```

### Graceful Shutdown
```python
# Coordinated shutdown with timeout
try:
    await coordinator.wait_for_all_tasks_completion(timeout=30.0)
except asyncio.TimeoutError:
    logger.warning("Graceful shutdown timed out, forcing shutdown")
    await coordinator.force_shutdown(timeout=5.0)
```

## Performance Characteristics

### Benchmarks (from performance tests)

#### Session Creation Performance
- **RefactoredPostgresSessionManager**: 100 instances in <1s
- **EnhancedSessionManager**: 100 instances in <1.5s  
- **FullyEnhancedSessionManager**: 100 instances in <2s

#### Connection Recovery Performance
- **Average recovery time**: <100ms
- **Maximum recovery time**: <500ms

#### Task Coordination Performance
- **Registration rate**: >5,000 tasks/s
- **Completion rate**: >5,000 tasks/s
- **Monitor shutdown**: 100 monitors in <2s

#### Memory Management
- **Memory increase**: <100MB for 1000 session managers
- **Cleanup efficiency**: >80%

## Best Practices

### 1. Session Manager Selection
- Use **RefactoredPostgresSessionManager** for basic needs
- Use **EnhancedSessionManager** when connection reliability is critical
- Use **ManagedSessionManager** for automatic lifecycle management
- Use **FullyEnhancedSessionManager** for production workloads

### 2. Error Handling
- Always use async context managers for sessions
- Let connection recovery handle transient failures automatically
- Implement application-specific retry logic for business logic errors

### 3. Resource Management
- Register long-running components with TaskLifecycleCoordinator
- Use dependency injection for testability
- Monitor memory usage in production

### 4. Testing
- Use Factory providers for easy test overrides
- Mock database engines for unit tests
- Use real asyncio.Task objects for integration tests

## Migration Checklist

- [ ] Update container dependencies to use enhanced session managers
- [ ] Replace manual monitor cancellation with TaskLifecycleCoordinator
- [ ] Update error handling to leverage automatic retry
- [ ] Add performance monitoring for connection recovery
- [ ] Update tests to use new patterns
- [ ] Document schema management usage
- [ ] Train team on new patterns
