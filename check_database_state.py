#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the current database state and see what tables exist in each schema.
"""

import sys
import os
from sqlalchemy import create_engine, text
from alembic.config import Config

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_state():
    """Check what tables exist in each schema."""
    
    # Get database URL from alembic.ini
    alembic_cfg = Config("alembic.ini")
    database_url = alembic_cfg.get_main_option("sqlalchemy.url")
    
    engine = create_engine(database_url)
    
    schemas = ['public', 'plat', 'plp', 'acq']
    
    with engine.connect() as connection:
        for schema in schemas:
            print(f"\n=== Schema: {schema} ===")
            try:
                # Get all tables in the schema
                result = connection.execute(text(f"""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = '{schema}'
                    ORDER BY table_name
                """))
                
                tables = [row[0] for row in result]
                if tables:
                    print(f"Tables: {', '.join(tables)}")
                else:
                    print("No tables found")
                    
                # Check alembic_version table
                try:
                    result = connection.execute(text(f"SELECT version_num FROM {schema}.alembic_version"))
                    version = result.fetchone()
                    if version:
                        print(f"Alembic version: {version[0]}")
                    else:
                        print("No alembic version found")
                except Exception as e:
                    print(f"No alembic_version table: {e}")
                    
            except Exception as e:
                print(f"Error checking schema {schema}: {e}")

if __name__ == "__main__":
    print("Checking database state...")
    check_database_state()
    print("\nDone!")
