#!/usr/bin/env python3
"""
Test real usage of correlation tracking to verify the fix works.
"""
import sys
import logging
import asyncio
sys.path.insert(0, '../dags/data_pipeline')

# Import using the same pattern as the application would
import dags.data_pipeline.custom_logger as custom_logger_module
from dags.data_pipeline.logging_utils import install_correlation_log_record_factory

def test_real_usage():
    """Test correlation tracking in a real usage scenario."""
    print("🧪 Testing real usage scenario...")
    
    # Step 1: Install the correlation factory (this would be done in container init)
    install_correlation_log_record_factory()
    
    # Step 2: Set up logging
    logger = logging.getLogger("real_usage_test")
    logger.setLevel(logging.INFO)
    
    # Add a console handler to see the output
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d %(levelname)-8s [%(correlation_id)s] [%(task_id)s:%(coroutine_name)s] %(funcName)-25s | %(message)s',
        datefmt='%Y-%m-%dT%H:%M:%S'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # Step 3: Use correlation context
    CorrelationContext = custom_logger_module.CorrelationContext
    
    print("\n--- Synchronous Operations ---")
    with CorrelationContext("user_registration") as ctx:
        logger.info("Starting user registration process")
        logger.info("Validating user input")
        logger.warning("Email domain not in whitelist - proceeding anyway")
        logger.info("Creating user account")
        logger.info("User registration completed")
    
    print("\n--- Different Operation ---")
    with CorrelationContext("data_export") as ctx:
        logger.info("Starting data export")
        logger.info("Querying database")
        logger.info("Export completed")
    
    print("\n--- Without Correlation Context ---")
    logger.info("This message has no correlation context")
    
    print("\n✅ Real usage test completed")

async def test_async_real_usage():
    """Test async correlation tracking in real usage."""
    print("\n🧪 Testing async real usage scenario...")
    
    # Set up logging
    logger = logging.getLogger("async_real_usage_test")
    logger.setLevel(logging.INFO)
    
    # Add a console handler
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d %(levelname)-8s [%(correlation_id)s] [%(task_id)s:%(coroutine_name)s] %(funcName)-25s | %(message)s',
        datefmt='%Y-%m-%dT%H:%M:%S'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    CorrelationContext = custom_logger_module.CorrelationContext
    
    async def process_batch(batch_id):
        async with CorrelationContext(f"batch_{batch_id}") as ctx:
            logger.info(f"Starting batch {batch_id} processing")
            await asyncio.sleep(0.01)  # Simulate async work
            logger.info(f"Processing items in batch {batch_id}")
            await asyncio.sleep(0.01)  # Simulate more work
            logger.info(f"Batch {batch_id} completed")
    
    print("\n--- Async Operations ---")
    # Process batches sequentially to see clear correlation IDs
    await process_batch(1)
    await process_batch(2)
    
    print("\n✅ Async real usage test completed")

async def main():
    """Run all real usage tests."""
    print("🚀 Real Usage Correlation Tracking Test")
    print("=" * 50)
    
    test_real_usage()
    await test_async_real_usage()
    
    print("=" * 50)
    print("🎉 Real usage tests completed!")
    print("\nWhat you should see:")
    print("- ✅ Correlation IDs that are NOT 'N/A'")
    print("- ✅ Same correlation ID for related operations")
    print("- ✅ Different correlation IDs for different operations")
    print("- ✅ Task IDs and coroutine names for async operations")

if __name__ == "__main__":
    asyncio.run(main())
