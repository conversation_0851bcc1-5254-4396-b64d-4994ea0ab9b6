import factory
from factory.alchemy import SQLAlchemyModelFactory
from datetime import datetime, timezone
from dags.data_pipeline.dbmodels.changelog import ChangelogJSON
from tests.factories.user_factory import UserFactory
from tests.factories.issue_factory import IssueFactory


class ChangelogJSONFactory(SQLAlchemyModelFactory):
    class Meta:
        model = ChangelogJSON
        sqlalchemy_session = None  # to be set by fixture
        sqlalchemy_session_persistence = "flush"

    id = factory.Sequence(lambda n: n + 1000)
    # author_user = factory.LazyAttribute(lambda o: UserFactory(accountType="atlassian").accountId)
    # issue = factory.SubFactory(IssueFactory)

    # author = factory.SelfAttribute("author_user.accountId")
    author = factory.LazyAttribute(lambda o: UserFactory(accountType="app").accountId)
    # issue_id = factory.SelfAttribute("issue.id")
    # issue_key = factory.SelfAttribute("issue.key")
    issue_id = factory.Sequence(lambda n: n + 1000)
    issue_key = factory.Sequence(lambda n: f"PLAT-{n + 1000}")
    created = factory.LazyFunction(lambda: datetime.now(timezone.utc))
    items = factory.LazyFunction(lambda: [
        {"field": "status", "from": "To Do", "to": "In Progress"}
    ])
