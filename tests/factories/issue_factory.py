# tests/factories/issue_factory.py
import random
from zoneinfo import ZoneInfo

import factory
from factory import SelfAttribute
from factory.alchemy import SQLAlchemyModelFactory
from faker import Faker
from dags.data_pipeline.dbmodels.issue import Issue
from tests.factories.user_factory import UserFactory


COMMON_TZINFOS = [
    ZoneInfo("UTC"),
    ZoneInfo("Asia/Kolkata"),
    ZoneInfo("Europe/London"),
    ZoneInfo("America/New_York"),
    ZoneInfo("Asia/Tokyo"),
    ZoneInfo("Australia/Sydney"),
]

def random_tzinfo():
    return random.choice(COMMON_TZINFOS)

faker = Faker()

class IssueFactory(SQLAlchemyModelFactory):
    class Meta:
        model = Issue
        sqlalchemy_session = None
        sqlalchemy_session_persistence = "flush"

    # _assignee_user = factory.SubFactory(UserFactory, accountType="app")
    # _reporter_user = factory.SubFactory(UserFactory, accountType="atlassian")


    id = factory.Sequence(lambda n: n + 10000)
    key = factory.LazyFunction(lambda: f"PROJ-{faker.random_int(100, 9999)}")
    summary = factory.Faker("sentence")
    description = factory.Faker("paragraph")
    description_markdown = factory.Faker("paragraph")
    tscv_summary_description = "fulltext index"

    isSubTask = factory.Faker("boolean")
    issuetype = factory.Iterator(["Task", "Bug", "Story", "Epic"])
    issue_hierarchy_level = factory.Faker("random_int", min=0, max=3)
    status = factory.Iterator(["To Do", "In Progress", "Done"])
    statusCategory = factory.Iterator(["new", "indeterminate", "done"])
    statuscategorychangedate = factory.LazyFunction(lambda: faker.date_time_this_year(tzinfo=random_tzinfo()))
    resolution = factory.Faker("word")
    resolutiondate = factory.LazyFunction(lambda: faker.date_time_this_year(tzinfo=random_tzinfo()))
    priority = factory.Iterator(["Low", "Medium", "High"])
    urgency = factory.Faker("word")
    components = factory.LazyFunction(lambda: [faker.word()])
    fixVersions = factory.LazyFunction(lambda: [faker.word()])
    versions = factory.LazyFunction(lambda: [faker.word()])

    assignee = factory.LazyAttribute(lambda o: UserFactory(accountType="app").accountId)
    reporter = factory.LazyAttribute(lambda o: UserFactory(accountType="atlassian").accountId)

    # reporter = SelfAttribute("_reporter_user.accountId")
    # assignee = SelfAttribute("_assignee_user.accountId")

    created = factory.LazyFunction(lambda: faker.date_time_this_year(tzinfo=ZoneInfo("Asia/Kolkata")))
    updated = factory.LazyFunction(lambda: faker.date_time_this_year(tzinfo=ZoneInfo("Asia/Kolkata")))
    Rank = factory.LazyFunction(lambda: f"{faker.random_int()}|{faker.random_int()}")

    sprint = factory.LazyFunction(lambda: {"id": faker.random_int(), "name": faker.word()})
    Team = factory.Faker("word")
    ClientJira = factory.LazyFunction(lambda: [faker.domain_name()])
    startdate = factory.Faker("date_this_year")
    duedate = factory.Faker("future_date")

    timetracking = factory.LazyFunction(lambda: {"originalEstimate": "3d", "timeSpent": "1d"})

    timeoriginalestimate = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    timespent = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    aggregatetimeoriginalestimate = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    aggregatetimeestimate = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    aggregatetimespent = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    timeestimate = factory.Faker("pydecimal", left_digits=2, right_digits=1)

    progress_progress = factory.Faker("pydecimal", left_digits=3, right_digits=1)
    progress_total = factory.Faker("pydecimal", left_digits=3, right_digits=1)
    progress_percent = factory.Faker("pydecimal", left_digits=2, right_digits=1)

    aggregateprogress_progress = factory.Faker("pydecimal", left_digits=3, right_digits=1)
    aggregateprogress_total = factory.Faker("pydecimal", left_digits=3, right_digits=1)
    aggregateprogress_percent = factory.Faker("pydecimal", left_digits=2, right_digits=1)

    totaleffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    totaldeveffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    baeffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    adeffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    rdeffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    qaeffort = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    contingency = factory.Faker("pydecimal", left_digits=2, right_digits=1)
    storypoints = factory.Faker("pydecimal", left_digits=1, right_digits=1)

    testcaseno = factory.Faker("uuid4")
    testcasesuite = factory.Faker("word")
    teststepno = factory.Faker("word")
    scenariono = factory.Faker("word")
    reqfinalized = factory.Faker("word")
    approvalstatus = factory.Iterator(["Pending", "Approved", "Rejected"])
    reopen_count = factory.Faker("random_int", min=0, max=10)

    qc_check = factory.Faker("sentence")
    cvss_score = factory.Faker("pydecimal", left_digits=1, right_digits=1)
    initiated_by = factory.Faker("name")
    change_risk = factory.Faker("word")
    category_type = factory.Faker("word")
    severity = factory.Faker("word")
    initiative_detail = factory.Faker("sentence")
