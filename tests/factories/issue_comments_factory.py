# tests/factories/issue_comments_factory.py

import factory
from factory.alchemy import SQLAlchemyModelFactory
from dags.data_pipeline.dbmodels.issue import IssueComments
from tests.factories.issue_factory import IssueFactory
from tests.factories.user_factory import UserFactory
from faker import Faker

faker = Faker()

class IssueCommentsFactory(SQLAlchemyModelFactory):
    class Meta:
        model = IssueComments
        sqlalchemy_session = None
        sqlalchemy_session_persistence = "flush"

    id = factory.Sequence(lambda n: n + 50000)
    author = factory.SubFactory(UserFactory)
    updateAuthor = factory.SubFactory(UserFactory)
    body = factory.LazyFunction(lambda: {"text": faker.paragraph()})
    renderedBody = factory.Faker("text")
    created = factory.Faker("date_time_this_year", tzinfo=faker.timezone())
    updated = factory.Faker("date_time_this_year", tzinfo=faker.timezone())
    jsdPublic = factory.Faker("boolean")

    issue = factory.SubFactory(IssueFactory)
    issue_id = factory.SelfAttribute("issue.id")
    issue_key = factory.SelfAttribute("issue.key")
