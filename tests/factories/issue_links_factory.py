# tests/factories/issue_links_factory.py

import factory
from factory.alchemy import SQLAlchemyModelFactory
from dags.data_pipeline.dbmodels.issue import IssueLinks
from tests.factories.issue_factory import IssueFactory
from faker import Faker

faker = Faker()

class IssueLinksFactory(SQLAlchemyModelFactory):
    class Meta:
        model = IssueLinks
        sqlalchemy_session = None
        sqlalchemy_session_persistence = "flush"

    id = factory.Sequence(lambda n: n + 90000)
    type = factory.LazyFunction(lambda: {"name": "blocks", "inward": "is blocked by", "outward": "blocks"})

    issue = factory.SubFactory(IssueFactory)
    issue_id = factory.SelfAttribute("issue.id")
    issue_key = factory.SelfAttribute("issue.key")

    outwardIssue_id = factory.SelfAttribute("issue.id")
    outwardIssue_key = factory.SelfAttribute("issue.key")
    inwardIssue_id = factory.LazyAttribute(lambda _: faker.random_int(min=1000, max=9999))
    inwardIssue_key = factory.LazyFunction(lambda: f"PROJ-{faker.random_int(1000, 9999)}")
