# tests/factories/worklog_factory.py

import factory
from factory.alchemy import SQLAlchemyModelFactory
from datetime import datetime, timezone
from dags.data_pipeline.dbmodels.worklog import WorkLog
from tests.factories.user_factory import UserFactory
from tests.factories.issue_factory import IssueFactory


class WorkLogFactory(SQLAlchemyModelFactory):
    class Meta:
        model = WorkLog
        sqlalchemy_session = None  # to be set by fixture
        sqlalchemy_session_persistence = "flush"

    id = factory.Sequence(lambda n: n + 2000)
    # author_user = factory.SubFactory(UserFactory, accountType="app")
    # updateauthor_user = factory.SubFactory(UserFactory, accountType="atlassian")
    # issue = factory.SubFactory(IssueFactory)

    author = factory.LazyAttribute(lambda o: UserFactory(accountType="app").accountId)
    updateauthor = factory.LazyAttribute(lambda o: UserFactory(accountType="app").accountId)
    issue_id = factory.LazyAttribute(lambda o: IssueFactory().id)
    issue_key = factory.LazyAttribute(lambda o: IssueFactory().key)

    created = factory.LazyFunction(lambda: datetime.now(timezone.utc))
    updated = factory.LazyFunction(lambda: datetime.now(timezone.utc))
    started = factory.LazyFunction(lambda: datetime.now(timezone.utc))

    timeSpent = "2h"
    timeSpentSeconds = 7200
    comment = factory.LazyFunction(lambda: {"type": "doc", "content": []})
