import factory
from factory.alchemy import SQLAlchemyModelFactory
from faker import Faker
from dags.data_pipeline.dbmodels.user import User

faker = Faker()

class UserFactory(SQLAlchemyModelFactory):
    class Meta:
        model = User
        sqlalchemy_session = None  # Injected via test fixture
        sqlalchemy_session_persistence = "flush"

    accountId = factory.LazyFunction(faker.uuid4)
    accountType = factory.Iterator([choice[0] for choice in User.ACCOUNT_TYPES])
    emailAddress = factory.LazyFunction(faker.unique.email)
    displayName = factory.LazyFunction(faker.name)
    active = factory.LazyFunction(faker.boolean)
    timeZone = factory.LazyFunction(faker.timezone)
    locale = factory.LazyFunction(lambda: "en_US")

    class Params:
        atlassian = factory.Trait(accountType="atlassian")
        app = factory.Trait(accountType="app")
        customer = factory.Trait(accountType="customer")

