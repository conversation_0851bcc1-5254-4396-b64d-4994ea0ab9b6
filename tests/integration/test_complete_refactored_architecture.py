# coding=utf-8
"""
Complete integration test for the refactored architecture.

This test suite verifies:
- End-to-end integration of TaskLifecycleCoordinator with session managers
- Enhanced dependency injection containers working together
- Graceful shutdown coordination across all components
- Performance under realistic workloads
- Error recovery and resilience patterns
"""

import pytest
import allure
import asyncio
import time
from unittest.mock import MagicMock, patch, AsyncMock
from contextlib import asynccontextmanager

from dags.data_pipeline.containers import (
    EnhancedApplicationContainer,
    FullyEnhancedSessionManager,
    EntryDetails
)
from dags.data_pipeline.utility_code import TaskLifecycleCoordinator
from dags.data_pipeline.utils.shutdown_handler import ApplicationShutdownHandler


@pytest.fixture
@allure.title("Integration Test Entry Details")
def integration_entry_details():
    """Fixture providing mock database entry details for integration testing."""
    return EntryDetails(
        username="integration_user",
        password="integration_password",
        url="postgresql://integration_user:integration_password@localhost:5432/integrationdb",
        custom_properties={
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "integrationdb"
        }
    )


@pytest.fixture
@allure.title("Integration Logger")
def integration_logger():
    """Fixture providing a mock logger for integration testing."""
    return MagicMock()



@allure.epic("Integration Testing")
@allure.feature("Complete Architecture Integration")
class TestCompleteArchitectureIntegration:
    """Integration test suite for the complete refactored architecture."""
    
    @allure.story("End-to-End Task Processing")
    @allure.title("Should process tasks with full coordination")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_end_to_end_task_processing(self, mock_create_sync_engine, mock_create_async_engine, 
                                            integration_entry_details, integration_logger):
        """Test complete end-to-end task processing with coordination."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create enhanced application container
        from dependency_injector import containers, providers
        
        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(integration_logger)
        
        mock_keepass_container = containers.DynamicContainer()
        mock_keepass_container.pg_rw = providers.Object(integration_entry_details)
        mock_keepass_container.pg_ro = providers.Object(integration_entry_details)
        
        container = EnhancedApplicationContainer()
        container.schema.override("integration_test")
        container.logger_container.override(mock_logger_container)
        container.keepass_container.override(mock_keepass_container)
        
        # Get components from container
        session_manager = container.database_rw_enhanced()
        task_coordinator = container.task_coordinator()
        shutdown_handler = container.shutdown_handler()
        
        # Simulate task processing workflow
        async def simulate_task_processing():
            """Simulate the complete task processing workflow."""
            
            # 1. Register application component with shutdown handler
            await shutdown_handler.register_component("task_processor")
            
            # 2. Create and register monitors
            async def mock_monitor():
                try:
                    while not task_coordinator.is_shutdown_requested():
                        await asyncio.sleep(0.01)
                except asyncio.CancelledError:
                    integration_logger.info("Monitor cancelled gracefully")
                    raise
                    
            async def mock_db_monitor():
                try:
                    while not task_coordinator.is_shutdown_requested():
                        # Simulate database monitoring
                        async with session_manager.async_session() as session:
                            # Mock database query
                            await asyncio.sleep(0.005)
                        await asyncio.sleep(0.01)
                except asyncio.CancelledError:
                    integration_logger.info("DB Monitor cancelled gracefully")
                    raise
                    
            monitor_task = asyncio.create_task(mock_monitor())
            db_monitor_task = asyncio.create_task(mock_db_monitor())
            
            await task_coordinator.register_monitor(monitor_task, "monitor")
            await task_coordinator.register_monitor(db_monitor_task, "db_monitor")
            
            # 3. Execute multiple tasks with coordination
            task_results = []
            
            for i in range(5):
                task_id = await task_coordinator.register_task(f"integration_task_{i}")
                
                try:
                    # Simulate task execution with database operations
                    async with session_manager.async_session() as session:
                        # Mock database operations
                        await asyncio.sleep(0.01)
                        result = f"task_{i}_completed"
                        
                    await task_coordinator.mark_task_completed(task_id, result)
                    task_results.append(result)
                    
                except Exception as e:
                    await task_coordinator.mark_task_failed(task_id, e)
                    raise
                    
            # 4. Wait for coordinated shutdown
            await task_coordinator.wait_for_all_tasks_completion(timeout=10.0)
            
            # 5. Unregister from shutdown handler
            await shutdown_handler.unregister_component("task_processor")
            
            return task_results
            
        # Execute the complete workflow
        start_time = time.perf_counter()
        
        with patch('dags.data_pipeline.containers.sessionmaker') as mock_sessionmaker:
            mock_session = AsyncMock()
            mock_session_factory = MagicMock(return_value=mock_session)
            mock_sessionmaker.return_value = mock_session_factory
            
            results = await simulate_task_processing()
            
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # Verify results
        assert len(results) == 5, "All tasks should complete successfully"
        assert all("completed" in result for result in results), "All tasks should have completion status"
        assert task_coordinator.get_active_task_count() == 0, "No tasks should be active after completion"
        assert task_coordinator.get_completed_task_count() == 5, "All tasks should be marked as completed"
        
        # Verify performance
        assert execution_time < 5.0, "Complete workflow should execute in <5 seconds"
        
        # Cleanup
        await session_manager.aclose()
        
        allure.attach(f"Execution time: {execution_time:.4f}s", name="execution_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Tasks completed: {len(results)}", name="tasks_completed", attachment_type=allure.attachment_type.TEXT)
        
    @allure.story("Connection Recovery Integration")
    @allure.title("Should handle connection failures gracefully in full workflow")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_connection_recovery_integration(
            self, mock_create_sync_engine, mock_create_async_engine,
                 integration_entry_details, integration_logger
    ):
        """Test connection recovery integration in complete workflow."""
        # Setup mocks with connection failure simulation
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create session manager with connection recovery
        session_manager = FullyEnhancedSessionManager(
            integration_entry_details, "recovery_test", True, integration_logger
        )
        
        task_coordinator = TaskLifecycleCoordinator(integration_logger)
        
        # Simulate connection failure and recovery
        from sqlalchemy.exc import InterfaceError
        
        connection_failures = 0
        max_failures = 2

        def session_factory_side_effect(*args, **kwargs):
            nonlocal connection_failures
            if connection_failures < max_failures:
                connection_failures += 1
                raise InterfaceError("Connection failed", "test", "test")
            else:
                # Simulate working session context
                mock_session = AsyncMock()
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__.return_value = mock_session
                mock_context_manager.__aexit__.return_value = None
                return mock_context_manager

        async def simulate_task_with_connection_issues():
            """Simulate task execution with connection issues."""
            nonlocal connection_failures
            
            task_id = await task_coordinator.register_task("recovery_test_task")
            
            try:
                # Mock session creation with intermittent failures
                with patch('dags.data_pipeline.containers.sessionmaker') as mock_sessionmaker:
                    # mock_session = AsyncMock()
                    #
                    # # Simulate connection failure on first attempts
                    # if connection_failures < max_failures:
                    #     connection_failures += 1
                    #     mock_session_factory = MagicMock(side_effect=InterfaceError("Connection failed", "test", "test"))
                    # else:
                    #     # mock_session_factory = MagicMock(return_value=mock_session)
                    #     mock_context_manager = AsyncMock()
                    #     mock_context_manager.__aenter__.return_value = mock_session
                    #     mock_context_manager.__aexit__.return_value = None
                    #     mock_session_factory = MagicMock(return_value=mock_context_manager)
                        
                    mock_sessionmaker.return_value = MagicMock(side_effect=session_factory_side_effect)
                    
                    # This should automatically retry on connection failures
                    async with session_manager.async_session() as session:
                        # Mock successful database operation after recovery
                        await asyncio.sleep(0.01)
                        result = "recovered_successfully"
                        
                await task_coordinator.mark_task_completed(task_id, result)
                return result
                
            except Exception as e:
                await task_coordinator.mark_task_failed(task_id, e)
                raise
                
        # Execute task with connection recovery
        start_time = time.perf_counter()
        
        # Mock the connection error handling
        with patch.object(session_manager, '_handle_connection_error', return_value=True) as mock_handle_error:
            with patch.object(session_manager, '_recreate_async_engine') as mock_recreate:
                result = await simulate_task_with_connection_issues()
                
        end_time = time.perf_counter()
        recovery_time = end_time - start_time
        
        # Wait for coordinated shutdown
        await task_coordinator.wait_for_all_tasks_completion(timeout=5.0)
        
        # Verify recovery behavior
        assert result == "recovered_successfully", "Task should complete successfully after recovery"
        assert task_coordinator.get_completed_task_count() == 1, "Task should be marked as completed"
        assert 2.0 < recovery_time < 4.0, "Recovery should complete quickly"
        
        # Verify connection recovery was attempted
        if connection_failures > 0:
            assert mock_handle_error.call_count >= 1, "Connection error handling should be called"
            assert mock_recreate.call_count >= 1, "Engine recreation should be attempted"
            
        # Cleanup
        await session_manager.aclose()
        
        allure.attach(f"Recovery time: {recovery_time:.4f}s", name="recovery_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Connection failures: {connection_failures}", name="connection_failures", attachment_type=allure.attachment_type.TEXT)
        
    @allure.story("Shutdown Coordination")
    @allure.title("Should coordinate shutdown across all components")
    @pytest.mark.asyncio
    async def test_shutdown_coordination(self, integration_logger):
        """Test coordinated shutdown across all components."""
        # Create components
        task_coordinator = TaskLifecycleCoordinator(integration_logger)
        shutdown_handler = ApplicationShutdownHandler()
        
        # Register multiple components
        await shutdown_handler.register_component("component_1")
        await shutdown_handler.register_component("component_2")
        await shutdown_handler.register_component("component_3")
        
        # Create multiple monitors
        monitors = []
        for i in range(3):
            async def mock_monitor():
                try:
                    while not task_coordinator.is_shutdown_requested():
                        await asyncio.sleep(0.01)
                except asyncio.CancelledError:
                    raise
                    
            monitor_task = asyncio.create_task(mock_monitor())
            monitors.append(monitor_task)
            await task_coordinator.register_monitor(monitor_task, f"monitor_{i}")
            
        # Execute tasks and trigger shutdown
        task_ids = []
        for i in range(3):
            task_id = await task_coordinator.register_task(f"shutdown_test_task_{i}")
            task_ids.append(task_id)
            
        # Complete all tasks to trigger shutdown
        for i, task_id in enumerate(task_ids):
            await task_coordinator.mark_task_completed(task_id, f"result_{i}")
            
        # Simulate component shutdown
        async def simulate_component_shutdown():
            await asyncio.sleep(0.1)  # Simulate some cleanup work
            await shutdown_handler.unregister_component("component_1")
            await asyncio.sleep(0.1)
            await shutdown_handler.unregister_component("component_2")
            await asyncio.sleep(0.1)
            await shutdown_handler.unregister_component("component_3")
            
        # Start component shutdown simulation
        shutdown_task = asyncio.create_task(simulate_component_shutdown())
        
        # Measure coordinated shutdown time
        start_time = time.perf_counter()
        
        # Wait for task coordinator shutdown
        await task_coordinator.wait_for_all_tasks_completion(timeout=5.0)
        
        # Wait for application shutdown
        with patch.object(shutdown_handler, '_cleanup_session_managers'):
            await shutdown_handler.shutdown(timeout=5.0)
            
        # Wait for component simulation to complete
        await shutdown_task
        
        end_time = time.perf_counter()
        shutdown_time = end_time - start_time
        
        # Verify coordinated shutdown
        assert all(monitor.cancelled() or monitor.done() for monitor in monitors), "All monitors should be shut down"
        assert task_coordinator.is_shutdown_requested(), "Task coordinator should be in shutdown state"
        assert shutdown_handler.is_shutting_down(), "Shutdown handler should be in shutdown state"
        assert len(shutdown_handler._active_components) == 0, "All components should be unregistered"
        
        # Verify performance
        assert shutdown_time < 3.0, "Coordinated shutdown should complete in <3 seconds"
        
        allure.attach(f"Shutdown time: {shutdown_time:.4f}s", name="shutdown_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Monitors shut down: {len(monitors)}", name="monitors_shutdown", attachment_type=allure.attachment_type.TEXT)


@allure.epic("Integration Testing")
@allure.feature("Production Readiness")
class TestProductionReadiness:
    """Test suite to verify production readiness of the refactored architecture."""
    
    @allure.story("Load Testing")
    @allure.title("Should handle realistic production load")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_production_load_simulation(self, mock_create_sync_engine, mock_create_async_engine, 
                                            integration_entry_details, integration_logger):
        """Simulate realistic production load."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create multiple session managers (simulating multiple schemas/projects)
        session_managers = []
        for i in range(5):
            manager = FullyEnhancedSessionManager(
                integration_entry_details, f"project_schema_{i}", True, integration_logger
            )
            session_managers.append(manager)
            
        task_coordinator = TaskLifecycleCoordinator(integration_logger)
        
        # Simulate production workload
        async def simulate_production_workload():
            """Simulate realistic production workload with multiple concurrent operations."""
            
            # Create monitors for each schema
            monitors = []
            for i, manager in enumerate(session_managers):
                async def schema_monitor(schema_id=i):
                    try:
                        while not task_coordinator.is_shutdown_requested():
                            # Simulate monitoring database operations
                            async with session_managers[schema_id].async_session() as session:
                                await asyncio.sleep(0.001)  # Very fast DB check
                            await asyncio.sleep(0.01)
                    except asyncio.CancelledError:
                        raise
                        
                monitor_task = asyncio.create_task(schema_monitor())
                monitors.append(monitor_task)
                await task_coordinator.register_monitor(monitor_task, f"schema_monitor_{i}")
                
            # Execute concurrent tasks across multiple schemas
            async def process_schema_tasks(schema_id, task_count):
                """Process tasks for a specific schema."""
                results = []
                for task_num in range(task_count):
                    task_id = await task_coordinator.register_task(f"schema_{schema_id}_task_{task_num}")
                    
                    try:
                        # Simulate database-intensive task
                        async with session_managers[schema_id].async_session() as session:
                            # Mock multiple database operations
                            for _ in range(3):
                                await asyncio.sleep(0.001)
                                
                        result = f"schema_{schema_id}_task_{task_num}_completed"
                        await task_coordinator.mark_task_completed(task_id, result)
                        results.append(result)
                        
                    except Exception as e:
                        await task_coordinator.mark_task_failed(task_id, e)
                        
                return results
                
            # Run concurrent workloads
            with patch('dags.data_pipeline.containers.sessionmaker') as mock_sessionmaker:
                mock_session = AsyncMock()
                mock_session_factory = MagicMock(return_value=mock_session)
                mock_sessionmaker.return_value = mock_session_factory
                
                # Process 10 tasks per schema concurrently
                schema_tasks = [process_schema_tasks(i, 10) for i in range(5)]
                all_results = await asyncio.gather(*schema_tasks)
                
            # Wait for coordinated shutdown
            await task_coordinator.wait_for_all_tasks_completion(timeout=10.0)
            
            return all_results
            
        # Execute production load simulation
        start_time = time.perf_counter()
        results = await simulate_production_workload()
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        total_tasks = sum(len(schema_results) for schema_results in results)
        throughput = total_tasks / execution_time if execution_time > 0 else 0
        
        # Verify production readiness
        assert total_tasks == 50, "All 50 tasks should complete successfully"
        assert task_coordinator.get_completed_task_count() == 50, "All tasks should be marked as completed"
        assert execution_time < 10.0, "Production load should complete in reasonable time"
        assert throughput > 10, "Should maintain reasonable throughput (>10 tasks/s)"
        
        # Cleanup
        for manager in session_managers:
            await manager.aclose()
            
        allure.attach(f"Execution time: {execution_time:.4f}s", name="execution_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Total tasks: {total_tasks}", name="total_tasks", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Throughput: {throughput:.1f} tasks/s", name="throughput", attachment_type=allure.attachment_type.TEXT)
