#!/usr/bin/env python3
"""
Complete test of the multi-schema setup.
"""

import subprocess
import sys

def run_command(cmd):
    """Run a command and return success/failure."""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Success")
        return True
    else:
        print(f"❌ Failed: {result.stderr}")
        return False

def test_complete_setup():
    """Test the complete multi-schema setup."""
    
    print("🧪 Testing Complete Multi-Schema Setup")
    print("=" * 50)
    
    # Step 1: Clean database
    print("\n1. Cleaning database...")
    if not run_command("uv run python clean_database.py"):
        return False
    
    # Step 2: Apply public schema migration
    print("\n2. Applying public schema migration...")
    if not run_command("uv run alembic -x schema=public upgrade head"):
        return False
    
    # Step 3: Apply plat schema migration
    print("\n3. Applying plat schema migration...")
    if not run_command("uv run alembic -x schema=plat upgrade head"):
        return False
    
    # Step 4: Test downgrade for plat
    print("\n4. Testing downgrade for plat...")
    if not run_command("uv run alembic -x schema=plat downgrade e7c9794475b7"):
        return False
    
    # Step 5: Test upgrade for plat again
    print("\n5. Testing upgrade for plat again...")
    if not run_command("uv run alembic -x schema=plat upgrade head"):
        return False
    
    # Step 6: Apply plp schema migration
    print("\n6. Applying plp schema migration...")
    if not run_command("uv run alembic -x schema=plp upgrade head"):
        return False
    
    # Step 7: Apply acq schema migration
    print("\n7. Applying acq schema migration...")
    if not run_command("uv run alembic -x schema=acq upgrade head"):
        return False
    
    # Step 8: Check final state
    print("\n8. Checking final database state...")
    if not run_command("uv run python check_database_state.py"):
        return False
    
    print("\n🎉 All tests passed!")
    return True

if __name__ == "__main__":
    success = test_complete_setup()
    sys.exit(0 if success else 1)
