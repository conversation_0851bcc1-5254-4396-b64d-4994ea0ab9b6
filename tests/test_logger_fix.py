#!/usr/bin/env python3
"""
Simple test to verify the logger caching fix works correctly.
This test focuses on the core file descriptor management without requiring pandas.
"""

import os
import sys
import tempfile
import time
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_logger_creation():
    """Test that multiple logger creations don't create excessive file handles."""
    print("🧪 Testing logger creation and caching...")
    
    try:
        # Import the module to test the logger caching
        from dags.data_pipeline.dataframe_utils.dataframe_debugger import (
            LoggerMixin,
            cleanup_logger_cache
        )
        # Access the cache through LoggerMixin
        _logger_cache = LoggerMixin._logger_cache
        _logger_cache_lock = LoggerMixin._logger_cache_lock
        print("✅ Successfully imported logger cache components")
        
        # Test that cache starts empty
        with _logger_cache_lock:
            initial_cache_size = len(_logger_cache)
        print(f"📊 Initial cache size: {initial_cache_size}")
        
        # Import the DataframeDebugger class
        from dags.data_pipeline.dataframe_utils.dataframe_debugger import DataframeDebugger
        
        # Create multiple debugger instances
        debuggers = []
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"Creating 10 DataframeDebugger instances...")
            
            for i in range(10):
                debugger = DataframeDebugger(path=temp_dir, log_level=logging.WARNING)
                debuggers.append(debugger)
            
            # Check cache size - should be 1 (shared logger)
            with _logger_cache_lock:
                cache_size_after = len(_logger_cache)
            
            print(f"📊 Cache size after creating 10 instances: {cache_size_after}")
            
            # Clean up debuggers
            for debugger in debuggers:
                debugger.stop()
            
            # Test cleanup
            cleanup_logger_cache()
            
            with _logger_cache_lock:
                final_cache_size = len(_logger_cache)
            
            print(f"📊 Cache size after cleanup: {final_cache_size}")
            
            # Verify results
            if cache_size_after <= 2:  # Should be 1, but allow some tolerance
                print("✅ Logger caching is working correctly")
                return True
            else:
                print(f"❌ Too many cached loggers: {cache_size_after}")
                return False
                
    except ImportError as e:
        print(f"❌ Failed to import required modules: {e}")
        return False
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return False


def test_file_creation():
    """Test that log files are created properly."""
    print("\n🧪 Testing log file creation...")
    
    try:
        from dags.data_pipeline.dataframe_utils.dataframe_debugger import DataframeDebugger
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a debugger instance
            debugger = DataframeDebugger(path=temp_dir, log_level=logging.INFO)
            
            # Log a test message
            debugger.logger.info("Test log message")
            
            # Check if shared log file exists
            log_dir = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
            shared_log_file = log_dir / "dataframe_debugger_shared.log"
            
            # Give it a moment for the file to be created
            time.sleep(0.5)
            
            debugger.stop()
            
            if shared_log_file.exists():
                print(f"✅ Shared log file created: {shared_log_file}")
                print(f"📁 File size: {shared_log_file.stat().st_size} bytes")
                return True
            else:
                print(f"❌ Shared log file not found: {shared_log_file}")
                # Check if any debugger log files exist
                if log_dir.exists():
                    log_files = list(log_dir.glob("debugger_*.log"))
                    print(f"📁 Found {len(log_files)} debugger log files")
                    for log_file in log_files[:5]:  # Show first 5
                        print(f"   - {log_file.name}")
                return False
                
    except Exception as e:
        print(f"💥 Error testing file creation: {e}")
        return False


def test_import_structure():
    """Test that all required imports work correctly."""
    print("\n🧪 Testing import structure...")
    
    try:
        # Test basic imports
        from dags.data_pipeline.dataframe_utils.dataframe_debugger import DataframeDebugger
        print("✅ DataframeDebugger import successful")

        from dags.data_pipeline.dataframe_utils.dataframe_debugger import cleanup_logger_cache
        print("✅ cleanup_logger_cache import successful")
        
        # Test SafeTimedRotatingFileHandler import
        try:
            from dags.data_pipeline.custom_logger import SafeTimedRotatingFileHandler
            print("✅ SafeTimedRotatingFileHandler import successful")
        except ImportError:
            print("⚠️  SafeTimedRotatingFileHandler not available (using fallback)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"💥 Unexpected error during import: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting logger fix verification tests...")
    print(f"🖥️  Operating System: {os.name}")
    print(f"🐍 Python Version: {sys.version}")
    
    # Run tests
    tests = [
        ("Import Structure", test_import_structure),
        ("Logger Creation and Caching", test_logger_creation),
        ("File Creation", test_file_creation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 TEST SUMMARY")
    print('='*60)
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Logger fix is working correctly.")
        print("\n📝 Key improvements:")
        print("   • Shared logger prevents file descriptor leaks")
        print("   • Proper cleanup mechanism implemented")
        print("   • SafeTimedRotatingFileHandler integration")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
