#!/usr/bin/env python3
"""
Test script for debugging JIRA data processing issues in consume_* functions.

This script provides various test scenarios to help debug DataFrame creation
and transformation issues in the consume functions using real JIRA data.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dags.data_pipeline.debug_queue_processor import (
    debug_consume_functions_with_jira_data,
    debug_specific_consume_function,
    QueueDebugProcessor
)
from dags.data_pipeline.containers import ApplicationContainer, LoggerContainer, JiraEntryDetailsContainer, \
    IssueFieldsContainer, QueueContainer, KeePassContainer
from logging import Logger


async def test_all_consume_functions():
    """Test 1: Test all consume functions with real JIRA data (inspection only)."""
    print("=" * 60)
    print("TEST 1: All Consume Functions with JIRA Data")
    print("=" * 60)

    # Wire the container
    # container = ApplicationContainer()
    # container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])

    try:
        await debug_consume_functions_with_jira_data(
            project_key="PLAT",
            max_issues=3,        # Fetch 3 issues
            process_data=False   # Just inspect, don't process to DB
        )
    except Exception as e:
        print(f"Error in test 1: {e}")
        import traceback
        traceback.print_exc()


async def test_specific_consume_function():
    """Test 2: Test a specific consume function with real JIRA data."""
    print("=" * 60)
    print("TEST 2: Specific Consume Function Test")
    print("=" * 60)

    # Wire the container
    # container = ApplicationContainer()
    # container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])

    try:
        # Test consume_changelog specifically
        await debug_specific_consume_function(
            consume_function_name="consume_issue_links",
            project_key="PLAT",
            max_issues=2,        # Fetch 2 issues
            process_data=False   # Just inspect, don't process to DB
        )
    except Exception as e:
        print(f"Error in test 2: {e}")
        import traceback
        traceback.print_exc()


async def test_with_custom_jql():
    """Test 3: Test with custom JQL query."""
    print("=" * 60)
    print("TEST 3: Custom JQL Query Test")
    print("=" * 60)

    # Wire the container
    # container = ApplicationContainer()
    # container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])

    try:
        # Test with a specific issue or custom criteria
        custom_jql = "project = PLAT AND updated > '2024-01-01' ORDER BY updated DESC"

        await debug_consume_functions_with_jira_data(
            project_key="PLAT",
            jql_override=custom_jql,
            max_issues=5,
            consume_functions=["consume_worklog", "consume_comment"],  # Test specific functions
            process_data=False
        )
    except Exception as e:
        print(f"Error in test 3: {e}")
        import traceback
        traceback.print_exc()


async def test_with_database_processing():
    """Test 4: Actually process data to database (use with caution)."""
    print("=" * 60)
    print("TEST 4: Database Processing (CAUTION)")
    print("=" * 60)

    # Wire the container
    # container = ApplicationContainer()
    # container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])

    try:
        await debug_consume_functions_with_jira_data(
            project_key="PLAT",
            max_issues=1,        # Only 1 issue for safety
            consume_functions=["consume_issue"],  # Test only one function
            process_data=True    # Actually process to database
        )
    except Exception as e:
        print(f"Error in test 4: {e}")
        import traceback
        traceback.print_exc()


def print_usage():
    """Print usage instructions."""
    print("JIRA Data Processing Debug Test Script")
    print("=" * 50)
    print("Usage: python test_queue_debug.py [test_number]")
    print()
    print("Available tests:")
    print("  1 - Test All Consume Functions with JIRA Data (safe)")
    print("  2 - Test Specific Consume Function (safe)")
    print("  3 - Test with Custom JQL Query (safe)")
    print("  4 - Test with Database Processing (use with caution)")
    print("  all - Run all safe tests (1, 2, 3)")
    print()
    print("If no test number is provided, runs test 1")
    print()
    print("Examples:")
    print("  python test_queue_debug.py 1")
    print("  python test_queue_debug.py 2")
    print("  python test_queue_debug.py all")
    print()
    print("This script fetches real JIRA data and tests the consume_* functions")
    print("to help debug DataFrame creation and transformation issues.")
    print()


async def main():
    """Main function to run the selected test."""
    
    # Check command line arguments
    if len(sys.argv) > 1:
        test_arg = sys.argv[1].lower()
    else:
        test_arg = "1"  # Default to test 1
    
    if test_arg in ["help", "-h", "--help"]:
        print_usage()
        return
    
    print("Queue Debug Test Script Starting...")
    print(f"Python path: {sys.path[0]}")
    print(f"Working directory: {os.getcwd()}")
    print(f"test_arg: {test_arg}")

    
    try:
        if test_arg == "1":
            await test_all_consume_functions()
        elif test_arg == "2":
            await test_specific_consume_function()
        elif test_arg == "3":
            await test_with_custom_jql()
        elif test_arg == "4":
            print("WARNING: This test will actually process data to the database!")
            response = input("Are you sure you want to continue? (yes/no): ")
            if response.lower() in ['yes', 'y']:
                await test_with_database_processing()
            else:
                print("Test 4 cancelled.")
        elif test_arg == "all":
            print("Running all safe tests...")
            await test_all_consume_functions()
            await asyncio.sleep(2)
            print("\n" + "="*60)
            await test_specific_consume_function()
            await asyncio.sleep(2)
            print("\n" + "="*60)
            await test_with_custom_jql()
            print("\nSkipping database test (test 4) in 'all' mode for safety.")
        else:
            print(f"Unknown test: {test_arg}")
            print_usage()
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nTest completed.")

keepass_container = KeePassContainer()
logger_container = LoggerContainer()
logger_container.wire(modules=[__name__])
logger_container.init_resources()
jira_container = JiraEntryDetailsContainer()
jira_container.wire([__name__])
jira_container.keepass.override(keepass_container.keepass_manager)
extract_container = IssueFieldsContainer()

application_container = ApplicationContainer()
application_container.wire(modules=[__name__])

queue_container = QueueContainer()
queue_container.wire([__name__])

if __name__ == "__main__":
    asyncio.run(main())
