import allure
import pandas as pd
import pytest
from sqlalchemy import and_
from sqlalchemy.dialects.postgresql import insert

from dags.data_pipeline.dbmodels.initiativeattribute import InitiativeAttribute
from tests.upsert_conflict_condition.test_utils import upsert_data, fetch_column_value, verify_upsert, pg_session

@allure.feature("Upsert Conflict Condition")
@allure.story("Test upsert with conflict condition for initiative_attribute table")
class TestUpsertInitiativeAttribute:

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("initiative_attribute: test upsert with conflict condition")
    @allure.label("requirement", "AIR-51")
    @pytest.mark.parametrize(
        "test_data, conflict_condition, expected_updated, should_update",
        [

            (
                    [
                        [10346, "TRAIN-1", "2020-03-23", "2022-03-21", "Managed Services"],
                        [10346, "TRAIN-1", "2020-03-23", "2019-03-21", "Managed Services"],
                        [10346, "TRAIN-1", "2020-03-23", "2020-03-23", "Managed Services"]
                    ],
                    and_(InitiativeAttribute.updated < insert(InitiativeAttribute).excluded.updated),
                    [
                        pd.Timestamp("2022-03-21 00:00:00", tz="Asia/Kolkata"),
                        None, None
                    ],
                    [
                        True, False, False
                    ]
            ),
        ]
    )
    def test_upsert_with_conflict_condition_initiative_attribute(self,
            pg_session, test_data, conflict_condition, expected_updated, should_update
    ):
        """
        Test with_conflict_condition for upsert functionality for InitiativeAttribute table.
        """
        # Extract initiative_id dynamically
        for i, data in enumerate(test_data):
            initiative_id = data[0]

            # Perform upsert
            upsert_data(
                session=pg_session,
                model=InitiativeAttribute,
                data=data,
                conflict_condition=conflict_condition,
                index_elements=["initiative_id"],
                optional_columns=["project"]
            )

            # Verify upsert results
            filter_condition = InitiativeAttribute.initiative_id == initiative_id

            # Fetch the `updated` value dynamically if no update is expected
            if not should_update[i]:
                expected_updated[i] = fetch_column_value(pg_session, InitiativeAttribute, filter_condition, "updated")

            expected_values = {
                "project": "Managed Services",
                "updated": expected_updated[i],
            }
            verify_upsert(pg_session, InitiativeAttribute, filter_condition, expected_values)
