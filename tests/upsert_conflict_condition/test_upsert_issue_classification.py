# coding=utf-8
from pathlib import Path

import allure
import pytest
import yaml
from sqlalchemy import or_, and_
from sqlalchemy.dialects.postgresql import insert

from dags.data_pipeline.dbmodels.issueclassification import IssueClassification
from tests.upsert_conflict_condition.test_utils import upsert_data, fetch_column_value, verify_upsert, pg_session

# Load test data from YAML file
# Get the absolute path of the YAML file
# yaml_file_name = os.path.join(os.path.dirname(__file__), 'test_upsert_issue_classification.yaml')
# with open(yaml_file_name, 'r') as file:
#     test_cases = yaml.safe_load(file)['tests']

yaml_file_path = Path(__file__).parent / 'test_upsert_issue_classification.yaml'

# Load test data from YAML file
with yaml_file_path.open('r') as file:
    test_cases = yaml.safe_load(file)['tests']


# Helper function to create conflict condition
def create_conflict_condition(columns):
    conditions = []
    for column in columns:
        conditions.append(
            or_(
                and_(
                    getattr(IssueClassification, column['column']).is_(None),
                    getattr(insert(IssueClassification.__table__).excluded, column['column']).is_not(None),
                ),
                and_(
                    getattr(IssueClassification, column['column']).is_not(None),
                    getattr(insert(IssueClassification.__table__).excluded, column['column']).is_(None),
                ),
                getattr(IssueClassification, column['column']) !=
                getattr(insert(IssueClassification.__table__).excluded, column['column']),
            )
        )
    return or_(*conditions)


@pytest.mark.parametrize(
    "test_data, expected_updated, should_update, conflict_condition",
    [
        (
                case['test_data'],
                case['expected_updated'],
                case['should_update'],
                create_conflict_condition(case['conflict_condition'])
        ) for case in test_cases
    ],
    ids=[case['test_case'] for case in test_cases]
)
@allure.title("Upsert Test for Issue Classification")
@allure.description("Test the upsert functionality considering null and non-null values in conflict conditions.")
@allure.severity(allure.severity_level.CRITICAL)
def test_upsert_with_conflict_condition_issue_classification(
        pg_session, test_data, expected_updated, should_update, conflict_condition
):
    """
    Generalized test for upsert functionality for InitiativeAttribute table.
    """
    with allure.step("Iterate over test data and perform upserts"):
        for i, data in enumerate(test_data):
            # Extract initiative_id dynamically
            initiative_id = data[0]

            # Perform upsert
            upsert_data(
                session=pg_session,
                model=IssueClassification,
                data=data,
                conflict_condition=conflict_condition,
                index_elements=["id"],
                optional_columns=["initiative_id", "epic_id", "standard_id", "subtask_id", "epic_key"]
            )

            # Verify upsert results
            filter_condition = IssueClassification.id == initiative_id

            # Fetch the `updated` value dynamically if no update is expected
            if not should_update[i]:
                expected_updated[i] = fetch_column_value(pg_session, IssueClassification, filter_condition, "epic_key")

            expected_values = {
                "epic_key": expected_updated[i],
            }
            verify_upsert(pg_session, IssueClassification, filter_condition, expected_values)
