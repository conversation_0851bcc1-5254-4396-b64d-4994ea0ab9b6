#!/usr/bin/env python3
"""
Test script to verify the multi-schema setup is working correctly.
"""

import os
import sys
import subprocess

def test_schema_setup():
    """Test that the schema setup is working correctly."""
    
    print("🧪 Testing Multi-Schema Setup")
    print("=" * 50)
    
    # Test 1: Create a fresh database and test the complete flow
    print("\n1. Testing fresh database setup...")
    
    # Reset the database state
    print("   Resetting database state...")
    result = subprocess.run(['uv', 'run', 'python', 'reset_alembic_state.py'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print(f"   ❌ Failed to reset database: {result.stderr}")
        return False
    
    # Test 2: Apply public schema migration
    print("   Applying public schema migration...")
    result = subprocess.run(['uv', 'run', 'alembic', '-x', 'schema=public', 'upgrade', 'head'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print(f"   ❌ Failed to apply public schema migration: {result.stderr}")
        return False
    
    # Test 3: Apply custom schema migrations
    for schema in ['plat', 'plp', 'acq']:
        print(f"   Applying {schema} schema migration...")
        result = subprocess.run(['uv', 'run', 'alembic', '-x', f'schema={schema}', 'upgrade', 'head'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"   ❌ Failed to apply {schema} schema migration: {result.stderr}")
            return False
    
    # Test 4: Test downgrade
    print("   Testing downgrade for plat schema...")
    result = subprocess.run(['uv', 'run', 'alembic', '-x', 'schema=plat', 'downgrade', 'd691e4902e12'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print(f"   ❌ Failed to downgrade plat schema: {result.stderr}")
        return False
    
    # Test 5: Test upgrade again
    print("   Testing upgrade for plat schema...")
    result = subprocess.run(['uv', 'run', 'alembic', '-x', 'schema=plat', 'upgrade', 'head'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print(f"   ❌ Failed to upgrade plat schema: {result.stderr}")
        return False
    
    print("   ✅ All tests passed!")
    return True

if __name__ == "__main__":
    success = test_schema_setup()
    if success:
        print("\n🎉 Multi-schema setup is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Multi-schema setup has issues!")
        sys.exit(1)
