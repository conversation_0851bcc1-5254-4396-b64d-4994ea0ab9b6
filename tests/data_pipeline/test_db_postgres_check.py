"""
Test that all SQLAlchemy models in dags.data_pipeline.dbmodels:
- Have their tables created in a fresh PostgreSQL database
- Support basic insert and select operations
Uses pytest-postgresql for DB isolation.
"""
import os
import subprocess
import sys

import factory
import pytest
import allure
from factory.alchemy import SQLAlchemyModelFactory
from faker import Faker
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError
from dags.data_pipeline.dbmodels.base import Base
from tests.factories.user_factory import UserFactory

# Windows compatibility fixes
if sys.platform == "win32":
    # Fix for os.killpg not existing on Windows
    def killpg_windows(pgid, sig):
        try:
            os.kill(pgid, sig)
        except (PermissionError, ProcessLookupError, OSError):
            try:
                subprocess.run(['taskkill', '/F', '/PID', str(pgid)],
                               capture_output=True, check=False)
            except:
                pass

    os.killpg = killpg_windows

# Try to import pytest-postgresql, skip tests if not available
try:
    from pytest_postgresql import factories

    postgresql_proc = factories.postgresql_proc(
        port=54329,
        postgres_options="-F",
        unixsocketdir="C:/vishal/log",
    )
    postgresql = factories.postgresql('postgresql_proc', load=[])
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False
    postgresql = None

# Import all models to ensure they're registered
import dags.data_pipeline.dbmodels.user  # noqa: F401
import dags.data_pipeline.dbmodels.issue  # noqa: F401
import dags.data_pipeline.dbmodels.initiativeattribute  # noqa: F401
import dags.data_pipeline.dbmodels.issueclassification  # noqa: F401
import dags.data_pipeline.dbmodels.gs_jira_issues  # noqa: F401
import dags.data_pipeline.dbmodels.user_addons  # noqa: F401
import dags.data_pipeline.dbmodels.mixins  # noqa: F401
import dags.data_pipeline.dbmodels.worklog  # noqa: F401
import dags.data_pipeline.dbmodels.versions  # noqa: F401
import dags.data_pipeline.dbmodels.changelog  # noqa: F401
import dags.data_pipeline.dbmodels.rundetails  # noqa: F401
import dags.data_pipeline.dbmodels.teams  # noqa: F401
import dags.data_pipeline.dbmodels.allboards  # noqa: F401
import dags.data_pipeline.dbmodels.sprint  # noqa: F401
import dags.data_pipeline.dbmodels.log_entry  # noqa: F401
import dags.data_pipeline.dbmodels.task_monitoring  # noqa: F401


# Get all registered model classes
MODELS = [
    mapper.class_
    for mapper in Base.registry.mappers
    if hasattr(mapper.class_, '__table__')
]

@pytest.fixture
def user_factory(test_session):
    """Provide a UserFactory bound to the test session."""
    UserFactory._meta.sqlalchemy_session = test_session
    return UserFactory

@pytest.fixture
def issue_factory(test_session):
    from tests.factories.issue_factory import IssueFactory

    IssueFactory._meta.sqlalchemy_session = test_session
    UserFactory._meta.sqlalchemy_session = test_session
    return IssueFactory

@pytest.fixture
def issue_comment_factory(test_session):
    from tests.factories.issue_comments_factory import IssueCommentsFactory
    IssueCommentsFactory._meta.sqlalchemy_session = test_session
    return IssueCommentsFactory

@pytest.fixture
def issue_link_factory(test_session):
    from tests.factories.issue_links_factory import IssueLinksFactory
    IssueLinksFactory._meta.sqlalchemy_session = test_session
    return IssueLinksFactory

@pytest.fixture
def changelog_factory(test_session):
    from tests.factories.changelog_factory import ChangelogJSONFactory
    ChangelogJSONFactory._meta.sqlalchemy_session = test_session
    return ChangelogJSONFactory

@pytest.fixture
def worklog_factory(test_session):
    from tests.factories.worklog_factory import WorkLogFactory
    WorkLogFactory._meta.sqlalchemy_session = test_session
    return WorkLogFactory


@pytest.fixture
def test_engine(postgresql):
    """Create a test database engine using pytest-postgresql with psycopg2."""
    if not POSTGRESQL_AVAILABLE:
        pytest.skip("pytest-postgresql not available")

    # Create engine from postgresql fixture with psycopg2 driver
    # Use the legacy psycopg2 driver that's compatible with SQLAlchemy 1.4
    print(f"{postgresql.info.host}:{postgresql.info.port}/{postgresql.info.dbname}")
    engine = create_engine(
        f"postgresql+psycopg2://postgres@{postgresql.info.host}:{postgresql.info.port}/{postgresql.info.dbname}",
        echo=False,
        pool_pre_ping=True,
        execution_options={
            "schema_translate_map": {None: "public"}  # 👈 add this
        },
        connect_args={
            "options": "-csearch_path=public"  # Ensure we're using public schema
        }
    )
    
    # Create the public schema if it doesn't exist
    with engine.connect() as conn:
        with conn.begin():
            conn.execute(text("CREATE SCHEMA IF NOT EXISTS public"))
            for value in [
                "citext", "ltree", "intarray", "hstore", "btree_gist", "pg_trgm"
            ]:
                conn.execute(text(f"CREATE EXTENSION IF NOT EXISTS {value} WITH SCHEMA pg_catalog;;"))
    
    return engine


@pytest.fixture
def test_session(test_engine):
    """Create a test database session with schema support."""

    # If schema_translate_map should be applied, create a new engine with it
    engine_with_schema = test_engine.execution_options(
        schema_translate_map={None: 'public'}
    )

    # Create all tables with proper schema handling
    Base.metadata.create_all(engine_with_schema)
    
    # Create session with schema translation if needed
    Session = sessionmaker(bind=test_engine)
    session = Session()
    
    yield session
    session.close()

@pytest.fixture(scope="function", autouse=True)
def create_all_tables(test_engine):
    """Create all tables once for dependency resolution."""


    # Load citext extension if needed
    with test_engine.connect() as conn:
        with conn.begin():
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS citext;"))
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS ltree;"))

    Base.metadata.create_all(test_engine)
    yield
    Base.metadata.drop_all(test_engine)

@pytest.mark.skipif(not POSTGRESQL_AVAILABLE, reason="pytest-postgresql not available")
@allure.epic("Database Models")
@allure.feature("PostgreSQL Table Creation and CRUD")
class TestDbModels:

    @allure.story("Table creation for all models")
    @pytest.mark.parametrize("model_cls", MODELS, ids=lambda m: m.__name__)
    def test_table_creation_postgresql(self, test_engine, model_cls):
        """Test that the table for each model is created in PostgreSQL."""


        with allure.step("Verify table exists in database"):
            inspector = inspect(test_engine)
            
            # Check in public schema (default for most PostgreSQL setups)
            table_names = inspector.get_table_names(schema='public')
            table_name = model_cls.__table__.name
            
            assert table_name in table_names, f"Table {table_name} not found in public schema"
    
    @allure.story("Model instantiation")
    @pytest.mark.parametrize("model_cls", MODELS, ids=lambda m: m.__name__)
    def test_model_instantiation(self, model_cls):
        """Test that each model can be instantiated."""
        with allure.step(f"Instantiate {model_cls.__name__}"):
            try:
                # Try to create an instance with minimal required fields
                instance = model_cls()
                assert instance is not None
                assert isinstance(instance, model_cls)
            except Exception as e:
                # Some models might require specific parameters
                pytest.skip(f"Model {model_cls.__name__} requires specific parameters: {e}")
    
    @allure.story("Basic CRUD operations")
    def test_issue_crud_operations(self, test_session, issue_factory):
        """Test basic CRUD operations on Issue model."""
        from dags.data_pipeline.dbmodels.issue import Issue
        
        with allure.step("Create Issue record"):
            issue = issue_factory.create()
        
        with allure.step("Read Issue record"):
            retrieved_issue = test_session.query(Issue).filter_by(key=issue.key).first()
            assert retrieved_issue is not None
            assert retrieved_issue.key == issue.key
            assert retrieved_issue.summary == issue.summary
        
        with allure.step("Update Issue record"):
            retrieved_issue.summary = "Implemented test using faker"
            test_session.commit()
            
            updated_issue = test_session.query(Issue).filter_by(key=issue.key).first()
            assert updated_issue.summary == "Implemented test using faker"
        
        with allure.step("Delete Issue record"):
            test_session.delete(retrieved_issue)
            test_session.commit()
            
            deleted_issue = test_session.query(Issue).filter_by(key=issue.key).first()
            assert deleted_issue is None
    
    @allure.story("Basic CRUD operations")
    def test_user_crud_operations(self, test_session, user_factory):
        """Test basic CRUD operations on User model."""
        from dags.data_pipeline.dbmodels.user import User
        
        with allure.step("Create User record"):
            user = user_factory.create()
        
        with allure.step("Read User record"):
            result = test_session.get(User, user.accountId)
            assert result is not None
            assert result.emailAddress == user.emailAddress
        
        with allure.step("Update User record"):
            result.displayName = "Tina Ambani"
            test_session.commit()
            
            updated_user = test_session.query(User).filter_by(accountId=user.accountId).first()
            assert updated_user.displayName == "Tina Ambani"
        
        with allure.step("Delete User record"):
            test_session.delete(updated_user)
            test_session.commit()
            
            deleted_user = test_session.query(User).filter_by(accountId=user.accountId).first()
            assert deleted_user is None

    @allure.story("Constraint Violations")
    def test_duplicate_email_fails(self, user_factory, test_session):
        user1 = user_factory.create(emailAddress="<EMAIL>")

        with pytest.raises(IntegrityError):
            user2 = user_factory.create(emailAddress="<EMAIL>")
            test_session.flush()  # commit or flush triggers the constraint

    @allure.story("Relationship testing")
    def test_issue_user_relationship(self, test_session, user_factory, issue_factory):
        """Test relationships between Issue and User models."""
        from dags.data_pipeline.dbmodels.issue import Issue

        with allure.step("Create User and Issue with relationship"):
            user = user_factory.create()
            issue = issue_factory.create(assignee=None, reporter=user.accountId)

        with allure.step("Verify relationship"):
            retrieved_issue = test_session.query(Issue).filter_by(key=issue.key).first()
            assert retrieved_issue is not None
            assert retrieved_issue.assignee is None
            assert retrieved_issue.reporter == user.accountId
    
    @allure.story("Constraint testing")
    def test_unique_constraints(self, test_session, issue_factory):
        """Test unique constraints on models."""
        from dags.data_pipeline.dbmodels.issue import Issue
        print(f"issue factory: {issue_factory._meta.sqlalchemy_session}")
        
        with allure.step("Create first Issue"):
            issue1 = issue_factory.create()
        
        with allure.step("Attempt to create duplicate Issue"):
            
            with pytest.raises(IntegrityError):
                issue_factory.create(key=issue1.key)
                test_session.commit()
    
    @allure.story("Schema validation")  
    def test_table_schemas(self, test_engine):
        """Test that tables have expected schemas in PostgreSQL."""
        with allure.step("Create all tables"):
            Base.metadata.create_all(test_engine)
        
        with allure.step("Verify table schemas"):
            inspector = inspect(test_engine)
            
            for model_cls in MODELS:
                table_name = model_cls.__table__.name
                
                # Check columns in public schema
                columns = inspector.get_columns(table_name, schema='public')
                
                # Verify table has columns
                assert len(columns) > 0, f"Table {table_name} has no columns"
                
                # Verify primary key exists
                pk_constraint = inspector.get_pk_constraint(table_name, schema='public')
                assert pk_constraint['constrained_columns'], f"Table {table_name} has no primary key"
    
    @allure.story("Schema support")
    def test_schema_usage(self, test_engine):
        """Test that models properly use PostgreSQL schemas."""
        with allure.step("Verify models have schema configuration"):
            schema_models = []
            for model_cls in MODELS:
                if hasattr(model_cls.__table__, 'schema') and model_cls.__table__.schema:
                    schema_models.append(model_cls)
            
            # Should have at least some models with schema defined
            assert len(schema_models) > 0, "No models found with schema configuration"
            
            # Verify they use 'public' schema
            for model_cls in schema_models:
                assert model_cls.__table__.schema == 'public', f"Model {model_cls.__name__} uses unexpected schema"
    
    @allure.story("Model metadata")
    def test_model_metadata(self, test_engine):
        """Test that models have proper metadata."""
        dialect_schema = test_engine.dialect.default_schema_name
        execution_options = test_engine.get_execution_options()
        schema_map = execution_options["schema_translate_map"] if "schema_translate_map" in execution_options else {}

        for model_cls in MODELS:
            with allure.step(f"Verify metadata for {model_cls.__name__}"):
                # Check that model has table name
                assert hasattr(model_cls, '__tablename__')
                assert model_cls.__tablename__ is not None
                
                # Check that model has table
                assert hasattr(model_cls, '__table__')
                assert model_cls.__table__ is not None
                
                # Check that table has columns
                assert len(model_cls.__table__.columns) > 0

                # Determine effective schema
                table_args = getattr(model_cls, '__table_args__', {})
                explicit_schema = None

                if isinstance(table_args, dict):
                    explicit_schema = table_args.get('schema')

                effective_schema = (
                    schema_map.get(explicit_schema) if explicit_schema else
                    schema_map.get(dialect_schema, dialect_schema)
                )

                if effective_schema is not None:
                    effective_schema = schema_map.get(explicit_schema, explicit_schema)
                else:
                    effective_schema = schema_map.get(None, dialect_schema)

                assert effective_schema is not None, (
                    f"Model {model_cls.__name__} does not resolve to a known schema"
                )

                # # Check for __table_args__ with schema (PostgreSQL-specific)
                # if hasattr(model_cls, '__table_args__'):
                #     table_args = model_cls.__table_args__
                #     if isinstance(table_args, dict):
                #         # Should have schema defined
                #         assert 'schema' in table_args, f"Model {model_cls.__name__} missing schema in __table_args__"
                #         assert table_args['schema'] == 'public', f"Model {model_cls.__name__} uses unexpected schema"

    @allure.story("Full Issue lifecycle with Comments and Links")
    def test_issue_comment_link_crud(
            self, test_session,
            issue_factory, user_factory, changelog_factory, worklog_factory
    ):
        """Test creation of Issue, its related IssueComments and IssueLinks."""
        from dags.data_pipeline.dbmodels.issue import Issue, IssueComments, IssueLinks
        from dags.data_pipeline.dbmodels.changelog import ChangelogJSON
        from dags.data_pipeline.dbmodels.worklog import WorkLog

        with allure.step("Create users"):
            reporter = user_factory.create(accountType="atlassian")
            assignee = user_factory.create(accountType="app")

        with allure.step("Create primary issue"):
            issue = issue_factory.create(reporter=reporter.accountId, assignee=assignee.accountId)
            test_session.flush()

        with allure.step("Create comment on issue"):
            comment = IssueComments(
                id=1,
                author=reporter.accountId,
                updateAuthor=reporter.accountId,
                issue_id=issue.id,
                issue_key=issue.key,
                body={"content": "Initial comment"},
                renderedBody="Initial comment (rendered)",
                jsdPublic=True
            )
            test_session.add(comment)
            test_session.flush()

        with allure.step("Create issue link"):
            link = IssueLinks(
                id=1,
                type={"name": "blocks"},
                issue_id=issue.id,
                issue_key=issue.key,
                outwardIssue_id=None,
                outwardIssue_key=None,
                inwardIssue_id=None,
                inwardIssue_key=None
            )
            test_session.add(link)
            test_session.flush()

        with allure.step("Create ChangelogJSON"):
            change_log = changelog_factory.create(
                author=reporter.accountId,
                issue_id=issue.id,
                issue_key=issue.key
            )
            test_session.add(change_log)
            test_session.flush()

        with allure.step("Create WorkLog"):
            worklog = worklog_factory.create(
                author=reporter.accountId, issue_id=issue.id, issue_key=issue.key
            )
            test_session.add(worklog)
            test_session.flush()

        with allure.step("Validate linked records"):
            retrieved_issue = test_session.get(Issue, issue.id)
            assert retrieved_issue is not None
            assert retrieved_issue.key == issue.key

            comment_result = test_session.query(IssueComments).filter_by(issue_id=issue.id).first()
            assert comment_result is not None
            assert comment_result.author == reporter.accountId

            link_result = test_session.query(IssueLinks).filter_by(issue_id=issue.id).first()
            assert link_result is not None
            assert link_result.type["name"] == "blocks"

        with allure.step("Clean up"):
            test_session.delete(link)
            test_session.delete(comment)
            test_session.delete(change_log)
            test_session.delete(worklog)
            test_session.delete(issue)
            test_session.commit()

            assert test_session.get(Issue, issue.id) is None
