#!/usr/bin/env python3
"""
Test script to verify the file descriptor leak fix in dataframe_utils.

This script simulates the high-frequency usage pattern that was causing
the "Too many open files" error and verifies that the fix works correctly.
"""

import os
import sys
import pandas as pd
import tempfile
import time
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from dags.data_pipeline.dataframe_utils.dataframe_debugger import (
        quick_save_dataframe_minimal,
        quick_save_dataframe,
        cleanup_logger_cache
    )
    print("✅ Successfully imported dataframe utilities")
except ImportError as e:
    print(f"❌ Failed to import dataframe utilities: {e}")
    sys.exit(1)


def get_open_file_count():
    """Get the current number of open file descriptors for this process."""
    try:
        if os.name == 'nt':  # Windows
            # On Windows, we can't easily count file descriptors
            # So we'll use a different approach or skip this check
            return None
        else:  # Unix-like systems
            import resource
            return resource.getrlimit(resource.RLIMIT_NOFILE)[0]
    except:
        return None


def test_minimal_save_function():
    """Test the minimal save function that doesn't use logging."""
    print("\n🧪 Testing quick_save_dataframe_minimal...")
    
    # Create test DataFrame
    test_df = pd.DataFrame({
        'id': range(1, 101),
        'name': [f'Item_{i}' for i in range(1, 101)],
        'value': [i * 1.5 for i in range(1, 101)]
    })
    
    with tempfile.TemporaryDirectory() as temp_dir:
        success_count = 0
        total_tests = 50
        
        print(f"Running {total_tests} save operations...")
        start_time = time.time()
        
        for i in range(total_tests):
            filename = f"test_minimal_{i}.xlsx"
            success = quick_save_dataframe_minimal(
                test_df, 
                filename=filename, 
                path=temp_dir
            )
            if success:
                success_count += 1
                
            # Check if file was created
            file_path = Path(temp_dir) / filename
            if not file_path.exists():
                print(f"❌ File {filename} was not created")
        
        end_time = time.time()
        
        print(f"✅ Completed {success_count}/{total_tests} saves successfully")
        print(f"⏱️  Time taken: {end_time - start_time:.2f} seconds")
        
        # Verify files exist
        created_files = list(Path(temp_dir).glob("test_minimal_*.xlsx"))
        print(f"📁 Created {len(created_files)} files")
        
        return success_count == total_tests


def test_regular_save_function():
    """Test the regular save function with improved logging."""
    print("\n🧪 Testing quick_save_dataframe with improved logging...")
    
    # Create test DataFrame
    test_df = pd.DataFrame({
        'id': range(1, 51),
        'data': [f'Data_{i}' for i in range(1, 51)]
    })
    
    with tempfile.TemporaryDirectory() as temp_dir:
        success_count = 0
        total_tests = 20  # Fewer tests since this uses logging
        
        print(f"Running {total_tests} save operations...")
        start_time = time.time()
        
        for i in range(total_tests):
            filename = f"test_regular_{i}.xlsx"
            success = quick_save_dataframe(
                test_df, 
                filename=filename, 
                path=temp_dir
            )
            if success:
                success_count += 1
        
        end_time = time.time()
        
        print(f"✅ Completed {success_count}/{total_tests} saves successfully")
        print(f"⏱️  Time taken: {end_time - start_time:.2f} seconds")
        
        # Clean up logger cache
        cleanup_logger_cache()
        print("🧹 Cleaned up logger cache")
        
        return success_count == total_tests


def test_high_frequency_usage():
    """Test high-frequency usage pattern similar to the original issue."""
    print("\n🧪 Testing high-frequency usage pattern...")
    
    # Create test DataFrames of different sizes
    small_df = pd.DataFrame({'x': range(10), 'y': range(10, 20)})
    medium_df = pd.DataFrame({'a': range(100), 'b': range(100, 200)})
    
    with tempfile.TemporaryDirectory() as temp_dir:
        success_count = 0
        total_operations = 100
        
        print(f"Running {total_operations} high-frequency operations...")
        start_time = time.time()
        
        for i in range(total_operations):
            # Alternate between different DataFrame sizes and formats
            if i % 3 == 0:
                df = small_df
                format_type = "xlsx"
            elif i % 3 == 1:
                df = medium_df
                format_type = "csv"
            else:
                df = small_df
                format_type = "xlsx"
            
            filename = f"high_freq_{i}.{format_type}"
            success = quick_save_dataframe_minimal(
                df, 
                filename=filename, 
                path=temp_dir,
                file_format=format_type
            )
            if success:
                success_count += 1
        
        end_time = time.time()
        
        print(f"✅ Completed {success_count}/{total_operations} operations successfully")
        print(f"⏱️  Time taken: {end_time - start_time:.2f} seconds")
        print(f"📊 Average time per operation: {(end_time - start_time) / total_operations * 1000:.2f}ms")
        
        return success_count >= total_operations * 0.95  # Allow 5% failure rate


def main():
    """Run all tests."""
    print("🚀 Starting file descriptor leak fix tests...")
    print(f"🖥️  Operating System: {os.name}")
    
    # Get initial file descriptor count
    initial_fd_count = get_open_file_count()
    if initial_fd_count:
        print(f"📊 Initial file descriptor limit: {initial_fd_count}")
    
    # Run tests
    tests = [
        ("Minimal Save Function", test_minimal_save_function),
        ("Regular Save Function", test_regular_save_function),
        ("High-Frequency Usage", test_high_frequency_usage),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    # Final cleanup
    cleanup_logger_cache()
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 TEST SUMMARY")
    print('='*60)
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! File descriptor leak fix is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
