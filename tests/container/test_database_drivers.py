#!/usr/bin/env python3
"""
Test script to verify database driver configuration.

This script tests that the correct database drivers (psycopg2 and asyncpg) 
are available and working correctly without importing the full containers module.
"""

import pytest
import allure
from unittest.mock import patch, MagicMock


@allure.feature("Database Drivers")
@allure.story("Driver Compatibility")
class TestDatabaseDrivers:
    """Test suite for database driver compatibility."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test psycopg2 driver availability")
    def test_psycopg2_available(self):
        """Test that psycopg2 driver is available and importable."""
        try:
            import psycopg2
            assert psycopg2 is not None
            print(f"✅ psycopg2 version: {psycopg2.__version__}")
        except ImportError as e:
            pytest.fail(f"psycopg2 not available: {e}")
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test asyncpg driver availability")
    def test_asyncpg_available(self):
        """Test that asyncpg driver is available and importable."""
        try:
            import asyncpg
            assert asyncpg is not None
            print(f"✅ asyncpg version: {asyncpg.__version__}")
        except ImportError as e:
            pytest.fail(f"asyncpg not available: {e}")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test psycopg3 is not installed")
    def test_psycopg3_not_installed(self):
        """Test that psycopg3 (psycopg) is not installed to avoid conflicts."""
        try:
            import psycopg
            pytest.skip("psycopg (psycopg3) is installed and may cause conflicts with psycopg2")
        except ImportError:
            # This is expected - psycopg3 should not be installed
            print("✅ psycopg3 is not installed (good)")
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test SQLAlchemy URL creation with psycopg2")
    def test_sqlalchemy_url_psycopg2(self):
        """Test SQLAlchemy URL creation with psycopg2 driver."""
        from sqlalchemy.engine import URL
        
        url = URL.create(
            drivername="postgresql+psycopg2",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        assert str(url).startswith("postgresql+psycopg2://")
        assert "test_user" in str(url)
        assert "localhost" in str(url)
        print(f"✅ psycopg2 URL: {url}")
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test SQLAlchemy URL creation with asyncpg")
    def test_sqlalchemy_url_asyncpg(self):
        """Test SQLAlchemy URL creation with asyncpg driver."""
        from sqlalchemy.engine import URL
        
        url = URL.create(
            drivername="postgresql+asyncpg",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        assert str(url).startswith("postgresql+asyncpg://")
        assert "test_user" in str(url)
        assert "localhost" in str(url)
        print(f"✅ asyncpg URL: {url}")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test SQLAlchemy engine creation with mocked psycopg2")
    @patch('sqlalchemy.create_engine')
    def test_sqlalchemy_engine_creation_psycopg2(self, mock_create_engine):
        """Test SQLAlchemy engine creation with psycopg2 (mocked)."""
        from sqlalchemy.engine import URL
        from sqlalchemy import create_engine
        
        # Mock the engine
        mock_engine = MagicMock()
        mock_engine.execution_options.return_value = mock_engine
        mock_create_engine.return_value = mock_engine
        
        url = URL.create(
            drivername="postgresql+psycopg2",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        engine = create_engine(url)
        engine_with_options = engine.execution_options(schema_translate_map={None: "test_schema"})
        
        # Verify engine creation was called
        mock_create_engine.assert_called_once_with(url)
        mock_engine.execution_options.assert_called_once_with(schema_translate_map={None: "test_schema"})
        
        print("✅ psycopg2 engine creation successful (mocked)")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test SQLAlchemy async engine creation with mocked asyncpg")
    @patch('sqlalchemy.ext.asyncio.create_async_engine')
    def test_sqlalchemy_async_engine_creation_asyncpg(self, mock_create_async_engine):
        """Test SQLAlchemy async engine creation with asyncpg (mocked)."""
        from sqlalchemy.engine import URL
        from sqlalchemy.ext.asyncio import create_async_engine
        
        # Mock the async engine
        mock_async_engine = MagicMock()
        mock_async_engine.execution_options.return_value = mock_async_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        url = URL.create(
            drivername="postgresql+asyncpg",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        engine = create_async_engine(url)
        engine_with_options = engine.execution_options(schema_translate_map={None: "test_schema"})
        
        # Verify async engine creation was called
        mock_create_async_engine.assert_called_once_with(url)
        mock_async_engine.execution_options.assert_called_once_with(schema_translate_map={None: "test_schema"})
        
        print("✅ asyncpg async engine creation successful (mocked)")


def test_driver_versions():
    """Standalone function to test and print driver versions."""
    print("\n" + "="*60)
    print("DATABASE DRIVER COMPATIBILITY CHECK")
    print("="*60)
    
    # Test psycopg2
    try:
        import psycopg2
        print(f"✅ psycopg2: {psycopg2.__version__}")
    except ImportError as e:
        print(f"❌ psycopg2: {e}")
    
    # Test asyncpg
    try:
        import asyncpg
        print(f"✅ asyncpg: {asyncpg.__version__}")
    except ImportError as e:
        print(f"❌ asyncpg: {e}")
    
    # Test psycopg3 (should not be installed)
    try:
        import psycopg
        print(f"⚠️  psycopg3: {psycopg.__version__} (may cause conflicts)")
    except ImportError:
        print("✅ psycopg3: Not installed (good)")
    
    # Test SQLAlchemy
    try:
        import sqlalchemy
        print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError as e:
        print(f"❌ SQLAlchemy: {e}")
    
    print("="*60)


if __name__ == "__main__":
    test_driver_versions()
    
    # Run the tests
    pytest.main([
        __file__,
        "-v",
        "--tb=short"
    ])
