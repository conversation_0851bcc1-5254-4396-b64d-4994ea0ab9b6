#!/usr/bin/env python3
"""
Setup script for uv environment with legacy database drivers.

This script sets up the test environment using uv with the legacy
dependency group to ensure psycopg2 is used instead of psycopg3.
"""
import shlex
import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'=' * 60}")
    print(f"Running: {description or ' '.join(cmd)}")
    print(f"{'=' * 60}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description or 'Command'} completed successfully")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed with exit code {e.returncode}")
        return e
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        print("Make sure uv is installed: pip install uv")
        return None


def check_uv_available():
    """Check if uv is available."""
    try:
        result = subprocess.run(["uv", "--version"], check=True, capture_output=True, text=True)
        print(f"✅ uv is available: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ uv is not available")
        print("Install uv with: pip install uv")
        return False


def find_project_root():
    """Find the project root directory containing pyproject.toml."""
    # Start from the current script location
    current_dir = Path(__file__).parent

    # Look for pyproject.toml in current directory and parent directories
    for path in [current_dir] + list(current_dir.parents):
        pyproject_file = path / "pyproject.toml"
        if pyproject_file.exists():
            return path, pyproject_file

    return None, None


def setup_uv_environment():
    """Set up uv environment with legacy drivers."""
    print("🚀 Setting up uv environment with legacy database drivers")

    # Find the project root
    project_root, pyproject_file = find_project_root()

    if project_root is None:
        print("❌ pyproject.toml not found in current directory or any parent directory")
        print("Make sure you're running this script from within the project")
        return False

    print(f"✅ Found project root: {project_root}")
    print(f"✅ Found pyproject.toml: {pyproject_file}")

    # Change to project root directory
    original_cwd = os.getcwd()
    os.chdir(project_root)
    print(f"Working directory changed to: {project_root}")

    try:
        # Sync dependencies with legacy drivers
        print("\n📦 Installing dependencies with legacy drivers...")
        result = run_command([
            "uv", "sync", "--group", "test", "--group", "legacy"
        ], "Installing test dependencies with legacy drivers")

        if result and result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies")
            return False


    except Exception as e:
        print(e)
    finally:
        # Restore original working directory
        os.chdir(original_cwd)


def verify_installation():
    """Verify that the installation is correct."""
    print("\n🔍 Verifying installation...")

    # Find project root again for verification
    project_root, _ = find_project_root()
    if not project_root:
        print("❌ Cannot find project root for verification")
        return False

    # Change to project root for running uv commands
    original_cwd = os.getcwd()
    os.chdir(project_root)

    try:
        # Check that psycopg2 is installed and psycopg3 is not
        checks = [
            ("uv run python -c \"import psycopg2; print(f'psycopg2: {psycopg2.__version__}')\"", "psycopg2 check"),
            ("uv run python -c \"import asyncpg; print(f'asyncpg: {asyncpg.__version__}')\"", "asyncpg check"),
            ("uv run python -c \"import sqlalchemy; print(f'SQLAlchemy: {sqlalchemy.__version__}')\"",
             "SQLAlchemy check"),
            ("uv run python -c \"import pytest; print(f'pytest: {pytest.__version__}')\"", "pytest check"),
        ]

        all_good = True
        for cmd_str, description in checks:
            # cmd = cmd_str.split()
            cmd = shlex.split(cmd_str)
            result = run_command(cmd, description)
            if not result or result.returncode != 0:
                all_good = False

        # Check that psycopg3 is NOT installed
        print("\n🔍 Checking that psycopg3 is not installed...")
        result = subprocess.run([
            "uv", "run", "python", "-c", "import psycopg; print('psycopg3 is installed')"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("⚠️  psycopg3 is installed - this may cause conflicts")
            print("Consider removing it from dependencies")
            all_good = False
        else:
            print("✅ psycopg3 is not installed (good)")

        return all_good

    finally:
        # Restore original working directory
        os.chdir(original_cwd)


def run_basic_test():
    """Run the basic setup test to verify everything works."""
    print("\n🧪 Running basic setup test...")

    # Find project root
    project_root, _ = find_project_root()
    if not project_root:
        print("❌ Cannot find project root for running tests")
        return False

    # Change to project root
    original_cwd = os.getcwd()
    os.chdir(project_root)

    try:
        # Look for test file in the tests/container directory
        test_file = Path("tests/container/test_basic_setup.py")
        if not test_file.exists():
            print(f"⚠️  Test file not found: {test_file}")
            print("Skipping basic test - create test_basic_setup.py if needed")
            return True  # Don't fail if test file doesn't exist

        result = run_command([
            "uv", "run", "pytest", str(test_file), "-v"
        ], "Running basic setup test")

        return result and result.returncode == 0

    finally:
        # Restore original working directory
        os.chdir(original_cwd)


def show_usage_instructions():
    """Show instructions for using the environment."""
    project_root, _ = find_project_root()

    print("\n" + "=" * 70)
    print("✅ Environment setup completed successfully!")
    print("=" * 70)

    print(f"\n📁 Project root: {project_root}")
    print("\n🚀 You can now run tests with:")
    print(f"   cd {project_root}")
    print("   uv run pytest tests/container/test_basic_setup.py")
    print("   uv run python tests/container/run_tests.py --coverage --allure")

    print("\n📦 Installed dependency groups:")
    print("   • test: All testing dependencies")
    print("   • legacy: psycopg2-binary (PostgreSQL driver)")

    print("\n💡 Useful commands:")
    print("   uv run pytest tests/ -v              # Run all tests")
    print("   uv run pytest tests/ -m unit         # Run only unit tests")
    print("   uv run pytest tests/ -m integration  # Run only integration tests")
    print("   uv tree --group test --group legacy  # Show dependency tree")


def main():
    """Main function."""
    print("🔧 UV ENVIRONMENT SETUP FOR LEGACY DATABASE DRIVERS")
    print("=" * 70)

    # Show current working directory and script location
    print(f"📁 Current working directory: {os.getcwd()}")
    print(f"📄 Script location: {Path(__file__).parent}")

    # Check if uv is available
    if not check_uv_available():
        return 1

    # Setup environment
    if not setup_uv_environment():
        return 1

    # Verify installation
    if not verify_installation():
        print("\n⚠️  Installation verification failed")
        print("Some dependencies may not be correctly installed")
        # Don't return failure here, continue to show instructions

    # Run basic test
    test_result = run_basic_test()

    # Show usage instructions
    show_usage_instructions()

    if not test_result:
        print("\n⚠️  Basic test failed, but environment setup is complete")
        print("Check the error messages above for any issues")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())