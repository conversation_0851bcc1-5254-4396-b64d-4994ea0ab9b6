#!/usr/bin/env python3
"""
Basic setup test to verify environment before running full test suite.

This test verifies that the basic dependencies and database drivers are
working correctly before attempting to run the full container test suite.
"""

import pytest
import allure
from unittest.mock import MagicMock, patch


@allure.feature("Basic Setup")
@allure.story("Environment Verification")
class TestBasicSetup:
    """Basic setup verification tests."""
    
    @allure.severity(allure.severity_level.BLOCKER)
    @allure.title("Test basic imports")
    def test_basic_imports(self):
        """Test that basic required modules can be imported."""
        # Test SQLAlchemy imports
        from sqlalchemy import create_engine
        from sqlalchemy.engine import URL
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy.orm import Session, sessionmaker
        
        # Test async imports
        import asyncio
        from contextlib import asynccontextmanager, contextmanager
        
        # Test dependency injection
        from dependency_injector import containers, providers
        
        print("✅ All basic imports successful")
    
    @allure.severity(allure.severity_level.BLOCKER)
    @allure.title("Test database driver imports")
    def test_database_driver_imports(self):
        """Test that database drivers can be imported."""
        # Test psycopg2
        import psycopg2
        assert psycopg2.__version__ is not None
        print(f"✅ psycopg2: {psycopg2.__version__}")
        
        # Test asyncpg
        import asyncpg
        assert asyncpg.__version__ is not None
        print(f"✅ asyncpg: {asyncpg.__version__}")
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test SQLAlchemy URL creation")
    def test_sqlalchemy_url_creation(self):
        """Test SQLAlchemy URL creation with correct drivers."""
        from sqlalchemy.engine import URL
        
        # Test psycopg2 URL
        sync_url = URL.create(
            drivername="postgresql+psycopg2",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        assert "postgresql+psycopg2" in str(sync_url)
        print(f"✅ Sync URL: {sync_url}")
        
        # Test asyncpg URL
        async_url = URL.create(
            drivername="postgresql+asyncpg",
            username="test_user",
            password="test_pass",
            host="localhost",
            port=5432,
            database="test_db"
        )
        
        assert "postgresql+asyncpg" in str(async_url)
        print(f"✅ Async URL: {async_url}")
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test mocked engine creation")
    @patch('sqlalchemy.create_engine')
    @patch('sqlalchemy.ext.asyncio.create_async_engine')
    def test_mocked_engine_creation(self, mock_async_engine, mock_sync_engine):
        """Test engine creation with mocked SQLAlchemy."""
        from sqlalchemy import create_engine
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy.engine import URL
        
        # Setup mocks
        mock_sync_engine_instance = MagicMock()
        mock_sync_engine_instance.execution_options.return_value = mock_sync_engine_instance
        mock_sync_engine.return_value = mock_sync_engine_instance
        
        mock_async_engine_instance = MagicMock()
        mock_async_engine_instance.execution_options.return_value = mock_async_engine_instance
        mock_async_engine.return_value = mock_async_engine_instance
        
        # Test sync engine creation
        sync_url = URL.create(
            drivername="postgresql+psycopg2",
            username="test", password="test",
            host="localhost", database="test"
        )
        
        sync_engine = create_engine(sync_url)
        sync_engine_with_options = sync_engine.execution_options(
            schema_translate_map={None: "test_schema"}
        )
        
        mock_sync_engine.assert_called_once_with(sync_url)
        mock_sync_engine_instance.execution_options.assert_called_once()
        
        # Test async engine creation
        async_url = URL.create(
            drivername="postgresql+asyncpg",
            username="test", password="test",
            host="localhost", database="test"
        )
        
        async_engine = create_async_engine(async_url)
        async_engine_with_options = async_engine.execution_options(
            schema_translate_map={None: "test_schema"}
        )
        
        mock_async_engine.assert_called_once_with(async_url)
        mock_async_engine_instance.execution_options.assert_called_once()
        
        print("✅ Mocked engine creation successful")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test dataclass creation")
    def test_dataclass_creation(self):
        """Test that dataclasses work correctly."""
        from dataclasses import dataclass, field
        from typing import Dict, Any, Optional
        
        @dataclass
        class TestEntryDetails:
            username: str
            password: str
            url: Optional[str] = None
            custom_properties: Dict[str, Any] = field(default_factory=dict)
        
        entry = TestEntryDetails(
            username="test_user",
            password="test_pass",
            url="test://url",
            custom_properties={"key": "value"}
        )
        
        assert entry.username == "test_user"
        assert entry.password == "test_pass"
        assert entry.url == "test://url"
        assert entry.custom_properties["key"] == "value"
        
        print("✅ Dataclass creation successful")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test dependency injection basics")
    def test_dependency_injection_basics(self):
        """Test basic dependency injection functionality."""
        from dependency_injector import containers, providers
        
        class TestContainer(containers.DeclarativeContainer):
            test_value = providers.Object("test_string")
            test_factory = providers.Factory(str, "factory_string")
        
        container = TestContainer()
        
        # Test object provider
        assert container.test_value() == "test_string"
        
        # Test factory provider
        assert container.test_factory() == "factory_string"
        
        # Test override
        container.test_value.override("overridden_string")
        assert container.test_value() == "overridden_string"
        
        print("✅ Dependency injection basics successful")
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test async functionality")
    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """Test basic async functionality."""
        import asyncio
        from contextlib import asynccontextmanager
        
        @asynccontextmanager
        async def test_async_context():
            print("Entering async context")
            try:
                yield "async_value"
            finally:
                print("Exiting async context")
        
        async with test_async_context() as value:
            assert value == "async_value"
        
        # Test asyncio.gather
        async def test_coroutine(value):
            await asyncio.sleep(0.001)  # Minimal sleep
            return f"processed_{value}"
        
        results = await asyncio.gather(
            test_coroutine("a"),
            test_coroutine("b"),
            test_coroutine("c")
        )
        
        assert results == ["processed_a", "processed_b", "processed_c"]
        
        print("✅ Async functionality successful")


def run_basic_checks():
    """Run basic environment checks without pytest."""
    print("🔧 BASIC ENVIRONMENT CHECK")
    print("=" * 50)
    
    checks = [
        ("SQLAlchemy imports", lambda: __import__('sqlalchemy')),
        ("psycopg2 import", lambda: __import__('psycopg2')),
        ("asyncpg import", lambda: __import__('asyncpg')),
        ("dependency_injector import", lambda: __import__('dependency_injector')),
        ("pytest import", lambda: __import__('pytest')),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            check_func()
            print(f"✅ {check_name}")
        except Exception as e:
            print(f"❌ {check_name}: {e}")
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("✅ Basic environment check passed!")
        print("You can proceed with running the full test suite.")
    else:
        print("❌ Basic environment check failed!")
        print("Please fix the issues above before running tests.")
    
    return all_passed


if __name__ == "__main__":
    # Run basic checks first
    if run_basic_checks():
        print("\n🧪 Running pytest...")
        # Run the tests
        pytest.main([
            __file__,
            "-v",
            "--tb=short",
            "--no-header"
        ])
    else:
        print("\n❌ Skipping pytest due to environment issues.")
        exit(1)
