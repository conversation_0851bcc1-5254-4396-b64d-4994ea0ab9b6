[tool:pytest]
# Pytest configuration for container tests

# Test discovery
testpaths = .
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    database: Tests requiring database connection
    async: Asynchronous tests

# Coverage settings
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=dags.data_pipeline.containers
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --alluredir=allure-results

# Async test configuration
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# Minimum version
minversion = 6.0
