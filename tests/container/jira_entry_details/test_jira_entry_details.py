# coding=utf-8
import pytest
import allure
from unittest.mock import <PERSON>Mock

from pykeepass import <PERSON>yKeePass

from dags.data_pipeline.containers import JiraEntryDetailsContainer, EntryDetails


@pytest.fixture(scope="module")
def container():
    """Fixture to provide the JiraEntryDetailsContainer instance"""
    container = JiraEntryDetailsContainer()
    # Mock the KeePass manager
    mock_keepass_manager = MagicMock(spec=PyKeePass)

    # Simulate the return value of find_entries for the JIRA_ENTRY title
    mock_entry = MagicMock()
    mock_entry.username = "<EMAIL>"
    mock_entry.url = "https://corecard.atlassian.net/"
    mock_entry.custom_properties = {}
    mock_keepass_manager.find_entries.return_value = mock_entry
    # Override the keepass provider in the container with the mock
    container.keepass.override(mock_keepass_manager)
    yield container


@allure.feature('JiraEntryDetailsContainer')
@allure.story('Test Jira entry details resolution')
class TestJiraEntryDetailsContainer:

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test Jira entry details resolution for JIRA_ENTRY')
    def test_jira_entry_details(self, container):
        """Test that the `jira_entry_details` resolves correctly with the expected username and URL."""

        # Mock the keepass provider
        mock_keepass = MagicMock(spec=PyKeePass)
        container.keepass = mock_keepass  # Ensure the MagicMock is assigned correctly

        # Simulate the return value of find_entries for the JIRA_ENTRY title
        mock_entry = MagicMock()
        mock_entry.username = "<EMAIL>"
        mock_entry.url = "https://corecard.atlassian.net/"
        mock_entry.custom_properties = {}  # Adjust if needed
        mock_keepass.find_entries.return_value = mock_entry

        # Resolve Jira entry details using the factory
        jira_entry = container.jira_entry_details()  # Corrected to invoke the callable

        # Check that the entry details match expected values
        assert jira_entry.username == "<EMAIL>", "Incorrect username"
        assert jira_entry.url == "https://corecard.atlassian.net/", "Incorrect URL"
        assert isinstance(jira_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("Jira entry details resolved correctly")

    @allure.severity(allure.severity_level.MINOR)
    @allure.title('Test JiraEntryDetailsContainer Singleton behavior')
    def test_jira_entry_details_singleton(self, container):
        """Test that the `jira_entry_details` is resolved as a singleton."""

        # Mock the keepass provider
        mock_keepass = MagicMock(spec=PyKeePass)
        container.keepass = mock_keepass  # Ensure the MagicMock is assigned correctly

        # Simulate the return value of find_entries for the JIRA_ENTRY title
        mock_entry = MagicMock()
        mock_entry.username = "<EMAIL>"
        mock_entry.url = "https://corecard.atlassian.net/"
        mock_entry.custom_properties = {}  # Adjust if needed
        mock_keepass.find_entries.return_value = mock_entry

        # Resolve Jira entry details twice to check singleton behavior
        jira_entry_1 = container.jira_entry_details()  # Corrected to invoke the callable
        jira_entry_2 = container.jira_entry_details()  # Corrected to invoke the callable

        # Assert that both instances are the same (singleton behavior)
        assert jira_entry_1 is jira_entry_2, "JiraEntryDetails should be the same instance"

        # Log the result for Allure report
        allure.step("Jira entry details singleton behavior verified")