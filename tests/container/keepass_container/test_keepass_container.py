# coding=utf-8
import allure
import pytest
from pykeepass import PyKeePass

from dags.data_pipeline.containers import KeePassContainer, EntryDetails


@pytest.fixture(scope="module")
def container():
    """Fixture to provide an ApplicationContainer for testing."""
    container = KeePassContainer()
    container.wire(modules=["__main__"])
    yield container

    # Clean up wiring after test
    container.unwire()


@allure.feature('KeePassContainer')
@allure.story('Test KeePass manager and entry details resolution')
class TestKeePassContainer:

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test KeePassManager instantiation')
    def test_keepass_manager(self, container):
        """Test the instantiation of the KeePass manager with the correct parameters."""

        # Resolve KeePass manager
        keepass_manager = container.keepass_manager()

        # Ensure the keepass manager is an instance of PyKeePass
        assert isinstance(keepass_manager, PyKeePass), "Expected instance of PyKeePass"

        # Validate that the filename and keyfile are set correctly
        assert keepass_manager.filename == container.config.KeePassDir.DB_NAME(), "Incorrect KeePass DB name"
        assert keepass_manager.keyfile == container.config.KeePassDir.KEY_FILE(), "Incorrect KeePass key file"

        # Log the result for Allure report
        allure.step("KeePassManager was instantiated successfully with the correct DB and keyfile")

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test get_kp_entry_details resolution for JIRA_ENTRY')
    def test_jira_entry_details(self, container):
        """Test that the `jira_entry_details` resolves correctly with the appropriate title."""

        # Resolve Jira entry details
        jira_entry = container.jira_entry_details()

        # Check if the entry title is correct
        assert jira_entry.username == '<EMAIL>', "Incorrect Jira entry title"
        assert jira_entry.password, "Password should not be empty"
        assert isinstance(jira_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("Jira entry details resolved correctly")

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test get_kp_entry_details resolution for PG_RW')
    def test_pg_rw_entry_details(self, container):
        """Test that the `pg_rw` resolves correctly with the appropriate title."""

        # Resolve PG_RW entry details
        pg_rw_entry = container.pg_rw()

        # Check if the entry title is correct
        assert pg_rw_entry.username == 'postgres', "Incorrect PG_RW entry title"
        assert pg_rw_entry.password, "Password should not be empty"
        assert isinstance(pg_rw_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("PG_RW entry details resolved correctly")

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test get_kp_entry_details resolution for PG_RO')
    def test_pg_ro_entry_details(self, container):
        """Test that the `pg_ro` resolves correctly with the appropriate title."""

        # Resolve PG_RO entry details
        pg_ro_entry = container.pg_ro()

        # Check if the entry title is correct
        assert pg_ro_entry.username == 'jira_ro', "Incorrect PG_RO entry title"
        assert pg_ro_entry.password, "Password should not be empty"
        assert isinstance(pg_ro_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("PG_RO entry details resolved correctly")

    @allure.severity(allure.severity_level.MINOR)
    @allure.title('Test schema_rw entry details')
    def test_schema_rw_entry_details(self, container):
        """Test that the schema_rw resolves correctly with the appropriate title."""

        # Resolve schema_rw entry details
        schema_rw_entry = container.schema_rw()

        # Check if the entry title matches rw_title
        assert schema_rw_entry.username == container.rw_title(), "Incorrect schema_rw entry title"
        assert schema_rw_entry.password, "Password should not be empty"
        assert isinstance(schema_rw_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("Schema_rw entry details resolved correctly")

    @allure.severity(allure.severity_level.MINOR)
    @allure.title('Test schema_ro entry details')
    def test_schema_ro_entry_details(self, container):
        """Test that the schema_ro resolves correctly with the appropriate title."""

        # Resolve schema_ro entry details
        schema_ro_entry = container.schema_ro()

        # Check if the entry title matches ro_title
        assert schema_ro_entry.username == 'test_ro', "Incorrect schema_ro entry title"
        assert schema_ro_entry.password, "Password should not be empty"
        assert isinstance(schema_ro_entry, EntryDetails), "The resolved entry should be of type EntryDetails"

        # Log the result for Allure report
        allure.step("Schema_ro entry details resolved correctly")

    @allure.severity(allure.severity_level.MINOR)
    @allure.title('Test KeePassManager Singleton behavior')
    def test_keepass_manager_singleton(self, container):
        """Test that the KeePassManager behaves as a singleton."""

        # Resolve KeePass manager twice
        keepass_manager_1 = container.keepass_manager()
        keepass_manager_2 = container.keepass_manager()

        # Assert that both instances are the same (singleton behavior)
        assert keepass_manager_1 is keepass_manager_2, "KeePassManager should be a singleton"

        # Log the result for Allure report
        allure.step("KeePassManager singleton behavior verified")

def test_keepass_members(container):
    assert container is not None
    assert container.rw_title() == "test_rw"
    container.rw_title.override("plat_rw")
    assert container.rw_title() == "plat_rw"
    print(container.schema_rw())