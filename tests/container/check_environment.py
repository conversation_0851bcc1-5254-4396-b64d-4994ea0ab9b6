#!/usr/bin/env python3
"""
Environment check script for database drivers and dependencies.

This script checks if the environment is properly configured for running
the container tests with the correct database drivers.
"""

import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check Python version."""
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("⚠️  Python 3.8+ is recommended")
    else:
        print("✅ Python version is compatible")


def check_package_installation(package_name, import_name=None):
    """Check if a package is installed and importable."""
    if import_name is None:
        import_name = package_name
    
    try:
        # Try to import the package
        module = __import__(import_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✅ {package_name}: {version}")
        return True
    except ImportError as e:
        print(f"❌ {package_name}: Not installed ({e})")
        return False


def check_conflicting_packages():
    """Check for conflicting packages."""
    print("\n🔍 Checking for conflicting packages...")
    
    # Check if psycopg3 is installed (should not be)
    try:
        import psycopg
        print(f"⚠️  psycopg (psycopg3) is installed: {psycopg.__version__}")
        print("   This may conflict with psycopg2. Consider uninstalling psycopg.")
        return False
    except ImportError:
        print("✅ psycopg3 is not installed (good)")
        return True


def check_sqlalchemy_drivers():
    """Check SQLAlchemy driver registration."""
    print("\n🔍 Checking SQLAlchemy driver registration...")
    
    try:
        from sqlalchemy.engine import URL
        
        # Test psycopg2 URL creation
        try:
            url = URL.create(drivername="postgresql+psycopg2", username="test", host="localhost", database="test")
            print("✅ postgresql+psycopg2 driver is registered")
        except Exception as e:
            print(f"❌ postgresql+psycopg2 driver error: {e}")
        
        # Test asyncpg URL creation
        try:
            url = URL.create(drivername="postgresql+asyncpg", username="test", host="localhost", database="test")
            print("✅ postgresql+asyncpg driver is registered")
        except Exception as e:
            print(f"❌ postgresql+asyncpg driver error: {e}")
            
    except ImportError as e:
        print(f"❌ SQLAlchemy import error: {e}")


def check_test_dependencies():
    """Check test-specific dependencies."""
    print("\n🔍 Checking test dependencies...")
    
    test_packages = [
        ("pytest", "pytest"),
        ("pytest-asyncio", "pytest_asyncio"),
        ("allure-pytest", "allure_pytest"),
        ("pytest-cov", "pytest_cov"),
        ("coverage", "coverage"),
    ]
    
    all_good = True
    for package_name, import_name in test_packages:
        if not check_package_installation(package_name, import_name):
            all_good = False
    
    return all_good


def check_database_drivers():
    """Check database driver packages."""
    print("\n🔍 Checking database drivers...")
    
    drivers = [
        ("psycopg2-binary", "psycopg2"),
        ("asyncpg", "asyncpg"),
        ("SQLAlchemy", "sqlalchemy"),
    ]
    
    all_good = True
    for package_name, import_name in drivers:
        if not check_package_installation(package_name, import_name):
            all_good = False
    
    return all_good


def suggest_fixes():
    """Suggest fixes for common issues."""
    print("\n💡 Suggested fixes:")
    print("1. Install required dependencies:")
    print("   pip install psycopg2-binary asyncpg sqlalchemy")
    print("   pip install -r tests/container/requirements-test.txt")
    print()
    print("2. If psycopg3 is installed and causing conflicts:")
    print("   pip uninstall psycopg")
    print()
    print("3. If you're using a virtual environment, make sure it's activated:")
    print("   source venv/bin/activate  # Linux/Mac")
    print("   venv\\Scripts\\activate     # Windows")
    print()
    print("4. Try running the driver compatibility test:")
    print("   python tests/container/test_database_drivers.py")


def run_simple_test():
    """Run a simple test to verify the environment."""
    print("\n🧪 Running simple environment test...")
    
    try:
        # Test basic imports
        from sqlalchemy import create_engine
        from sqlalchemy.engine import URL
        from sqlalchemy.ext.asyncio import create_async_engine
        
        # Test URL creation (this is where the error usually occurs)
        sync_url = URL.create(
            drivername="postgresql+psycopg2",
            username="test",
            password="test",
            host="localhost",
            port=5432,
            database="test"
        )
        
        async_url = URL.create(
            drivername="postgresql+asyncpg",
            username="test",
            password="test",
            host="localhost",
            port=5432,
            database="test"
        )
        
        print("✅ URL creation successful")
        print(f"   Sync URL: {sync_url}")
        print(f"   Async URL: {async_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False


def main():
    """Main function."""
    print("🔧 ENVIRONMENT COMPATIBILITY CHECK")
    print("=" * 60)
    
    # Check Python version
    check_python_version()
    
    # Check database drivers
    drivers_ok = check_database_drivers()
    
    # Check for conflicts
    no_conflicts = check_conflicting_packages()
    
    # Check SQLAlchemy driver registration
    check_sqlalchemy_drivers()
    
    # Check test dependencies
    test_deps_ok = check_test_dependencies()
    
    # Run simple test
    simple_test_ok = run_simple_test()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if drivers_ok and no_conflicts and test_deps_ok and simple_test_ok:
        print("✅ Environment is ready for testing!")
        print("You can now run: python tests/container/run_tests.py --coverage --allure")
        return 0
    else:
        print("❌ Environment has issues that need to be fixed.")
        suggest_fixes()
        return 1


if __name__ == "__main__":
    sys.exit(main())
