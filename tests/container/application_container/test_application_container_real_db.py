# coding=utf-8

import allure
import pytest
from allure_commons.types import LinkType

from dags.data_pipeline.containers import <PERSON>Container, PostgresSessionManager


@pytest.fixture
@allure.title("Setting up ApplicationContainer")
def container():
    """Fixture to provide an ApplicationContainer for testing."""
    container = ApplicationContainer()

    # # Mock database_rw to avoid real database dependency
    # mock_db_rw = MagicMock()
    # mock_db_rw.get_schemas.return_value = [
    #     'acq', 'information_schema', 'plat', 'plp', 'public', 'train'
    # ]
    # container.database_rw.override(mock_db_rw)
    container.wire(modules=["__main__"])
    yield container

    # Clean up wiring after test
    container.unwire()


@allure.feature("ApplicationContainer")
@allure.story("Test against real db")
class TestApplicationContainer:

    @pytest.mark.jira("AIR-41")
    @allure.issue("AIR-41", "View issue in Jira")
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test pg_rw_entry resolution")
    @allure.label("requirement", "AIR-1")
    def test_pg_rw_entry(self, container):
        """Test that `pg_rw_entry` resolves correctly."""
        container.schema.override("plat")
        pg_rw_entry = container.pg_rw_entry()
        assert pg_rw_entry.username == "plat_rw", "RW entry username mismatch"
        assert pg_rw_entry.custom_properties["DB_SERVER_NAME"] == "localhost", "DB_SERVER_NAME mismatch"

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test pg_ro_entry resolution")
    def test_pg_ro_entry(self, container):
        """Test that `pg_ro_entry` resolves correctly."""
        container.schema.override("acq")
        pg_ro_entry = container.pg_ro_entry()

        assert pg_ro_entry.username == "acq_ro", "RO entry username mismatch"

    @allure.severity(allure.severity_level.MINOR)
    @allure.title("Test database_rw session manager resolution")
    def test_database_plat_rw(self, container):
        """Test that `database_rw` resolves correctly."""
        container.schema.override("plat")

        db_rw_manager = container.database_rw()

        assert isinstance(db_rw_manager, PostgresSessionManager), "Resolved object is not a PostgresSessionManager"
        assert db_rw_manager.entry.username == "plat_rw", "RW database entry mismatch"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test singleton behavior of pg_rw_entry")
    def test_pg_rw_entry_singleton(self, container):
        """Test that `pg_rw_entry` is resolved as a singleton."""
        container.schema.override("plat")
        pg_rw_entry_1 = container.pg_rw_entry()
        pg_rw_entry_2 = container.pg_rw_entry()

        assert pg_rw_entry_1 is not pg_rw_entry_2, "pg_rw_entry should not be a singleton"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test pg_ro_entry resolution with different schema")
    def test_pg_ro_entry_different_schema(self, container):
        """Test that `pg_ro_entry` resolves with a different schema."""
        schema_name = "plat"
        container.schema.override(schema_name)
        pg_ro_entry = container.pg_ro_entry()

        assert schema_name in pg_ro_entry.username, "RO entry schema mismatch in URL"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test schema_translate_map update to different schema")
    def test_update_schema_updates_schema_translate_map(self, container):
        """Test that `update_schema` correctly updates the schema_translate_map on the engines."""

        initial_schema = "public"
        with container.database_rw().session() as pg_session:
            pg_session.schema = initial_schema

            # Simulate the initial schema_translate_map on the mock engine
            # session_manager.engine.execution_options.return_value = session_manager.engine

            # Call update_schema with a new schema
        new_schema = "plat"
        container.database_rw().update_schema(new_schema)
        with container.database_rw().session() as pg_session:
            # Verify the schema_translate_map is updated on the engine
            pg_session.bind.update_execution_options(schema_translate_map={None: new_schema})
            assert pg_session.bind.execution_options().name == "postgresql"
            # Check that the schema was actually updated in the session_manager
            # assert pg_session.schema == new_schema, "Schema was not updated correctly"
            assert container.database_rw().get_schemas() == [
                'acq', 'cpp', 'information_schema', 'plat', 'plp', 'public',], "Schema list is not matching"

    @pytest.mark.scenario("AIR-47")
    @allure.title("Get Schema")
    # @allure.description("Get the schema from db")
    @allure.severity(allure.severity_level.NORMAL)
    @allure.issue('https://biyani.atlassian.net/browse/AIR-47', 'create schema')
    @allure.link('https://biyani.atlassian.net/browse/AIR-47', LinkType.ISSUE, 'test issue linkage')
    def test_container_initialization(self, container):
        """
        Test that the container initializes correctly.
        All schema exists
        """
        assert container is not None
        assert container.database_rw().get_schemas() == ['acq', 'cpp', 'information_schema', 'plat', 'plp', 'public']
