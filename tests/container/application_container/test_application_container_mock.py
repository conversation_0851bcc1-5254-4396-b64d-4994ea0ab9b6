# coding=utf-8
import pytest
import allure
from unittest.mock import MagicMock

from dags.data_pipeline.containers import <PERSON>Container, PyKeePass, PostgresSessionManager


@pytest.fixture(scope="module")
@allure.title("Setting up Mock ApplicationContainer")
def container():
    """Fixture to provide the ApplicationContainer instance."""
    container = ApplicationContainer()

    # Mock configuration values
    container.config.override({
        "KeePassDir": {
            "DB_NAME": "mocked_db_name.kdbx",
            "KEY_FILE": "mocked_key_file.key"
        }
    })

    # Mock the KeePass manager
    mock_keepass_manager = MagicMock(spec=PyKeePass)

    mock_entry_rw = MagicMock()
    mock_entry_rw.username = "rw_user"
    mock_entry_rw.url = "postgres://rw_user:rw_password@localhost:5432/db"
    mock_entry_rw.custom_properties = {
        "DB_SERVER_NAME": "**************",
        "DB_SERVER_RW_PORT": 5432,
        "DB_SERVER_RO_PORT": 5433,
        "DB_NAME": "test_database"
    }

    mock_entry_ro = MagicMock()
    mock_entry_ro.username = "ro_user"
    mock_entry_ro.url = "postgres://ro_user:ro_password@localhost:5432/db"
    mock_entry_ro.custom_properties = {
        "DB_SERVER_NAME": "localhost",
        "DB_SERVER_RW_PORT": 5432,
        "DB_SERVER_RO_PORT": 5433,
        "DB_NAME": "test_database"
    }

    mock_keepass_manager.find_entries.side_effect = lambda title, first: (
        mock_entry_rw if "rw" in title else mock_entry_ro
    )
    container.keepass_manager.override(mock_keepass_manager)

    # Override pg_rw_entry and pg_ro_entry directly with the mock objects
    container.pg_rw_entry.override(mock_entry_rw)
    container.pg_ro_entry.override(mock_entry_ro)

    yield container


@pytest.fixture(scope="module")
def session_manager():
    """Fixture to provide the PostgresSessionManager instance."""
    # Create a mock PostgresSessionManager instance
    # mock_engine = create_autospec(Engine, instance=True)
    mock_engine = MagicMock()
    mock_engine.username = "rw_user"

    mock_engine.password = "dummy"
    mock_engine.url = ""
    mock_engine.custom_properties = {
        "DB_SERVER_NAME": "**************",
        "DB_SERVER_RW_PORT": 5432,
        "DB_SERVER_RO_PORT": 5433,
        "DB_NAME": "test_database"
    }

    # Mock the execution_options method for the engine
    mock_engine.execution_options.return_value = mock_engine

    def get_schemas():
        return ["public", "plat"]

    mock_engine.get_schemas = get_schemas

    # Initialize the PostgresSessionManager with the mock engine
    session_manager = PostgresSessionManager(
        entry=mock_engine,
        schema="initial_schema",
        rw=True  # or False depending on your use case
    )

    session_manager.engine = mock_engine
    session_manager.engine_async = mock_engine  # Assuming engine_async is the same for this test

    yield session_manager


@allure.feature("ApplicationContainer")
@allure.story("Test component resolutions")
class TestApplicationContainer:

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test pg_rw_entry resolution")
    def test_pg_rw_entry(self, container):
        """Test that `pg_rw_entry` resolves correctly."""
        container.schema.override("test_schema")
        pg_rw_entry = container.pg_rw_entry()

        assert pg_rw_entry.username == "rw_user", "RW entry username mismatch"
        assert "rw_user" in pg_rw_entry.url, "RW entry URL mismatch"

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test pg_ro_entry resolution")
    def test_pg_ro_entry(self, container):
        """Test that `pg_ro_entry` resolves correctly."""
        container.schema.override("test_schema")
        pg_ro_entry = container.pg_ro_entry()

        assert pg_ro_entry.username == "ro_user", "RO entry username mismatch"
        assert "ro_user" in pg_ro_entry.url, "RO entry URL mismatch"

    @allure.severity(allure.severity_level.MINOR)
    @allure.title("Test database_rw session manager resolution")
    def test_database_rw(self, container):
        """Test that `database_rw` resolves correctly."""
        container.schema.override("test_schema")

        db_rw_manager = container.database_rw()

        assert isinstance(db_rw_manager, PostgresSessionManager), \
            "Resolved object is not a PostgresSessionManager"
        assert db_rw_manager.entry.username == "rw_user", "RW database entry mismatch"

    @allure.severity(allure.severity_level.MINOR)
    @allure.title("Test database_ro session manager resolution")
    def test_database_ro(self, container):
        """Test that `database_ro` resolves correctly."""
        container.schema.override("test_schema")
        db_ro_manager = container.database_ro()

        assert isinstance(db_ro_manager, PostgresSessionManager), \
            "Resolved object is not a PostgresSessionManager"
        assert db_ro_manager.entry.username == "ro_user", "RO database entry mismatch"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test singleton behavior of KeePass manager")
    def test_keepass_manager_singleton(self, container):
        """Test that `keepass_manager` is resolved as a singleton."""
        keepass_manager_1 = container.keepass_manager()
        keepass_manager_2 = container.keepass_manager()

        assert keepass_manager_1 is keepass_manager_2, "KeePass manager should be a singleton"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test singleton behavior of pg_rw_entry")
    def test_pg_rw_entry_singleton(self, container):
        """Test that `pg_rw_entry` is resolved as a singleton."""
        container.schema.override("test_schema")
        pg_rw_entry_1 = container.pg_rw_entry()
        pg_rw_entry_2 = container.pg_rw_entry()

        assert pg_rw_entry_1 is pg_rw_entry_2, "pg_rw_entry should not be a singleton"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test pg_ro_entry resolution with different schema")
    def test_pg_ro_entry_different_schema(self, container):
        """Test that `pg_ro_entry` resolves with a different schema."""
        container.schema.override("alternate_schema")
        pg_ro_entry = container.pg_ro_entry()

        assert "ro_user" in pg_ro_entry.url, "RO entry schema mismatch in URL"

    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test schema_translate_map update to different schema")
    def test_update_schema_updates_schema_translate_map(self, session_manager):
        """Test that `update_schema` correctly updates the schema_translate_map on the engines."""
        # Initial schema
        initial_schema = "public"
        session_manager.schema = initial_schema

        # Simulate the initial schema_translate_map on the mock engine
        # session_manager.engine.execution_options.return_value = session_manager.engine

        # Call update_schema with a new schema
        new_schema = "new_schema"
        session_manager.update_schema(new_schema)

        # Verify the schema_translate_map is updated on the engine
        session_manager.engine.execution_options.assert_called_with(schema_translate_map={None: new_schema})
        session_manager.engine_async.execution_options.assert_called_with(schema_translate_map={None: new_schema})

        # Check that the schema was actually updated in the session_manager
        assert session_manager.schema == new_schema, "Schema was not updated correctly"
        assert session_manager.engine.get_schemas() == ["public", "plat"], "Schema list is not matching"
