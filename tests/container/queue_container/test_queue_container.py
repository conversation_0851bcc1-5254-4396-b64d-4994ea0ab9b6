# coding=utf-8
import pytest
import asyncio
import allure
from dags.data_pipeline.containers import QueueContainer

# Define the fixture for creating the QueueContainer instance
@pytest.fixture(scope="module")
def container():
    """Fixture to provide the QueueContainer instance."""
    container = QueueContainer()
    yield container


# Test class with Allure annotations
@allure.epic('Queue Handling')  # Epic for Queue related tests
@allure.feature('QueueContainer')  # Feature for QueueContainer functionality
@allure.story('Test queue selection for different schemas')  # Story for Schema-specific tests
@allure.label("owner", "Vishal")  # Label indicating the owner of the tests
@allure.suite('Queue Selection Tests')  # Grouping tests related to queue selection
class TestQueueContainer:

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test queue selection for "acq" schema')
    @allure.testcase("http://testcase-link.com/acq-schema")  # Link to external testcase management
    def test_queue_selector_acq(self, container):
        """Test that the 'acq' schema returns the correct queue instances."""

        # Set the schema to 'acq' for testing
        container.config.override({"schema_name": "acq"})

        # Resolve the queue selector
        queue_acq = container.queue_selector()

        # Validate the queue for 'acq'
        assert isinstance(queue_acq, dict), "Queue for 'acq' should be a dictionary"
        assert "queue_issues" in queue_acq, "Queue for 'acq' should have 'queue_issues'"
        assert isinstance(queue_acq["queue_issues"], asyncio.Queue), "Queue 'queue_issues' should be an instance of asyncio.Queue"

        # Log the result for Allure report
        allure.step("Queue selection for 'acq' schema was successful")

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test queue selection for "plp" schema')
    @allure.testcase("http://testcase-link.com/plp-schema")
    def test_queue_selector_plp(self, container):
        """Test that the 'plp' schema returns the correct queue instances."""

        # Set the schema to 'plp' for testing
        container.config.override({"schema_name": "plp"})

        # Resolve the queue selector
        queue_plp = container.queue_selector()

        # Validate the queue for 'plp'
        assert isinstance(queue_plp, dict), "Queue for 'plp' should be a dictionary"
        assert "queue_issues" in queue_plp, "Queue for 'plp' should have 'queue_issues'"
        assert isinstance(queue_plp["queue_issues"], asyncio.Queue), "Queue 'queue_issues' should be an instance of asyncio.Queue"

        # Log the result for Allure report
        allure.step("Queue selection for 'plp' schema was successful")

    @allure.severity(allure.severity_level.NORMAL)
    @allure.title('Test queue selection for "plat" schema')
    @allure.testcase("http://testcase-link.com/plat-schema")
    def test_queue_selector_plat(self, container):
        """Test that the 'plat' schema returns the correct queue instances."""

        # Set the schema to 'plat' for testing
        container.config.override({"schema_name": "plat"})

        # Resolve the queue selector
        queue_plat = container.queue_selector()

        # Validate the queue for 'plat'
        assert isinstance(queue_plat, dict), "Queue for 'plat' should be a dictionary"
        assert "queue_issues" in queue_plat, "Queue for 'plat' should have 'queue_issues'"
        assert isinstance(queue_plat["queue_issues"], asyncio.Queue), "Queue 'queue_issues' should be an instance of asyncio.Queue"

        # Log the result for Allure report
        allure.step("Queue selection for 'plat' schema was successful")

    @allure.severity(allure.severity_level.MINOR)
    @allure.title('Test queue instances are singleton across multiple requests')
    @allure.testcase("http://testcase-link.com/singleton-queues")
    def test_singleton_queue_instances(self, container):
        """Test that the same instance of queues is returned for multiple requests."""

        # Set schema to 'acq' for first test
        container.config.override({"schema_name": "acq"})
        queue_acq_1 = container.queue_selector()

        # Set schema to 'acq' for second test
        container.config.override({"schema_name": "acq"})
        queue_acq_2 = container.queue_selector()

        # Ensure both instances are the same (singleton behavior)
        assert queue_acq_1["queue_issues"] is queue_acq_2["queue_issues"], "Queues for 'acq' should be the same instance"

        # Log the result for Allure report
        allure.step("Singleton behavior for queue instances verified successfully")
