# coding=utf-8
"""
Performance test suite for refactored session manager architecture.

This test suite benchmarks:
- Session creation and cleanup performance
- Connection recovery overhead
- TaskLifecycleCoordinator coordination efficiency
- Memory usage and resource management
- Concurrent session handling
"""

import pytest
import allure
import asyncio
import time
import psutil
import gc
from unittest.mock import MagicMock, patch, AsyncMock
from contextlib import asynccontextmanager

from dags.data_pipeline.containers import (
    RefactoredPostgresSessionManager,
    EnhancedSessionManager,
    FullyEnhancedSessionManager,
    EntryDetails
)
from dags.data_pipeline.utility_code import TaskLifecycleCoordinator


@pytest.fixture
@allure.title("Performance Test Entry Details")
def perf_entry_details():
    """Fixture providing mock database entry details for performance testing."""
    return EntryDetails(
        username="perf_user",
        password="perf_password",
        url="postgresql://perf_user:perf_password@localhost:5432/perfdb",
        custom_properties={
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "perfdb"
        }
    )


@pytest.fixture
@allure.title("Performance Logger")
def perf_logger():
    """Fixture providing a mock logger for performance testing."""
    return MagicMock()


@allure.epic("Performance Testing")
@allure.feature("Session Manager Performance")
class TestSessionManagerPerformance:
    """Performance test suite for session managers."""
    
    @allure.story("Session Creation Performance")
    @allure.title("Should create sessions efficiently")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_session_creation_performance(self, mock_create_sync_engine, mock_create_async_engine, 
                                              perf_entry_details, perf_logger):
        """Benchmark session creation performance."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Benchmark different session manager types
        session_managers = [
            ("RefactoredPostgresSessionManager", RefactoredPostgresSessionManager),
            ("EnhancedSessionManager", EnhancedSessionManager),
            ("FullyEnhancedSessionManager", FullyEnhancedSessionManager)
        ]
        
        results = {}
        
        for name, manager_class in session_managers:
            start_time = time.perf_counter()
            
            # Create multiple session managers
            managers = []
            for i in range(100):
                manager = manager_class(perf_entry_details, f"schema_{i}", True, perf_logger)
                managers.append(manager)
                
            end_time = time.perf_counter()
            creation_time = end_time - start_time
            
            # Cleanup
            for manager in managers:
                await manager.aclose()
                
            results[name] = creation_time
            
        # Log performance results
        for name, time_taken in results.items():
            allure.attach(f"{name}: {time_taken:.4f}s", name=f"{name}_creation_time", attachment_type=allure.attachment_type.TEXT)
            
        # Assert performance requirements
        assert results["RefactoredPostgresSessionManager"] < 1.0, "Base session manager should create 100 instances in <1s"
        assert results["EnhancedSessionManager"] < 1.5, "Enhanced session manager should create 100 instances in <1.5s"
        assert results["FullyEnhancedSessionManager"] < 2.0, "Fully enhanced session manager should create 100 instances in <2s"
        
    @allure.story("Connection Recovery Performance")
    @allure.title("Should handle connection failures efficiently")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_connection_recovery_performance(self, mock_create_sync_engine, mock_create_async_engine, 
                                                 perf_entry_details, perf_logger):
        """Benchmark connection recovery performance."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        manager = EnhancedSessionManager(perf_entry_details, "perf_schema", True, perf_logger)
        
        # Simulate connection failures and measure recovery time
        from sqlalchemy.exc import InterfaceError
        
        recovery_times = []
        
        for i in range(10):
            start_time = time.perf_counter()
            
            # Simulate connection error handling
            error = InterfaceError("Connection failed", "test", "test")
            should_retry = await manager._handle_connection_error(error, f"test_operation_{i}")
            
            if should_retry:
                await manager._recreate_async_engine()
                
            end_time = time.perf_counter()
            recovery_times.append(end_time - start_time)
            
        avg_recovery_time = sum(recovery_times) / len(recovery_times)
        max_recovery_time = max(recovery_times)
        
        allure.attach(f"Average recovery time: {avg_recovery_time:.4f}s", name="avg_recovery_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Max recovery time: {max_recovery_time:.4f}s", name="max_recovery_time", attachment_type=allure.attachment_type.TEXT)
        
        # Assert performance requirements
        assert avg_recovery_time < 0.1, "Average connection recovery should be <100ms"
        assert max_recovery_time < 0.5, "Max connection recovery should be <500ms"
        
        await manager.aclose()
        
    @allure.story("Memory Usage")
    @allure.title("Should manage memory efficiently")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_memory_usage(self, mock_create_sync_engine, mock_create_async_engine, 
                               perf_entry_details, perf_logger):
        """Test memory usage and cleanup efficiency."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Measure initial memory
        gc.collect()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Create many session managers
        managers = []
        for i in range(1000):
            manager = FullyEnhancedSessionManager(perf_entry_details, f"schema_{i}", True, perf_logger)
            managers.append(manager)
            
        # Measure peak memory
        gc.collect()
        peak_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Cleanup all managers
        for manager in managers:
            await manager.aclose()
        managers.clear()
        
        # Force garbage collection
        gc.collect()
        
        # Measure final memory
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        cleanup_efficiency = (memory_cleanup / memory_increase) * 100 if memory_increase > 0 else 100
        
        allure.attach(f"Initial memory: {initial_memory:.2f} MB", name="initial_memory", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Peak memory: {peak_memory:.2f} MB", name="peak_memory", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Final memory: {final_memory:.2f} MB", name="final_memory", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Cleanup efficiency: {cleanup_efficiency:.1f}%", name="cleanup_efficiency", attachment_type=allure.attachment_type.TEXT)
        
        # Assert memory management requirements
        assert memory_increase < 100, "Memory increase should be <100MB for 1000 session managers"
        assert cleanup_efficiency > 80, "Memory cleanup should be >80% efficient"


@allure.epic("Performance Testing")
@allure.feature("TaskLifecycleCoordinator Performance")
class TestTaskLifecycleCoordinatorPerformance:
    """Performance test suite for TaskLifecycleCoordinator."""
    
    @allure.story("Task Registration Performance")
    @allure.title("Should register tasks efficiently")
    @pytest.mark.asyncio
    async def test_task_registration_performance(self, perf_logger):
        """Benchmark task registration performance."""
        coordinator = TaskLifecycleCoordinator(perf_logger)
        
        # Benchmark task registration
        start_time = time.perf_counter()
        
        task_ids = []
        for i in range(10000):
            task_id = await coordinator.register_task(f"task_{i}")
            task_ids.append(task_id)
            
        end_time = time.perf_counter()
        registration_time = end_time - start_time
        
        # Benchmark task completion
        start_time = time.perf_counter()
        
        for task_id in task_ids:
            await coordinator.mark_task_completed(task_id, "success")
            
        end_time = time.perf_counter()
        completion_time = end_time - start_time
        
        allure.attach(f"Registration time: {registration_time:.4f}s", name="registration_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Completion time: {completion_time:.4f}s", name="completion_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Registration rate: {10000/registration_time:.0f} tasks/s", name="registration_rate", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Completion rate: {10000/completion_time:.0f} tasks/s", name="completion_rate", attachment_type=allure.attachment_type.TEXT)
        
        # Assert performance requirements
        assert registration_time < 1.0, "Should register 10,000 tasks in <1s"
        assert completion_time < 1.0, "Should complete 10,000 tasks in <1s"
        assert 10000/registration_time > 5000, "Registration rate should be >5,000 tasks/s"
        
    @allure.story("Monitor Coordination Performance")
    @allure.title("Should coordinate monitors efficiently")
    @pytest.mark.asyncio
    async def test_monitor_coordination_performance(self, perf_logger):
        """Benchmark monitor coordination performance."""
        coordinator = TaskLifecycleCoordinator(perf_logger)
        
        # Create multiple monitors
        monitors = []
        for i in range(100):
            async def mock_monitor():
                try:
                    while True:
                        await asyncio.sleep(0.01)
                except asyncio.CancelledError:
                    raise
                    
            monitor_task = asyncio.create_task(mock_monitor())
            monitors.append(monitor_task)
            await coordinator.register_monitor(monitor_task, f"monitor_{i}")
            
        # Register a task and complete it to trigger shutdown
        task_id = await coordinator.register_task("trigger_task")
        
        # Measure shutdown performance
        start_time = time.perf_counter()
        
        await coordinator.mark_task_completed(task_id, "success")
        await coordinator.wait_for_all_tasks_completion(timeout=10.0)
        
        end_time = time.perf_counter()
        shutdown_time = end_time - start_time
        
        # Verify all monitors were cancelled
        cancelled_count = sum(1 for monitor in monitors if monitor.cancelled())
        
        allure.attach(f"Shutdown time: {shutdown_time:.4f}s", name="shutdown_time", attachment_type=allure.attachment_type.TEXT)
        allure.attach(f"Monitors cancelled: {cancelled_count}/100", name="monitors_cancelled", attachment_type=allure.attachment_type.TEXT)
        
        # Assert performance requirements
        assert shutdown_time < 2.0, "Should shutdown 100 monitors in <2s"
        assert cancelled_count >= 95, "Should cancel at least 95% of monitors"


@allure.epic("Performance Testing")
@allure.feature("Concurrent Operations")
class TestConcurrentPerformance:
    """Performance test suite for concurrent operations."""
    
    @allure.story("Concurrent Session Usage")
    @allure.title("Should handle concurrent sessions efficiently")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_concurrent_session_usage(self, mock_create_sync_engine, mock_create_async_engine, 
                                          perf_entry_details, perf_logger):
        """Test concurrent session usage performance."""
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        manager = FullyEnhancedSessionManager(perf_entry_details, "concurrent_schema", True, perf_logger)
        
        # Mock session creation
        with patch('dags.data_pipeline.containers.sessionmaker') as mock_sessionmaker:
            mock_session = AsyncMock()
            mock_session_factory = MagicMock(return_value=mock_session)
            mock_sessionmaker.return_value = mock_session_factory
            
            async def concurrent_session_usage(session_id):
                """Simulate concurrent session usage."""
                async with manager.async_session() as session:
                    # Simulate database work
                    await asyncio.sleep(0.01)
                    return f"result_{session_id}"
                    
            # Run concurrent sessions
            start_time = time.perf_counter()
            
            tasks = [concurrent_session_usage(i) for i in range(50)]
            results = await asyncio.gather(*tasks)
            
            end_time = time.perf_counter()
            concurrent_time = end_time - start_time
            
            allure.attach(f"Concurrent execution time: {concurrent_time:.4f}s", name="concurrent_time", attachment_type=allure.attachment_type.TEXT)
            allure.attach(f"Throughput: {50/concurrent_time:.1f} sessions/s", name="throughput", attachment_type=allure.attachment_type.TEXT)
            
            # Assert performance requirements
            assert concurrent_time < 2.0, "50 concurrent sessions should complete in <2s"
            assert len(results) == 50, "All concurrent sessions should complete successfully"
            assert 50/concurrent_time > 20, "Throughput should be >20 sessions/s"
            
        await manager.aclose()
