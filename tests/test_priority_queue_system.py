# coding=utf-8
"""
Tests for the priority queue system implementation.

This module tests the priority queue system with composite keys to ensure
proper message ordering and priority handling.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock
from dags.data_pipeline.priority_queue_system import (
    PriorityQueueManager, PriorityMessage, MessageType, priority_queue_manager
)


class TestPriorityMessage:
    """Test suite for PriorityMessage class."""
    
    def test_priority_message_creation(self):
        """Test creating a priority message with composite key."""
        msg = PriorityMessage(
            message_type_priority=MessageType.ISSUE_DIRECT.value,
            task_id=1,
            counter=5,
            data={"test": "data"}
        )
        
        # Verify composite priority calculation
        expected_priority = (1 << 32) + (1 << 16) + 5
        assert msg.priority == expected_priority
        assert msg.data == {"test": "data"}
    
    def test_priority_message_ordering(self):
        """Test that priority messages are ordered correctly."""
        # Create messages with different priorities
        msg1 = PriorityMessage(
            message_type_priority=MessageType.ISSUE_DIRECT.value,  # Priority 1
            task_id=1,
            counter=1,
            data="high_priority"
        )
        
        msg2 = PriorityMessage(
            message_type_priority=MessageType.SPECIALIZED.value,  # Priority 2
            task_id=1,
            counter=1,
            data="medium_priority"
        )
        
        msg3 = PriorityMessage(
            message_type_priority=MessageType.TERMINATION.value,  # Priority 999
            task_id=1,
            counter=1,
            data=None
        )
        
        # Sort messages - lower priority number should come first
        messages = [msg3, msg2, msg1]
        messages.sort()
        
        assert messages[0].data == "high_priority"
        assert messages[1].data == "medium_priority"
        assert messages[2].data is None


class TestPriorityQueueManager:
    """Test suite for PriorityQueueManager class."""
    
    def test_counter_generation(self):
        """Test that counters are generated correctly per task."""
        manager = PriorityQueueManager()
        
        # Test counter generation for same task
        counter1 = manager._get_next_counter(1)
        counter2 = manager._get_next_counter(1)
        counter3 = manager._get_next_counter(1)
        
        assert counter1 == 1
        assert counter2 == 2
        assert counter3 == 3
        
        # Test counter generation for different task
        counter4 = manager._get_next_counter(2)
        assert counter4 == 1
    
    def test_create_priority_message(self):
        """Test creating priority messages through manager."""
        manager = PriorityQueueManager()
        
        msg = manager.create_priority_message(
            data={"test": "data"},
            message_type=MessageType.ISSUE_DIRECT,
            task_id=5
        )
        
        assert msg.message_type_priority == MessageType.ISSUE_DIRECT.value
        assert msg.task_id == 5
        assert msg.counter == 1
        assert msg.data == {"test": "data"}
    
    @pytest.mark.asyncio
    async def test_put_and_get_priority_message(self):
        """Test putting and getting messages from priority queue."""
        manager = PriorityQueueManager()
        queue = asyncio.PriorityQueue()
        
        # Put messages with different priorities
        await manager.put_priority_message(
            queue, "low_priority", MessageType.TERMINATION, 1
        )
        await manager.put_priority_message(
            queue, "high_priority", MessageType.ISSUE_DIRECT, 1
        )
        await manager.put_priority_message(
            queue, "medium_priority", MessageType.SPECIALIZED, 1
        )
        
        # Get messages - should come out in priority order
        msg1 = await manager.get_priority_message(queue)
        msg2 = await manager.get_priority_message(queue)
        msg3 = await manager.get_priority_message(queue)
        
        assert msg1 == "high_priority"
        assert msg2 == "medium_priority"
        assert msg3 == "low_priority"


class TestMessageTypePriorities:
    """Test suite for MessageType enum priorities."""
    
    def test_message_type_values(self):
        """Test that message types have correct priority values."""
        assert MessageType.ISSUE_DIRECT.value == 1
        assert MessageType.SPECIALIZED.value == 2
        assert MessageType.REGULAR_DATA.value == 3
        assert MessageType.TERMINATION.value == 999
    
    @pytest.mark.asyncio
    async def test_priority_ordering_in_queue(self):
        """Test that messages are processed in correct priority order."""
        queue = asyncio.PriorityQueue()
        
        # Add messages in random order
        test_data = [
            (MessageType.TERMINATION, "termination"),
            (MessageType.REGULAR_DATA, "regular"),
            (MessageType.ISSUE_DIRECT, "issue_direct"),
            (MessageType.SPECIALIZED, "specialized"),
        ]
        
        for msg_type, data in test_data:
            await priority_queue_manager.put_priority_message(
                queue, data, msg_type, 1
            )
        
        # Retrieve messages - should be in priority order
        results = []
        while not queue.empty():
            msg = await priority_queue_manager.get_priority_message(queue)
            results.append(msg)
        
        expected_order = ["issue_direct", "specialized", "regular", "termination"]
        assert results == expected_order


class TestGlobalPriorityQueueManager:
    """Test suite for the global priority queue manager instance."""
    
    def test_global_instance_exists(self):
        """Test that global priority queue manager instance exists."""
        assert priority_queue_manager is not None
        assert isinstance(priority_queue_manager, PriorityQueueManager)
    
    @pytest.mark.asyncio
    async def test_global_instance_functionality(self):
        """Test that global instance works correctly."""
        queue = asyncio.PriorityQueue()
        
        await priority_queue_manager.put_priority_message(
            queue, "test_data", MessageType.ISSUE_DIRECT, 1
        )
        
        result = await priority_queue_manager.get_priority_message(queue)
        assert result == "test_data"


@pytest.mark.asyncio
async def test_none_has_lowest_priority():
    """Test that None messages (termination signals) have the lowest priority."""
    queue = asyncio.PriorityQueue()
    
    # Add various messages including None
    await priority_queue_manager.put_priority_message(
        queue, {"data": "regular"}, MessageType.REGULAR_DATA, 1
    )
    await priority_queue_manager.put_priority_message(
        queue, None, MessageType.TERMINATION, 1
    )
    await priority_queue_manager.put_priority_message(
        queue, {"data": "issue"}, MessageType.ISSUE_DIRECT, 1
    )
    
    # Get messages - None should come last
    msg1 = await priority_queue_manager.get_priority_message(queue)
    msg2 = await priority_queue_manager.get_priority_message(queue)
    msg3 = await priority_queue_manager.get_priority_message(queue)
    
    assert msg1 == {"data": "issue"}
    assert msg2 == {"data": "regular"}
    assert msg3 is None


@pytest.mark.asyncio
async def test_task_id_ordering():
    """Test that messages with same priority are ordered by task_id."""
    queue = asyncio.PriorityQueue()
    
    # Add messages with same priority but different task_ids
    await priority_queue_manager.put_priority_message(
        queue, "task_3", MessageType.ISSUE_DIRECT, 3
    )
    await priority_queue_manager.put_priority_message(
        queue, "task_1", MessageType.ISSUE_DIRECT, 1
    )
    await priority_queue_manager.put_priority_message(
        queue, "task_2", MessageType.ISSUE_DIRECT, 2
    )
    
    # Get messages - should be ordered by task_id
    msg1 = await priority_queue_manager.get_priority_message(queue)
    msg2 = await priority_queue_manager.get_priority_message(queue)
    msg3 = await priority_queue_manager.get_priority_message(queue)
    
    assert msg1 == "task_1"
    assert msg2 == "task_2"
    assert msg3 == "task_3"
