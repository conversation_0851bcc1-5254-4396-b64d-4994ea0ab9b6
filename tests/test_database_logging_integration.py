#!/usr/bin/env python3
# coding=utf-8
"""
Integration test for database logging with dependency injection.
This test verifies that the database logging works correctly with the container system.
"""

import asyncio
import logging
import os
import sys
import tempfile
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_database_logging_integration():
    """Test database logging integration with containers."""
    print("🧪 Testing database logging integration...")
    
    try:
        from containers import ApplicationContainer, initialize_database_logging
        from custom_logger import CorrelationContext
        
        # Set up test environment
        os.environ["ENABLE_DATABASE_LOGGING"] = "true"
        os.environ["DB_LOG_BATCH_SIZE"] = "5"  # Small batch for testing
        os.environ["DB_LOG_FLUSH_INTERVAL"] = "2"  # Quick flush
        
        # Initialize container
        container = ApplicationContainer()
        container.wire(modules=[__name__])
        
        # Initialize basic logging
        container.logger_container.init_resources()
        
        # Configure database logging
        db_handlers = initialize_database_logging(
            container=container,
            schema='public',  # Use public schema for testing
            enabled=True
        )
        
        print(f"✅ Created {len(db_handlers)} database log handlers")
        
        # Test logging with correlation
        logger = container.logger_container.logger()
        
        with CorrelationContext("integration_test") as ctx:
            logger.info("Starting integration test")
            logger.warning("This is a test warning")
            logger.error("This is a test error")
            logger.info("Integration test completed")
        
        # Wait for flush
        time.sleep(3)
        
        # Verify handlers are working
        for handler in db_handlers:
            if hasattr(handler, 'session_manager') and handler.session_manager:
                print(f"✅ Handler has session manager: {type(handler.session_manager).__name__}")
            else:
                print(f"❌ Handler missing session manager")
        
        print("✅ Database logging integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Database logging integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_handler_factory():
    """Test the DatabaseLogHandlerFactory."""
    print("🧪 Testing DatabaseLogHandlerFactory...")
    
    try:
        from custom_logger import DatabaseLogHandlerFactory, DatabaseLogHandler
        
        # Test creating handler without session manager
        handler = DatabaseLogHandlerFactory.create_deferred_handler(
            batch_size=10,
            flush_interval=5,
            schema='test',
            enabled=True,
            level=logging.INFO
        )
        
        assert isinstance(handler, DatabaseLogHandler)
        assert handler.batch_size == 10
        assert handler.flush_interval == 5
        assert handler.schema == 'test'
        assert handler.enabled == True
        assert handler.session_manager is None  # Should be None initially
        
        print("✅ DatabaseLogHandlerFactory test passed")
        return True
        
    except Exception as e:
        print(f"❌ DatabaseLogHandlerFactory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configure_database_logging():
    """Test the configure_database_logging function."""
    print("🧪 Testing configure_database_logging function...")
    
    try:
        from custom_logger import configure_database_logging, DatabaseLogHandler
        
        # Create a mock session manager
        class MockSessionManager:
            def __init__(self):
                self.schema = 'test'
        
        mock_session = MockSessionManager()
        
        # Configure database logging for a test logger
        test_logger_name = "test_db_logger"
        handler = configure_database_logging(
            logger_name=test_logger_name,
            session_manager=mock_session,
            schema='test',
            batch_size=20,
            flush_interval=10,
            level=logging.DEBUG
        )
        
        assert isinstance(handler, DatabaseLogHandler)
        assert handler.session_manager == mock_session
        assert handler.batch_size == 20
        assert handler.flush_interval == 10
        
        # Verify handler was added to logger
        test_logger = logging.getLogger(test_logger_name)
        db_handlers = [h for h in test_logger.handlers if isinstance(h, DatabaseLogHandler)]
        assert len(db_handlers) == 1
        
        print("✅ configure_database_logging test passed")
        return True
        
    except Exception as e:
        print(f"❌ configure_database_logging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_correlation_with_database():
    """Test correlation context with database logging."""
    print("🧪 Testing correlation context with database logging...")
    
    try:
        from custom_logger import CorrelationContext, UnifiedUnicodeHandler
        
        # Create a test log record
        class MockRecord:
            def __init__(self):
                self.msg = "Test message"
                self.args = ()
                self.levelname = "INFO"
                self.name = "test"
                self.funcName = "test_func"
                self.lineno = 123
                self.thread = 12345
                self.process = 67890
                self.created = time.time()
        
        # Test with correlation context
        with CorrelationContext("test_operation") as ctx:
            handler = UnifiedUnicodeHandler()
            record = MockRecord()
            
            # Process the record
            result = handler.filter(record)
            
            assert result == True
            assert hasattr(record, 'correlation_id')
            assert record.correlation_id is not None
            assert record.correlation_id != 'N/A'
        
        print("✅ Correlation context with database test passed")
        return True
        
    except Exception as e:
        print(f"❌ Correlation context with database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_async_correlation():
    """Test async correlation tracking."""
    print("🧪 Testing async correlation tracking...")
    
    try:
        from custom_logger import CorrelationContext
        
        async def async_task(task_id):
            async with CorrelationContext(f"async_task_{task_id}") as ctx:
                # Simulate some async work
                await asyncio.sleep(0.01)
                return ctx.correlation_id
        
        # Run multiple async tasks
        tasks = [async_task(i) for i in range(3)]
        correlation_ids = await asyncio.gather(*tasks)
        
        # Verify each task got a unique correlation ID
        assert len(set(correlation_ids)) == 3  # All should be unique
        assert all(cid is not None for cid in correlation_ids)
        
        print("✅ Async correlation tracking test passed")
        return True
        
    except Exception as e:
        print(f"❌ Async correlation tracking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_all_tests():
    """Run all integration tests."""
    print("🚀 Database Logging Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Handler Factory", test_handler_factory),
        ("Configure Function", test_configure_database_logging),
        ("Correlation Context", test_correlation_with_database),
        ("Async Correlation", test_async_correlation),
        ("Full Integration", test_database_logging_integration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All integration tests passed!")
        print("\nDatabase logging is properly integrated with:")
        print("- ✅ Dependency injection containers")
        print("- ✅ Correlation ID tracking")
        print("- ✅ Async operation support")
        print("- ✅ Unicode handling")
        print("- ✅ Factory pattern for handler creation")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return failed == 0


if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Tests failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
