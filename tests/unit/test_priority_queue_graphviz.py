import pytest
import random
import time
import graphviz
import allure
from collections import defaultdict
from datetime import datetime
from pathlib import Path

# Simulate message types and priorities
MESSAGE_TYPES = ["IssueInitiative", "Issue", "Worklog", "Changelog", "Comment", "IssueLink"]

TYPE_PRIORITY = {
    "IssueInitiative": 1,
    "Issue": 2,
    "Worklog": 3,
    "Changelog": 4,
    "Comment": 5,
    "IssueLink": 6,
    "None": 999
}

# Realistic range for related messages per issue
RELATED_COUNTS = {
    "Worklog": (0, 5),
    "Changelog": (0, 3),
    "IssueLink": (0, 2),
    "Comment": (0, 4),
}


class Message:
    def __init__(self, msg_type, parent_id=None, group_id=0, priority=None):
        self.msg_type = msg_type
        self.id = f"{msg_type}_{random.randint(10000, 99999)}"
        self.group_id = group_id
        self.parent_id = parent_id
        self.priority = TYPE_PRIORITY.get(msg_type, 999) if priority is None else priority
        self.entered_at = datetime.now()
        self.processed_at = None

    def process(self):
        self.processed_at = datetime.now()


@pytest.mark.parametrize("use_unique_task_ids", [True, False])
@allure.epic("Message Processing")
@allure.title("Graphviz visualization of async processing sequence")
def test_message_flow_graphviz(tmp_path: Path, use_unique_task_ids: bool):
    random.seed(42)
    num_groups = 5
    base_issues_per_group = 20
    all_messages = []
    edges = []
    node_labels = {}

    color_map = {
        "IssueInitiative": "gold",
        "Issue": "deepskyblue",
        "Worklog": "orange",
        "Changelog": "limegreen",
        "Comment": "pink",
        "IssueLink": "violet",
        "None": "gray"
    }

    # Generate messages
    for group_id in range(num_groups):
        issues_count = int(base_issues_per_group * random.uniform(0.9, 1.1))

        for _ in range(issues_count):
            issue = Message("Issue", group_id=group_id)
            initiative = Message("IssueInitiative", parent_id=issue.id, group_id=group_id)
            all_messages.extend([initiative, issue])
            edges.append((initiative.id, issue.id))

            for related_type, (min_c, max_c) in RELATED_COUNTS.items():
                for _ in range(random.randint(min_c, max_c)):
                    child = Message(related_type, parent_id=issue.id, group_id=group_id)
                    all_messages.append(child)
                    edges.append((issue.id, child.id))

    # Process messages in priority order
    queue = sorted(all_messages, key=lambda m: m.priority)
    for msg in queue:
        msg.process()

    # Generate Graphviz diagram
    dot = graphviz.Digraph(comment="Message Processing Flow", format="svg")
    dot.attr(rankdir="LR", fontsize="12")

    for msg in all_messages:
        label = f"{msg.msg_type}\\n{msg.id}\\nprio: {msg.priority}"
        color = color_map.get(msg.msg_type, "black")
        shape = "box" if msg.msg_type == "IssueInitiative" else "ellipse"
        dot.node(msg.id, label=label, color=color, style="filled", fillcolor=color, shape=shape)
        node_labels[msg.id] = label

    for src, dst in edges:
        dot.edge(src, dst)

    filename_base = f"message_flow_{'unique' if use_unique_task_ids else 'default'}"
    out_path = tmp_path / f"{filename_base}.svg"
    dot.render(out_path.with_suffix(""), cleanup=True)

    # Attach to Allure
    allure.attach.file(str(out_path), name=f"Graphviz Message Flow ({filename_base})", attachment_type=allure.attachment_type.SVG)

    # Basic validation
    assert any(m.msg_type == "Issue" for m in all_messages)
    assert any(m.processed_at for m in all_messages)
