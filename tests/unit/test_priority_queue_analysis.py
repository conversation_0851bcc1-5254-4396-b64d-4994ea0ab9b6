import asyncio
import time
import random
from dataclasses import dataclass, field
from enum import IntEnum
from typing import Any, List, Dict, Tuple
from collections import Counter, defaultdict
from asyncio import PriorityQueue
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import pandas as pd
import pytest
import allure
from io import StringIO
import numpy as np

# ---------- Realistic Configuration ----------
# Simulating your actual use case with 100K messages
NUM_ISSUES = 10000  # Number of unique issues
WORKLOG_PROBABILITY = 0.7  # 70% of issues have worklogs
CHANGELOG_PROBABILITY = 0.4  # 40% of issues have changelogs
COMMENT_PROBABILITY = 0.8  # 80% of issues have comments
ISSUELINK_PROBABILITY = 0.3  # 30% of issues have issue links

# Realistic message counts per issue (based on typical usage)
WORKLOG_RANGE = (1, 8)  # 1-8 worklogs per issue that has worklogs
CHANGELOG_RANGE = (1, 5)  # 1-5 changelogs per issue that has changelogs
COMMENT_RANGE = (1, 12)  # 1-12 comments per issue that has comments
ISSUELINK_RANGE = (1, 3)  # 1-3 issue links per issue that has links

# Processing groups (e.g., different data sources or processors)
NUM_GROUPS = 5


# ----------------------------------

class MessageType(IntEnum):
    ISSUE_INITIATIVE = 0
    ISSUE_DIRECT = 1
    WORKLOG = 2
    CHANGELOG = 3
    COMMENTS = 4
    ISSUELINKS = 5
    SENTINEL = 999


@dataclass(order=True)
class PriorityMessage:
    priority: int = field(init=False)
    message_type_priority: int
    task_id: int = field(compare=False)
    counter: int = field(compare=False)
    data: Any = field(compare=False)
    enqueue_time: float = field(default_factory=time.time, compare=False)
    sequence_number: int = field(default=0, compare=False)

    def __post_init__(self):
        self.priority = (
                (self.message_type_priority << 32) +
                (self.task_id << 16) +
                self.counter
        )


class PriorityQueueManager:
    def __init__(self):
        self._counters = {}
        self._global_sequence = 0

    def _get_next_counter(self, task_id: int) -> int:
        self._counters.setdefault(task_id, 0)
        val = self._counters[task_id]
        self._counters[task_id] += 1
        return val

    def create_priority_message(self, data: Any, message_type: MessageType, task_id: int) -> PriorityMessage:
        counter = self._get_next_counter(task_id)
        self._global_sequence += 1
        return PriorityMessage(
            message_type_priority=message_type.value,
            task_id=task_id,
            counter=counter,
            data=data,
            sequence_number=self._global_sequence
        )

    async def put_priority_message(self, queue: PriorityQueue, data: Any, message_type: MessageType, task_id: int):
        message = self.create_priority_message(data, message_type, task_id)
        await queue.put(message)


async def simulate_realistic_jira_workflow(queue: PriorityQueue, manager: PriorityQueueManager,
                                           use_unique_task_ids: bool):
    """Simulate realistic JIRA-like message flow with proper parent-child relationships"""

    print(f"Generating {NUM_ISSUES:,} issues with realistic message distribution...")

    messages_generated = 0

    for group_id in range(NUM_GROUPS):
        issues_per_group = NUM_ISSUES // NUM_GROUPS

        for issue_num in range(issues_per_group):
            issue_id = f"PROJ-{group_id * 1000 + issue_num + 1}"

            # Task ID strategy
            if use_unique_task_ids:
                # Each group gets its own task ID space
                issue_task_id = group_id * 10 + 1
                worklog_task_id = group_id * 10 + 2
                changelog_task_id = group_id * 10 + 3
                comment_task_id = group_id * 10 + 4
                issuelink_task_id = group_id * 10 + 5
            else:
                # All messages use task_id = 0
                issue_task_id = 0
                worklog_task_id = 0
                changelog_task_id = 0
                comment_task_id = 0
                issuelink_task_id = 0

            # 1. Create the main issue
            await manager.put_priority_message(
                queue,
                data={
                    "source": "Issue",
                    "issue_id": issue_id,
                    "group_id": group_id,
                    "parent_id": None
                },
                message_type=MessageType.ISSUE_DIRECT,
                task_id=issue_task_id
            )
            messages_generated += 1

            # 2. Create related messages based on probabilities

            # Worklogs
            if random.random() < WORKLOG_PROBABILITY:
                num_worklogs = random.randint(*WORKLOG_RANGE)
                for wl_num in range(num_worklogs):
                    await manager.put_priority_message(
                        queue,
                        data={
                            "source": "Worklog",
                            "issue_id": issue_id,
                            "group_id": group_id,
                            "parent_id": issue_id,
                            "worklog_id": f"{issue_id}-WL-{wl_num + 1}"
                        },
                        message_type=MessageType.WORKLOG,
                        task_id=worklog_task_id
                    )
                    messages_generated += 1

            # Changelogs
            if random.random() < CHANGELOG_PROBABILITY:
                num_changelogs = random.randint(*CHANGELOG_RANGE)
                for cl_num in range(num_changelogs):
                    await manager.put_priority_message(
                        queue,
                        data={
                            "source": "Changelog",
                            "issue_id": issue_id,
                            "group_id": group_id,
                            "parent_id": issue_id,
                            "changelog_id": f"{issue_id}-CL-{cl_num + 1}"
                        },
                        message_type=MessageType.CHANGELOG,
                        task_id=changelog_task_id
                    )
                    messages_generated += 1

            # Comments
            if random.random() < COMMENT_PROBABILITY:
                num_comments = random.randint(*COMMENT_RANGE)
                for cm_num in range(num_comments):
                    await manager.put_priority_message(
                        queue,
                        data={
                            "source": "Comment",
                            "issue_id": issue_id,
                            "group_id": group_id,
                            "parent_id": issue_id,
                            "comment_id": f"{issue_id}-CM-{cm_num + 1}"
                        },
                        message_type=MessageType.COMMENTS,
                        task_id=comment_task_id
                    )
                    messages_generated += 1

            # Issue Links
            if random.random() < ISSUELINK_PROBABILITY:
                num_issuelinks = random.randint(*ISSUELINK_RANGE)
                for il_num in range(num_issuelinks):
                    await manager.put_priority_message(
                        queue,
                        data={
                            "source": "IssueLink",
                            "issue_id": issue_id,
                            "group_id": group_id,
                            "parent_id": issue_id,
                            "link_id": f"{issue_id}-IL-{il_num + 1}"
                        },
                        message_type=MessageType.ISSUELINKS,
                        task_id=issuelink_task_id
                    )
                    messages_generated += 1

            # Add some initiatives (lower priority, processed later)
            if random.random() < 0.1:  # 10% of issues have initiatives
                await manager.put_priority_message(
                    queue,
                    data={
                        "source": "Initiative",
                        "issue_id": issue_id,
                        "group_id": group_id,
                        "parent_id": issue_id
                    },
                    message_type=MessageType.ISSUE_INITIATIVE,
                    task_id=group_id * 10 if use_unique_task_ids else 0
                )
                messages_generated += 1

    await manager.put_priority_message(
        queue,
        data={
            "source": "Changelog",
            "issue_id": issue_id,
            "group_id": group_id,
            "parent_id": issue_id,
            "changelog_id": f"{issue_id}-CL-{cl_num + 1}"
        },
        message_type=MessageType.SENTINEL,
        task_id=changelog_task_id
    )
    print(f"Generated {messages_generated:,} total messages")
    return messages_generated


async def run_large_scale_analysis(use_unique_task_ids: bool = True):
    """Run large-scale analysis similar to production workload"""

    start_time = time.time()
    queue = PriorityQueue()
    manager = PriorityQueueManager()

    # Generate realistic message load
    total_generated = await simulate_realistic_jira_workflow(queue, manager, use_unique_task_ids)

    generation_time = time.time() - start_time
    print(f"Message generation took {generation_time:.2f} seconds")

    # Process queue and collect results
    print("Processing priority queue...")
    process_start = time.time()

    results = []
    dequeue_order = 0
    sample_interval = max(1, total_generated // 1000)  # Sample every N messages for analysis

    while not queue.empty():
        msg: PriorityMessage = await queue.get()

        # Only collect detailed data for a sample to avoid memory issues
        if dequeue_order % sample_interval == 0 or dequeue_order < 1000:
            results.append({
                "dequeue_order": dequeue_order,
                "priority": msg.priority,
                "message_type": msg.message_type_priority,
                "task_id": msg.task_id,
                "counter": msg.counter,
                "source": msg.data["source"],
                "group": msg.data["group_id"],
                "issue_id": msg.data["issue_id"],
                "sequence_number": msg.sequence_number,
                "parent_id": msg.data.get("parent_id"),
                "processing_delay": time.time() - msg.enqueue_time
            })

        dequeue_order += 1

        # Progress indicator
        if dequeue_order % 10000 == 0:
            print(f"Processed {dequeue_order:,} messages...")

    processing_time = time.time() - process_start
    total_time = time.time() - start_time

    print(f"Processing took {processing_time:.2f} seconds")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Processing rate: {total_generated / total_time:,.0f} messages/second")

    return results, {
        "total_messages": total_generated,
        "generation_time": generation_time,
        "processing_time": processing_time,
        "total_time": total_time,
        "processing_rate": total_generated / total_time
    }


def analyze_realistic_patterns(results_unique: List[Dict], results_shared: List[Dict],
                               stats_unique: Dict, stats_shared: Dict) -> str:
    """Analyze patterns specific to realistic JIRA-like workflow"""

    report = StringIO()
    report.write("=== LARGE-SCALE PRIORITY QUEUE ANALYSIS (Realistic JIRA Simulation) ===\n\n")

    # Performance Analysis
    report.write("1. PERFORMANCE ANALYSIS:\n")
    report.write(f"   Unique Task IDs Mode:\n")
    report.write(f"     • Total messages: {stats_unique['total_messages']:,}\n")
    report.write(f"     • Processing rate: {stats_unique['processing_rate']:,.0f} messages/second\n")
    report.write(f"     • Total time: {stats_unique['total_time']:.2f} seconds\n")

    report.write(f"   Shared Task ID Mode:\n")
    report.write(f"     • Total messages: {stats_shared['total_messages']:,}\n")
    report.write(f"     • Processing rate: {stats_shared['processing_rate']:,.0f} messages/second\n")
    report.write(f"     • Total time: {stats_shared['total_time']:.2f} seconds\n")

    perf_diff = ((stats_unique['processing_rate'] - stats_shared['processing_rate']) / stats_shared[
        'processing_rate']) * 100
    report.write(f"   Performance difference: {perf_diff:+.1f}% (Unique vs Shared)\n\n")

    # Message Type Distribution Analysis
    report.write("2. MESSAGE TYPE DISTRIBUTION:\n")
    for mode, results in [("Unique", results_unique), ("Shared", results_shared)]:
        type_counts = Counter(r['source'] for r in results)
        total_sampled = len(results)
        report.write(f"   {mode} Task IDs (sampled {total_sampled:,} messages):\n")
        for msg_type, count in sorted(type_counts.items()):
            percentage = count / total_sampled * 100
            report.write(f"     • {msg_type}: {count:,} ({percentage:.1f}%)\n")
        report.write("\n")

    # Task ID Utilization Analysis
    report.write("3. TASK ID UTILIZATION:\n")
    unique_task_ids = set(r['task_id'] for r in results_unique)
    shared_task_ids = set(r['task_id'] for r in results_shared)

    report.write(f"   Unique Mode: Uses {len(unique_task_ids)} different task IDs: {sorted(unique_task_ids)}\n")
    report.write(f"   Shared Mode: Uses {len(shared_task_ids)} different task IDs: {sorted(shared_task_ids)}\n\n")

    # Priority Distribution Analysis
    unique_priorities = [r['priority'] for r in results_unique]
    shared_priorities = [r['priority'] for r in results_shared]

    report.write("4. PRIORITY DISTRIBUTION ANALYSIS:\n")
    report.write(f"   Priority ranges:\n")
    report.write(f"     • Unique: {min(unique_priorities):,} to {max(unique_priorities):,}\n")
    report.write(f"     • Shared: {min(shared_priorities):,} to {max(shared_priorities):,}\n")

    unique_range = max(unique_priorities) - min(unique_priorities)
    shared_range = max(shared_priorities) - min(shared_priorities)
    report.write(f"   Priority space utilization:\n")
    report.write(f"     • Unique: {unique_range:,} priority values\n")
    report.write(f"     • Shared: {shared_range:,} priority values\n\n")

    # Parent-Child Relationship Analysis
    def analyze_parent_child_ordering(results, mode_name):
        """Analyze if child messages are processed after their parents"""
        parent_child_pairs = []
        issue_positions = {}

        # Build position map for issues (parents)
        for i, r in enumerate(results):
            if r['source'] == 'Issue':
                issue_positions[r['issue_id']] = i

        # Check child message positions
        for i, r in enumerate(results):
            if r['parent_id'] and r['parent_id'] in issue_positions:
                parent_pos = issue_positions[r['parent_id']]
                child_pos = i
                parent_child_pairs.append({
                    'parent_pos': parent_pos,
                    'child_pos': child_pos,
                    'ordered_correctly': parent_pos < child_pos,
                    'child_type': r['source']
                })

        if parent_child_pairs:
            correct_orders = sum(1 for p in parent_child_pairs if p['ordered_correctly'])
            total_pairs = len(parent_child_pairs)
            correctness_pct = correct_orders / total_pairs * 100

            return f"{correct_orders}/{total_pairs} ({correctness_pct:.1f}%) parent-child pairs correctly ordered"
        else:
            return "No parent-child relationships found in sample"

    report.write("5. PARENT-CHILD ORDERING ANALYSIS:\n")
    unique_ordering = analyze_parent_child_ordering(results_unique, "Unique")
    shared_ordering = analyze_parent_child_ordering(results_shared, "Shared")

    report.write(f"   Unique Task IDs: {unique_ordering}\n")
    report.write(f"   Shared Task ID:  {shared_ordering}\n\n")

    # Same-Issue Message Clustering Analysis
    def analyze_issue_clustering(results):
        """Analyze how well messages from the same issue are processed together"""
        issue_spans = defaultdict(list)

        for i, r in enumerate(results):
            issue_spans[r['issue_id']].append(i)

        clustering_scores = []
        for issue_id, positions in issue_spans.items():
            if len(positions) > 1:
                span = max(positions) - min(positions)
                ideal_span = len(positions) - 1
                clustering_score = 1 - (span - ideal_span) / (len(results) - ideal_span) if len(
                    results) > ideal_span else 1
                clustering_scores.append(clustering_score)

        if clustering_scores:
            avg_clustering = sum(clustering_scores) / len(clustering_scores)
            return avg_clustering, len(clustering_scores)
        else:
            return 0, 0

    report.write("6. ISSUE MESSAGE CLUSTERING ANALYSIS:\n")
    unique_clustering, unique_issues = analyze_issue_clustering(results_unique)
    shared_clustering, shared_issues = analyze_issue_clustering(results_shared)

    report.write(
        f"   Unique Task IDs: {unique_clustering:.3f} average clustering score ({unique_issues} issues analyzed)\n")
    report.write(
        f"   Shared Task ID:  {shared_clustering:.3f} average clustering score ({shared_issues} issues analyzed)\n")
    report.write("   (Higher score = better clustering, 1.0 = perfect clustering)\n\n")

    # Key Recommendations
    report.write("7. KEY FINDINGS AND RECOMMENDATIONS:\n")

    if perf_diff > 5:
        report.write("   ✓ PERFORMANCE: Unique task IDs show significantly better performance\n")
    elif perf_diff < -5:
        report.write("   ⚠ PERFORMANCE: Shared task ID shows better performance (unexpected)\n")
    else:
        report.write("   → PERFORMANCE: No significant performance difference\n")

    if unique_clustering > shared_clustering + 0.05:
        report.write("   ✓ CLUSTERING: Unique task IDs provide better issue message clustering\n")
    elif shared_clustering > unique_clustering + 0.05:
        report.write("   ⚠ CLUSTERING: Shared task ID provides better clustering (unexpected)\n")
    else:
        report.write("   → CLUSTERING: No significant clustering difference\n")

    priority_efficiency = len(unique_task_ids) > len(shared_task_ids)
    if priority_efficiency:
        report.write("   ✓ PRIORITY SPACE: Unique task IDs utilize priority space more efficiently\n")
    else:
        report.write("   → PRIORITY SPACE: No efficiency gain from unique task IDs\n")

    report.write("\n")

    # Final Recommendation
    benefits = 0
    if perf_diff > 5: benefits += 1
    if unique_clustering > shared_clustering + 0.05: benefits += 1
    if priority_efficiency: benefits += 1

    report.write("8. FINAL RECOMMENDATION:\n")
    if benefits >= 2:
        report.write("   🎯 RECOMMENDATION: IMPLEMENT unique task IDs\n")
        report.write("   Multiple benefits observed. The complexity of implementing unique task IDs is justified.\n")
    elif benefits == 1:
        report.write("   🤔 RECOMMENDATION: CONSIDER unique task IDs\n")
        report.write("   Some benefits observed. Evaluate if the implementation effort is worth it.\n")
    else:
        report.write("   💡 RECOMMENDATION: KEEP shared task ID (task_id = 0)\n")
        report.write("   No significant benefits from unique task IDs. Simpler implementation is better.\n")

    return report.getvalue()


def create_large_scale_visualizations(results_unique: List[Dict], results_shared: List[Dict],
                                      stats_unique: Dict, stats_shared: Dict):
    """Create visualizations optimized for large-scale data"""

    # Create subplot figure
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=[
            'Message Type Processing Order',
            'Task ID Distribution',
            'Priority Value Distribution',
            'Issue Message Clustering',
            'Processing Performance',
            'Parent-Child Relationship Validation'
        ],
        specs=[
            [{"secondary_y": False}, {"secondary_y": False}, {"secondary_y": False}],
            [{"secondary_y": False}, {"secondary_y": False}, {"secondary_y": False}]
        ],
        vertical_spacing=0.15,
        horizontal_spacing=0.1
    )

    # Plot 1: Message Type Processing Order
    for mode, results, color in [("Unique", results_unique, "blue"), ("Shared", results_shared, "red")]:
        sample_results = results[::max(1, len(results) // 200)]  # Sample for visualization
        fig.add_trace(
            go.Scatter(
                x=[r['dequeue_order'] for r in sample_results],
                y=[r['message_type'] for r in sample_results],
                mode='markers',
                name=f'{mode} Task IDs',
                marker=dict(color=color, size=4, opacity=0.6),
                text=[f"{r['source']} ({r['issue_id']})" for r in sample_results]
            ),
            row=1, col=1
        )

    # Plot 2: Task ID Distribution
    unique_task_counts = Counter(r['task_id'] for r in results_unique)
    shared_task_counts = Counter(r['task_id'] for r in results_shared)

    fig.add_trace(
        go.Bar(
            x=list(unique_task_counts.keys()),
            y=list(unique_task_counts.values()),
            name='Unique Task IDs',
            marker_color='blue',
            opacity=0.7
        ),
        row=1, col=2
    )
    if shared_task_counts:
        fig.add_trace(
            go.Bar(
                x=list(shared_task_counts.keys()),
                y=list(shared_task_counts.values()),
                name='Shared Task ID',
                marker_color='red',
                opacity=0.7
            ),
            row=1, col=2
        )

    # Plot 3: Priority Distribution (histogram)
    fig.add_trace(
        go.Histogram(
            x=[r['priority'] for r in results_unique],
            name='Unique Priority Dist',
            opacity=0.7,
            marker_color='blue',
            nbinsx=50
        ),
        row=1, col=3
    )
    fig.add_trace(
        go.Histogram(
            x=[r['priority'] for r in results_shared],
            name='Shared Priority Dist',
            opacity=0.7,
            marker_color='red',
            nbinsx=50
        ),
        row=1, col=3
    )

    # Plot 4: Issue Message Clustering (sample analysis)
    def calculate_clustering_sample(results):
        issue_groups = defaultdict(list)
        sample_results = results[:1000]  # Sample first 1000 for detailed analysis

        for i, r in enumerate(sample_results):
            issue_groups[r['issue_id']].append(i)

        clustering_data = []
        for issue_id, positions in issue_groups.items():
            if len(positions) > 1:
                span = max(positions) - min(positions)
                clustering_data.append({
                    'issue': issue_id,
                    'message_count': len(positions),
                    'span': span,
                    'clustering_score': 1 - span / len(sample_results)
                })

        return clustering_data

    unique_clustering = calculate_clustering_sample(results_unique)
    shared_clustering = calculate_clustering_sample(results_shared)

    if unique_clustering:
        fig.add_trace(
            go.Scatter(
                x=[c['message_count'] for c in unique_clustering],
                y=[c['clustering_score'] for c in unique_clustering],
                mode='markers',
                name='Unique Clustering',
                marker=dict(color='blue', size=6),
                text=[c['issue'] for c in unique_clustering]
            ),
            row=2, col=1
        )

    if shared_clustering:
        fig.add_trace(
            go.Scatter(
                x=[c['message_count'] for c in shared_clustering],
                y=[c['clustering_score'] for c in shared_clustering],
                mode='markers',
                name='Shared Clustering',
                marker=dict(color='red', size=6),
                text=[c['issue'] for c in shared_clustering]
            ),
            row=2, col=1
        )

    # Plot 5: Performance Comparison
    metrics = ['Total Messages', 'Processing Rate (msg/s)', 'Total Time (s)']
    unique_values = [stats_unique['total_messages'], stats_unique['processing_rate'], stats_unique['total_time']]
    shared_values = [stats_shared['total_messages'], stats_shared['processing_rate'], stats_shared['total_time']]

    fig.add_trace(
        go.Bar(
            x=metrics,
            y=unique_values,
            name='Unique Task IDs',
            marker_color='blue',
            opacity=0.7
        ),
        row=2, col=2
    )
    fig.add_trace(
        go.Bar(
            x=metrics,
            y=shared_values,
            name='Shared Task ID',
            marker_color='red',
            opacity=0.7
        ),
        row=2, col=2
    )

    # Plot 6: Parent-Child Validation Sample
    def get_parent_child_sample(results):
        sample = results[:500]  # First 500 messages
        parent_positions = {}
        validation_data = []

        for i, r in enumerate(sample):
            if r['source'] == 'Issue':
                parent_positions[r['issue_id']] = i

        for i, r in enumerate(sample):
            if r['parent_id'] and r['parent_id'] in parent_positions:
                parent_pos = parent_positions[r['parent_id']]
                validation_data.append({
                    'parent_pos': parent_pos,
                    'child_pos': i,
                    'correctly_ordered': i > parent_pos,
                    'type': r['source']
                })

        return validation_data

    unique_validation = get_parent_child_sample(results_unique)
    shared_validation = get_parent_child_sample(results_shared)

    for mode, validation_data, color in [("Unique", unique_validation, "blue"), ("Shared", shared_validation, "red")]:
        if validation_data:
            correct = [v for v in validation_data if v['correctly_ordered']]
            incorrect = [v for v in validation_data if not v['correctly_ordered']]

            if correct:
                fig.add_trace(
                    go.Scatter(
                        x=[v['parent_pos'] for v in correct],
                        y=[v['child_pos'] for v in correct],
                        mode='markers',
                        name=f'{mode} Correct Order',
                        marker=dict(color=color, symbol='circle', size=6),
                        text=[v['type'] for v in correct]
                    ),
                    row=2, col=3
                )

            if incorrect:
                fig.add_trace(
                    go.Scatter(
                        x=[v['parent_pos'] for v in incorrect],
                        y=[v['child_pos'] for v in incorrect],
                        mode='markers',
                        name=f'{mode} Wrong Order',
                        marker=dict(color=color, symbol='x', size=8),
                        text=[v['type'] for v in incorrect]
                    ),
                    row=2, col=3
                )

    # Update layout
    fig.update_layout(
        height=800,
        title_text="Large-Scale Priority Queue Analysis: Task ID Impact on 100K+ Messages",
        showlegend=True
    )

    # Update axes
    fig.update_xaxes(title_text="Processing Order", row=1, col=1)
    fig.update_yaxes(title_text="Message Type", row=1, col=1)

    fig.update_xaxes(title_text="Task ID", row=1, col=2)
    fig.update_yaxes(title_text="Message Count", row=1, col=2)

    fig.update_xaxes(title_text="Priority Value", row=1, col=3)
    fig.update_yaxes(title_text="Frequency", row=1, col=3)

    fig.update_xaxes(title_text="Messages per Issue", row=2, col=1)
    fig.update_yaxes(title_text="Clustering Score", row=2, col=1)

    fig.update_xaxes(title_text="Metric", row=2, col=2)
    fig.update_yaxes(title_text="Value", row=2, col=2)

    fig.update_xaxes(title_text="Parent Position", row=2, col=3)
    fig.update_yaxes(title_text="Child Position", row=2, col=3)

    return fig


@pytest.mark.asyncio
@allure.title("Large-Scale Priority Queue Analysis: 100K+ Messages")
@allure.description("Realistic simulation of JIRA-like workflow with 100,000+ messages")
async def test_large_scale_priority_queue_analysis():
    """Test priority queue behavior with realistic large-scale message load"""

    with allure.step("Running large-scale test with Unique Task IDs"):
        results_unique, stats_unique = await run_large_scale_analysis(use_unique_task_ids=True)

    with allure.step("Running large-scale test with Shared Task ID"):
        results_shared, stats_shared = await run_large_scale_analysis(use_unique_task_ids=False)

    with allure.step("Generating comprehensive analysis report"):
        analysis_report = analyze_realistic_patterns(results_unique, results_shared, stats_unique, stats_shared)
        allure.attach(analysis_report, name="Large-Scale Analysis Report", attachment_type=allure.attachment_type.TEXT)

    with allure.step("Creating large-scale visualizations"):
        fig = create_large_scale_visualizations(results_unique, results_shared, stats_unique, stats_shared)
        html_str = fig.to_html(include_plotlyjs='cdn')
        allure.attach(html_str, name="Large-Scale Priority Analysis", attachment_type=allure.attachment_type.HTML)

    with allure.step("Generating performance comparison"):
        perf_report = StringIO()
        perf_report.write("=== PERFORMANCE COMPARISON SUMMARY ===\n\n")
        perf_report.write(f"Unique Task IDs Mode:\n")
        perf_report.write(f"  • Messages processed: {stats_unique['total_messages']:,}\n")
        perf_report.write(f"  • Processing rate: {stats_unique['processing_rate']:,.0f} msg/sec\n")
        perf_report.write(f"  • Total time: {stats_unique['total_time']:.2f} seconds\n")
        perf_report.write(f"  • Generation time: {stats_unique['generation_time']:.2f} seconds\n")
        perf_report.write(f"  • Queue processing time: {stats_unique['processing_time']:.2f} seconds\n\n")

        perf_report.write(f"Shared Task ID Mode:\n")
        perf_report.write(f"  • Messages processed: {stats_shared['total_messages']:,}\n")
        perf_report.write(f"  • Processing rate: {stats_shared['processing_rate']:,.0f} msg/sec\n")
        perf_report.write(f"  • Total time: {stats_shared['total_time']:.2f} seconds\n")
        perf_report.write(f"  • Generation time: {stats_shared['generation_time']:.2f} seconds\n")
        perf_report.write(f"  • Queue processing time: {stats_shared['processing_time']:.2f} seconds\n\n")

        perf_diff = ((stats_unique['processing_rate'] - stats_shared['processing_rate']) / stats_shared[
            'processing_rate']) * 100
        perf_report.write(f"Performance Difference: {perf_diff:+.1f}%\n")
        if abs(perf_diff) < 2:
            perf_report.write("CONCLUSION: No significant performance difference\n")
        elif perf_diff > 0:
            perf_report.write("CONCLUSION: Unique task IDs perform better\n")
        else:
            perf_report.write("CONCLUSION: Shared task ID performs better\n")

        allure.attach(perf_report.getvalue(), name="Performance Comparison",
                      attachment_type=allure.attachment_type.TEXT)

    with allure.step("Generating sample message logs"):
        # Sample logs for analysis
        for mode, results in [("Unique", results_unique[:500]), ("Shared", results_shared[:500])]:
            log_content = "Order\tPriority\tType\tTaskID\tSource\tIssueID\tParentID\tGroup\n"
            for r in results:
                log_content += (f"{r['dequeue_order']}\t{r['priority']:,}\t{r['message_type']}\t"
                                f"{r['task_id']}\t{r['source']}\t{r['issue_id']}\t"
                                f"{r.get('parent_id', 'None')}\t{r['group']}\n")

            allure.attach(log_content, name=f"Sample Message Log ({mode} Task IDs)",
                          attachment_type=allure.attachment_type.TEXT)

    # Validation assertions
    with allure.step("Validating test results"):
        # Ensure both tests processed significant number of messages
        assert len(results_unique) > 100, f"Insufficient unique mode sample: {len(results_unique)}"
        assert len(results_shared) > 100, f"Insufficient shared mode sample: {len(results_shared)}"

        # Ensure we actually processed a large number of messages
        assert stats_unique['total_messages'] >= 50000, f"Expected 50K+ messages, got {stats_unique['total_messages']}"
        assert stats_shared['total_messages'] >= 50000, f"Expected 50K+ messages, got {stats_shared['total_messages']}"

        # Ensure task IDs are correctly assigned
        unique_task_ids = set(r['task_id'] for r in results_unique)
        shared_task_ids = set(r['task_id'] for r in results_shared)

        assert len(unique_task_ids) > 1, f"Unique mode should use multiple task IDs, got: {unique_task_ids}"
        assert shared_task_ids == {0}, f"Shared mode should only use task ID 0, got: {shared_task_ids}"

        # Ensure priority ordering is maintained
        unique_priorities = [r['priority'] for r in results_unique]
        shared_priorities = [r['priority'] for r in results_shared]

        assert unique_priorities == sorted(unique_priorities), "Priority ordering violated in unique mode"
        assert shared_priorities == sorted(shared_priorities), "Priority ordering violated in shared mode"

        print(f"\n✅ Test completed successfully!")
        print(f"Processed {stats_unique['total_messages']:,} messages in unique mode")
        print(f"Processed {stats_shared['total_messages']:,} messages in shared mode")
        print(f"Performance difference: {perf_diff:+.1f}%")


if __name__ == "__main__":
    # Run the test directly for development
    import sys

    print("Starting large-scale priority queue analysis...")
    print(f"Configuration: {NUM_ISSUES:,} issues, {NUM_GROUPS} groups")
    print("This may take a few minutes to complete...")

    try:
        asyncio.run(test_large_scale_priority_queue_analysis())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        sys.exit(1)