import asyncio
import pytest
import time
from unittest.mock import patch

import allure
from allure import severity_level

from dags.data_pipeline.utils.debugging_monitor import MonitorConfig, DebuggingMonitor, AdaptiveSampler, ActivityMetrics


# Import the classes from the main module
# from debugging_monitor import DebuggingMonitor, MonitorConfig, ActivityMetrics, AdaptiveSampler, debug_monitor


@allure.epic("Debugging Monitor")
@allure.feature("Core Functionality")
class TestDebuggingMonitor:
    """Test suite for the DebuggingMonitor class."""

    @pytest.fixture
    def monitor_config(self):
        """Create a test configuration."""
        return MonitorConfig(
            enabled=True,
            log_interval=1,
            max_queue_size_warning=10,
            slow_operation_threshold=0.1,
            memory_warning_threshold=50.0,
            log_suppression_interval=5,
            max_log_entries=100,
            batch_size=10,
            high_volume_threshold=100,
            adaptive_sampling=True
        )

    @pytest.fixture
    def monitor(self, monitor_config):
        """Create a monitor instance for testing."""
        return DebuggingMonitor(monitor_config)

    @allure.story("Monitor Initialization")
    @allure.severity(severity_level.CRITICAL)
    def test_monitor_initialization(self, monitor_config):
        """Test monitor initialization with custom config."""
        with allure.step("Create monitor with custom configuration"):
            monitor = DebuggingMonitor(monitor_config)
            
        with allure.step("Verify configuration is properly set"):
            assert monitor.config.enabled == True
            assert monitor.config.log_interval == 1
            assert monitor.config.adaptive_sampling == True
            
        with allure.step("Verify initial state"):
            assert monitor.start_time > 0
            assert len(monitor.metrics) == 0
            assert len(monitor._active_tasks) == 0
            assert len(monitor._active_queues) == 0

    @allure.story("Context Manager")
    @allure.severity(severity_level.CRITICAL)
    def test_context_manager_basic(self, monitor):
        """Test basic context manager functionality."""
        with allure.step("Use monitor as context manager"):
            with monitor as m:
                assert m is monitor
                assert monitor.monitoring_thread is not None
                
        with allure.step("Verify cleanup after context exit"):
            # Allow some time for cleanup
            time.sleep(0.1)
            assert monitor.stop_monitoring.is_set()

    @allure.story("Context Manager")
    @allure.severity(severity_level.NORMAL)
    def test_context_manager_with_exception(self, monitor):
        """Test context manager behavior with exceptions."""
        with allure.step("Test exception handling in context manager"):
            try:
                with monitor as _:
                    raise ValueError("Test exception")
            except ValueError:
                pass
                
        with allure.step("Verify monitor is properly cleaned up"):
            assert monitor.stop_monitoring.is_set()

    @allure.story("Operation Context")
    @allure.severity(severity_level.NORMAL)
    def test_operation_context_success(self, monitor):
        """Test operation context for successful operations."""
        operation_name = "test_operation"
        
        with allure.step("Execute operation in context"):
            with monitor.operation_context(operation_name):
                time.sleep(0.05)  # Short operation
                
        with allure.step("Verify metrics were recorded"):
            task_metric_key = f'task_{operation_name}'
            assert task_metric_key in monitor.metrics
            assert monitor.metrics[task_metric_key].count > 0

    @allure.story("Operation Context")
    @allure.severity(severity_level.NORMAL)
    def test_operation_context_failure(self, monitor):
        """Test operation context for failed operations."""
        operation_name = "failing_operation"
        
        with allure.step("Execute failing operation in context"):
            try:
                with monitor.operation_context(operation_name):
                    raise RuntimeError("Simulated failure")
            except RuntimeError:
                pass
                
        with allure.step("Verify error metrics were recorded"):
            task_metric_key = f'task_{operation_name}'
            assert task_metric_key in monitor.metrics
            assert monitor.metrics[task_metric_key].errors > 0

    @allure.story("HTTP Request Logging")
    @allure.severity(severity_level.NORMAL)
    def test_log_http_request_success(self, monitor):
        """Test HTTP request logging for successful requests."""
        with allure.step("Log successful HTTP request"):
            monitor.log_http_request("GET", "/api/test", 200)
            
        with allure.step("Verify metrics were updated"):
            assert 'http' in monitor.metrics
            assert monitor.metrics['http'].count == 1
            assert monitor.metrics['http'].errors == 0
            assert monitor.metrics['http'].rate_limits == 0

    @allure.story("HTTP Request Logging")
    @allure.severity(severity_level.NORMAL)
    def test_log_http_request_rate_limit(self, monitor):
        """Test HTTP request logging for rate limited requests."""
        with allure.step("Log rate limited HTTP request"):
            monitor.log_http_request("POST", "/api/data", 429)
            
        with allure.step("Verify rate limit metrics were updated"):
            assert monitor.metrics['http'].rate_limits == 1
            assert monitor.metrics['http'].errors == 1

    @allure.story("Queue Activity Logging")
    @allure.severity(severity_level.NORMAL)
    def test_log_queue_activity_normal(self, monitor):
        """Test queue activity logging under normal conditions."""
        with allure.step("Log normal queue activity"):
            monitor.log_queue_activity("test_queue", "put", 5)
            
        with allure.step("Verify queue metrics were updated"):
            queue_key = 'queue_test_queue_put'
            assert queue_key in monitor.metrics
            assert monitor.metrics[queue_key].count == 1

    @allure.story("Queue Activity Logging")
    @allure.severity(severity_level.NORMAL)
    def test_log_queue_activity_large_queue(self, monitor, caplog):
        """Test queue activity logging for large queues."""
        large_size = monitor.config.max_queue_size_warning + 1
        
        with allure.step("Log large queue activity"):
            monitor.log_queue_activity("large_queue", "put", large_size)
            
        with allure.step("Verify warning was logged"):
            # Note: Due to sampling, warning might not always appear
            # This test verifies the metrics are still updated
            queue_key = 'queue_large_queue_put'
            assert queue_key in monitor.metrics

    @allure.story("Database Operations")
    @allure.severity(severity_level.NORMAL)
    def test_log_db_operation_fast(self, monitor):
        """Test database operation logging for fast operations."""
        with allure.step("Log fast database operation"):
            monitor.log_db_operation("SELECT * FROM users", 0.05)
            
        with allure.step("Verify DB metrics were updated"):
            assert monitor.metrics['db'].count == 1
            assert monitor.metrics['db'].errors == 0

    @allure.story("Database Operations")
    @allure.severity(severity_level.NORMAL)
    def test_log_db_operation_slow(self, monitor):
        """Test database operation logging for slow operations."""
        slow_duration = monitor.config.slow_operation_threshold + 0.1
        
        with allure.step("Log slow database operation"):
            monitor.log_db_operation("COMPLEX QUERY", slow_duration)
            
        with allure.step("Verify slow operation was marked as error"):
            assert monitor.metrics['db'].errors == 1

    @allure.story("Task Management")
    @allure.severity(severity_level.NORMAL)
    def test_task_lifecycle(self, monitor):
        """Test task start and end logging."""
        task_name = "test_task"
        
        with allure.step("Log task start"):
            monitor.log_task_start(task_name)
            
        with allure.step("Log task end (success)"):
            monitor.log_task_end(task_name, success=True)
            
        with allure.step("Verify task metrics"):
            task_key = f'task_{task_name}'
            assert task_key in monitor.metrics
            assert monitor.metrics[task_key].count == 1
            assert monitor.metrics[task_key].errors == 0

    @allure.story("Task Management")
    @allure.severity(severity_level.NORMAL)
    def test_task_failure(self, monitor):
        """Test task failure logging."""
        task_name = "failing_task"
        
        with allure.step("Log task start and failure"):
            monitor.log_task_start(task_name)
            monitor.log_task_end(task_name, success=False)
            
        with allure.step("Verify failure was recorded"):
            task_key = f'task_{task_name}'
            assert monitor.metrics[task_key].errors == 1

    @allure.story("Monitoring Thread")
    @allure.severity(severity_level.NORMAL)
    def test_monitoring_thread_lifecycle(self, monitor):
        """Test monitoring thread start and stop."""
        with allure.step("Start monitoring"):
            monitor.start_monitoring()
            assert monitor.monitoring_thread is not None
            assert monitor.monitoring_thread.is_alive()
            
        with allure.step("Stop monitoring"):
            monitor.stop()
            time.sleep(0.1)  # Allow time for cleanup
            assert monitor.stop_monitoring.is_set()

    @allure.story("Memory Management")
    @allure.severity(severity_level.NORMAL)
    def test_weak_references(self, monitor):
        """Test that weak references don't prevent garbage collection."""
        # Create some objects to track
        class DummyTask:
            pass
            
        class DummyQueue:
            pass
            
        with allure.step("Register objects with monitor"):
            task = DummyTask()
            queue = DummyQueue()
            
            monitor.register_task(task)
            monitor.register_queue(queue)
            
            initial_task_count = len(monitor._active_tasks)
            initial_queue_count = len(monitor._active_queues)
            
        with allure.step("Delete objects and verify garbage collection"):
            del task, queue
            # Weak references should allow garbage collection
            # The actual cleanup might be delayed, so we don't assert exact counts


@allure.epic("Debugging Monitor")
@allure.feature("Adaptive Sampling")
class TestAdaptiveSampler:
    """Test suite for the AdaptiveSampler class."""

    @pytest.fixture
    def sampler(self):
        """Create a sampler for testing."""
        return AdaptiveSampler(high_volume_threshold=10)

    @allure.story("Sampling Rate Adjustment")
    @allure.severity(severity_level.NORMAL)
    def test_low_volume_sampling(self, sampler):
        """Test sampling behavior under low volume."""
        with allure.step("Test low volume sampling"):
            # Low volume should maintain high sampling rate
            for _ in range(5):
                should_sample = sampler.should_sample()
                # At low volume, sampling rate should be high
            
            assert sampler.sample_rate >= 0.8  # Should be close to 1.0

    @allure.story("Sampling Rate Adjustment")
    @allure.severity(severity_level.NORMAL)
    def test_high_volume_sampling(self, sampler):
        """Test sampling behavior under high volume."""
        with allure.step("Simulate high volume"):
            # Simulate high volume by adding many activities quickly
            current_time = time.time()
            for i in range(15):  # Above threshold of 10
                sampler.activity_window.append(current_time + i * 0.01)
            
        with allure.step("Check sampling rate adjustment"):
            sampler.should_sample()  # Trigger rate calculation
            assert sampler.sample_rate < 1.0  # Should reduce sampling


@allure.epic("Debugging Monitor")
@allure.feature("Configuration")
class TestMonitorConfig:
    """Test suite for MonitorConfig."""

    @allure.story("Default Configuration")
    @allure.severity(severity_level.NORMAL)
    def test_default_config(self):
        """Test default configuration values."""
        with allure.step("Create default configuration"):
            config = MonitorConfig()
            
        with allure.step("Verify default values"):
            assert config.enabled == False
            assert config.log_interval == 30
            assert config.adaptive_sampling == True
            assert config.high_volume_threshold == 1000

    @allure.story("Custom Configuration")
    @allure.severity(severity_level.NORMAL)
    def test_custom_config(self):
        """Test custom configuration values."""
        with allure.step("Create custom configuration"):
            config = MonitorConfig(
                enabled=True,
                log_interval=5,
                max_queue_size_warning=50,
                adaptive_sampling=False
            )
            
        with allure.step("Verify custom values"):
            assert config.enabled == True
            assert config.log_interval == 5
            assert config.max_queue_size_warning == 50
            assert config.adaptive_sampling == False


@allure.epic("Debugging Monitor")
@allure.feature("Activity Metrics")
class TestActivityMetrics:
    """Test suite for ActivityMetrics."""

    @allure.story("Metric Updates")
    @allure.severity(severity_level.NORMAL)
    def test_basic_increment(self):
        """Test basic metric incrementing."""
        with allure.step("Create metrics and increment"):
            metrics = ActivityMetrics()
            initial_time = metrics.last_activity
            
            time.sleep(0.01)  # Small delay
            metrics.increment()
            
        with allure.step("Verify increment"):
            assert metrics.count == 1
            assert metrics.errors == 0
            assert metrics.rate_limits == 0
            assert metrics.last_activity > initial_time

    @allure.story("Error Tracking")
    @allure.severity(severity_level.NORMAL)
    def test_error_increment(self):
        """Test error tracking in metrics."""
        with allure.step("Increment with error flag"):
            metrics = ActivityMetrics()
            metrics.increment(error=True)
            
        with allure.step("Verify error tracking"):
            assert metrics.count == 1
            assert metrics.errors == 1

    @allure.story("Rate Limit Tracking")
    @allure.severity(severity_level.NORMAL)
    def test_rate_limit_increment(self):
        """Test rate limit tracking in metrics."""
        with allure.step("Increment with rate limit flag"):
            metrics = ActivityMetrics()
            metrics.increment(rate_limit=True)
            
        with allure.step("Verify rate limit tracking"):
            assert metrics.count == 1
            assert metrics.rate_limits == 1


@allure.epic("Debugging Monitor")
@allure.feature("Integration Tests")
class TestIntegration:
    """Integration tests for the debugging monitor."""

    @allure.story("High Volume Scenario")
    @allure.severity(severity_level.CRITICAL)
    def test_high_volume_requests(self):
        """Test monitor behavior under high volume."""
        config = MonitorConfig(
            enabled=True,
            adaptive_sampling=True,
            high_volume_threshold=50,
            log_interval=1
        )
        
        with allure.step("Process high volume requests"):
            with DebuggingMonitor(config) as monitor:
                # Simulate high volume
                for i in range(100):
                    monitor.log_http_request("GET", f"/api/item/{i}", 200)
                    if i % 10 == 0:
                        monitor.log_queue_activity("processing", "put", i)
                        
        with allure.step("Verify adaptive sampling kicked in"):
            assert monitor.sampler.sample_rate < 1.0

    @allure.story("Mixed Operations")
    @allure.severity(severity_level.NORMAL)
    def test_mixed_operations(self):
        """Test various operations working together."""
        config = MonitorConfig(enabled=True, log_interval=1)
        
        with allure.step("Execute mixed operations"):
            with DebuggingMonitor(config) as monitor:
                # HTTP requests
                monitor.log_http_request("GET", "/api/users", 200)
                monitor.log_http_request("POST", "/api/users", 429)
                
                # Database operations
                monitor.log_db_operation("SELECT", 0.05)
                monitor.log_db_operation("UPDATE", 0.2)  # Slow operation
                
                # Queue activity
                monitor.log_queue_activity("jobs", "put", 5)
                
                # Task operations
                with monitor.operation_context("data_processing"):
                    time.sleep(0.02)
                    
        with allure.step("Verify all metrics were recorded"):
            assert 'http' in monitor.metrics
            assert 'db' in monitor.metrics
            assert 'queue_jobs' in monitor.metrics
            assert 'task_data_processing' in monitor.metrics
            
            # Verify summary
            summary = monitor.get_metrics_summary()
            assert len(summary) >= 4

    @allure.story("Async Context Manager")
    @allure.severity(severity_level.NORMAL)
    @pytest.mark.asyncio
    async def test_async_context_manager(self):
        """Test async context manager functionality."""
        config = MonitorConfig(enabled=True)
        monitor = DebuggingMonitor(config)
        
        with allure.step("Use async context manager"):
            async with monitor.async_context():
                monitor.log_http_request("GET", "/async/test", 200)
                await asyncio.sleep(0.01)
                
        with allure.step("Verify monitoring was active"):
            assert 'http' in monitor.metrics


@allure.epic("Debugging Monitor")
@allure.feature("Error Handling")
class TestErrorHandling:
    """Test error handling and edge cases."""

    @allure.story("Disabled Monitor")
    @allure.severity(severity_level.NORMAL)
    def test_disabled_monitor_operations(self):
        """Test that disabled monitor doesn't perform operations."""
        config = MonitorConfig(enabled=False)
        monitor = DebuggingMonitor(config)
        
        with allure.step("Perform operations on disabled monitor"):
            monitor.log_http_request("GET", "/test", 200)
            monitor.log_queue_activity("test", "put", 10)
            monitor.log_db_operation("SELECT", 1.0)
            
        with allure.step("Verify no metrics were recorded"):
            assert len(monitor.metrics) == 0

    @allure.story("Memory Info Fallback")
    @allure.severity(severity_level.NORMAL)
    def test_memory_info_without_psutil(self, monitor):
        """Test memory info handling when psutil is not available."""
        with allure.step("Mock psutil import error"):
            with patch('builtins.__import__', side_effect=ImportError("No module named 'psutil'")):
                memory_info = monitor._get_memory_info()
                
        with allure.step("Verify graceful fallback"):
            assert "error" in memory_info
            assert "psutil not available" in memory_info["error"]

    @allure.story("Stack Trace Handling")
    @allure.severity(severity_level.NORMAL)
    def test_stack_trace_dump(self, monitor, caplog):
        """Test stack trace dumping functionality."""
        with allure.step("Trigger stack trace dump"):
            monitor.dump_stack_traces()
            
        with allure.step("Verify stack trace was logged"):
            # Check that some stack trace related logging occurred
            # The exact content depends on the current thread state
            pass  # Stack traces are logged, hard to assert specific content


# Utility functions for test setup
@pytest.fixture(scope="session", autouse=True)
def setup_allure_environment():
    """Set up Allure environment information."""
    
    # This would typically be done by pytest-allure plugin
    # but we can add environment info programmatically
    allure.dynamic.description("""
    Comprehensive test suite for the DebuggingMonitor class.
    Tests cover core functionality, context managers, adaptive sampling,
    and high-volume scenarios.
    """)


# Performance benchmarks (optional)
@allure.epic("Debugging Monitor")
@allure.feature("Performance")
class TestPerformance:
    """Performance tests for the debugging monitor."""

    @allure.story("Logging Performance")
    @allure.severity(severity_level.MINOR)
    def test_logging_performance(self):
        """Test logging performance under load."""
        config = MonitorConfig(
            enabled=True,
            adaptive_sampling=True,
            high_volume_threshold=1000
        )
        
        with allure.step("Measure logging performance"):
            start_time = time.time()
            
            with DebuggingMonitor(config) as monitor:
                # Log many operations quickly
                for i in range(1000):
                    monitor.log_http_request("GET", f"/perf/test/{i}", 200)
                    
            end_time = time.time()
            duration = end_time - start_time
            
        with allure.step("Verify performance is acceptable"):
            # Should be able to log 1000 operations in under 1 second
            assert duration < 1.0, f"Logging took {duration:.3f}s, expected < 1.0s"
            
            # Attach performance metrics to allure report
            allure.attach(
                f"Duration: {duration:.3f}s\nRate: {1000/duration:.1f} ops/sec",
                name="Performance Metrics",
                attachment_type=allure.attachment_type.TEXT
            )


if __name__ == "__main__":
    # Run tests with Allure reporting
    pytest.main([
        __file__,
        "--alluredir=allure-results",
        "-v",
        "--tb=short"
    ])
