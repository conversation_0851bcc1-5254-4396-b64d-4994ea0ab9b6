"""
Test for enhanced type conversion error handling.
"""
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock

from dags.data_pipeline.data_type_handlers import RobustDataTypeHandler


def test_storypoints_conversion_with_problematic_data():
    """Test storypoints conversion with various problematic data types."""
    
    # Create test data with problematic values
    df = pd.DataFrame({
        'key': ['PROJ-1', 'PROJ-2', 'PROJ-3', 'PROJ-4', 'PROJ-5'],
        'storypoints': [1, '2.0', 'invalid', None, '']
    })
    
    handler = RobustDataTypeHandler()
    mock_logger = Mock()
    
    # Test conversion
    df_result, results = handler.safe_astype_conversion(
        df, {'storypoints': 'Int64'}, mock_logger
    )
    
    # Check that conversion completed without crashing
    assert len(results) == 1
    result = results[0]
    assert result.column == 'storypoints'
    assert result.target_type == 'Int64'
    
    # Check that logger was called with debugging information
    mock_logger.debug.assert_called()
    mock_logger.warning.assert_called()
    
    # Verify the converted data
    assert df_result['storypoints'].dtype == 'Int64'
    assert df_result['storypoints'].iloc[0] == 1  # Should convert successfully
    assert df_result['storypoints'].iloc[1] == 2  # Should convert '2.0' to 2
    assert pd.isna(df_result['storypoints'].iloc[2])  # 'invalid' should become NaN
    assert pd.isna(df_result['storypoints'].iloc[3])  # None should stay NaN
    assert pd.isna(df_result['storypoints'].iloc[4])  # Empty string should become NaN


def test_enhanced_debugging_for_special_columns():
    """Test that enhanced debugging is triggered for special columns."""
    
    df = pd.DataFrame({
        'key': ['PROJ-1', 'PROJ-2'],
        'storypoints': ['abc', 'def'],  # Non-numeric values
        'id': [1, 2],
        'parent_id': [None, 1]
    })
    
    handler = RobustDataTypeHandler()
    mock_logger = Mock()
    
    # Test conversion of storypoints (should trigger enhanced debugging)
    df_result, results = handler.safe_astype_conversion(
        df, {'storypoints': 'Int64'}, mock_logger
    )
    
    # Verify enhanced debugging was called
    debug_calls = [call.args[0] for call in mock_logger.debug.call_args_list]
    warning_calls = [call.args[0] for call in mock_logger.warning.call_args_list]
    
    # Should have debug messages about the conversion
    assert any("Converting column 'storypoints'" in msg for msg in debug_calls)
    assert any("Total rows:" in msg for msg in debug_calls)
    assert any("Sample values:" in msg for msg in debug_calls)
    
    # Should have warnings about non-numeric values
    assert any("Non-numeric values found" in msg for msg in warning_calls)
    assert any("Issue keys with problematic" in msg for msg in warning_calls)


def test_conversion_result_details():
    """Test that conversion results provide detailed information."""
    
    df = pd.DataFrame({
        'key': ['PROJ-1', 'PROJ-2', 'PROJ-3'],
        'storypoints': [1, 'invalid', 3]
    })
    
    handler = RobustDataTypeHandler()
    mock_logger = Mock()
    
    df_result, results = handler.safe_astype_conversion(
        df, {'storypoints': 'Int64'}, mock_logger
    )
    
    assert len(results) == 1
    result = results[0]
    
    # Check result details
    assert result.success is True  # Should succeed even with some invalid data
    assert result.column == 'storypoints'
    assert result.original_type == 'object'
    assert result.target_type == 'Int64'
    assert result.rows_affected >= 0
    assert result.rows_failed >= 0


def test_error_handling_with_issue_keys():
    """Test that error handling includes issue key information when available."""
    
    df = pd.DataFrame({
        'key': ['PROJ-1', 'PROJ-2'],
        'storypoints': ['completely_invalid', 'also_invalid']
    })
    
    handler = RobustDataTypeHandler()
    mock_logger = Mock()
    
    # This should trigger the enhanced error handling
    df_result, results = handler.safe_astype_conversion(
        df, {'storypoints': 'Int64'}, mock_logger
    )
    
    # Check that warning messages include issue keys
    warning_calls = [call.args[0] for call in mock_logger.warning.call_args_list]
    
    # Should mention issue keys in warnings
    assert any("Issue keys with" in msg for msg in warning_calls)


def test_multiple_column_conversion():
    """Test conversion of multiple columns with mixed success."""
    
    df = pd.DataFrame({
        'key': ['PROJ-1', 'PROJ-2'],
        'storypoints': [1, 2],  # Should succeed
        'id': ['abc', 'def'],   # Should fail
        'parent_id': [1, None]  # Should succeed
    })
    
    handler = RobustDataTypeHandler()
    mock_logger = Mock()
    
    type_mapping = {
        'storypoints': 'Int64',
        'id': 'Int64', 
        'parent_id': 'Int64'
    }
    
    df_result, results = handler.safe_astype_conversion(df, type_mapping, mock_logger)
    
    # Should have results for all three columns
    assert len(results) == 3
    
    # Check individual results
    storypoints_result = next(r for r in results if r.column == 'storypoints')
    id_result = next(r for r in results if r.column == 'id')
    parent_id_result = next(r for r in results if r.column == 'parent_id')
    
    assert storypoints_result.success is True
    assert id_result.success is True  # Should succeed with cleaning
    assert parent_id_result.success is True


if __name__ == "__main__":
    test_storypoints_conversion_with_problematic_data()
    test_enhanced_debugging_for_special_columns()
    test_conversion_result_details()
    test_error_handling_with_issue_keys()
    test_multiple_column_conversion()
    print("✅ All type conversion enhancement tests passed!")
