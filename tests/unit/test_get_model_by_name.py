"""
Test cases for get_model_by_name function
"""
import pytest
import allure
from unittest.mock import Mock, patch

from sqlalchemy import MetaData, Table, Column, Integer, String

from dags.data_pipeline.database.get_model_by_name import get_model_by_name
from dags.data_pipeline.dbmodels.base import Base


@allure.epic("Database Operations")
@allure.feature("Model Retrieval")
class TestGetModelByName:

    @allure.story("Error handling")
    @allure.title("Should raise ValueError for non-existent model")
    def test_get_model_by_name_not_found(self):
        """Test error handling for non-existent model."""
        with patch.object(Base.registry, '_class_registry') as mock_registry:
            mock_registry.items.return_value = []

            with allure.step("Attempt to retrieve non-existent model"):
                with pytest.raises(ValueError) as exc_info:
                    get_model_by_name("NonExistentModel")

            with allure.step("Verify error message"):
                assert "Model 'NonExistentModel' not found" in str(exc_info.value)

    @allure.story("Function behavior")
    @allure.title("Should handle function call without errors")
    def test_get_model_by_name_function_exists(self):
        """Test that the function exists and can be called."""
        with allure.step("Verify function exists"):
            assert callable(get_model_by_name)

        with allure.step("Verify function raises ValueError for invalid input"):
            with pytest.raises(ValueError):
                get_model_by_name("NonExistentModel123456789")

    @pytest.mark.parametrize(
        "model_name, actual_schema, input_schema, should_match",
        [
            ("Issue", "public", None, True),  # No schema passed, should match public
            ("Issue", "public", "public", True),  # Explicit match with public
            ("Issue", "public", "acq", False),  # Schema mismatch
            ("Issue", "acq", "acq", True),  # Custom schema match
            ("Issue", "acq", None, True),  # No schema passed, still match
            ("Issue", "acq", "public", False),  # Schema mismatch
            ("Unknown", "acq", "acq", False),  # Model name doesn't match
        ]
    )
    @allure.story("Schema-sensitive lookup")
    @allure.title("Should return model only if name and schema match")
    def test_model_lookup_by_schema(self, model_name, actual_schema, input_schema, should_match):
        """Parameterized test for schema-aware model retrieval."""

        # Create a simple mock class that doesn't inherit from Base to avoid SQLAlchemy machinery
        mock_table = Mock()
        mock_table.schema = actual_schema

        # Create a mock class with all the attributes your function checks
        mock_class_name = model_name if model_name != "Unknown" else "Issue"

        # Create mock class without inheriting from Base
        MockModel = Mock(spec=type)
        MockModel.__name__ = mock_class_name
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)  # Make issubclass(MockModel, Base) return True

        # Mock issubclass to return True for our mock class
        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        # Create mock weakref that returns the class
        mock_weakref = Mock()
        mock_weakref.return_value = MockModel

        # Set up registry items - only include if model exists
        if model_name == "Issue":
            registry_items = [(mock_class_name, mock_weakref)]
        else:
            registry_items = []  # For "Unknown" model test case

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            if should_match:
                with allure.step("Model should be found"):
                    model = get_model_by_name(model_name, schema=input_schema)
                    assert model.__name__ == mock_class_name
                    assert model.__table__.schema == actual_schema
            else:
                with allure.step("Model should raise ValueError"):
                    with pytest.raises(ValueError) as exc_info:
                        get_model_by_name(model_name, schema=input_schema)

                    # Verify the error message format matches your implementation
                    expected_schema_msg = f" with schema '{input_schema}'" if input_schema else ""
                    expected_msg = f"Model '{model_name}'{expected_schema_msg} not found."
                    assert str(exc_info.value) == expected_msg

    @allure.story("Edge cases")
    @allure.title("Should handle garbage collected weak references")
    def test_garbage_collected_weakref(self):
        """Test handling of None entries from garbage collected weak references."""

        # Create mock weakrefs - one returns None (garbage collected), one returns valid class
        mock_weakref_none = Mock()
        mock_weakref_none.return_value = None  # Simulates garbage collected weakref

        mock_table = Mock()
        mock_table.schema = "public"
        MockModel = Mock(spec=type)
        MockModel.__name__ = "ValidModel"
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)

        mock_weakref_valid = Mock()
        mock_weakref_valid.return_value = MockModel

        # Registry has both None and valid entries
        registry_items = [
            ("GarbageCollected", mock_weakref_none),  # This will return None
            ("ValidModel", mock_weakref_valid)  # This will return valid model
        ]

        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            with allure.step("Should skip None entries and find valid model"):
                model = get_model_by_name("ValidModel")
                assert model.__name__ == "ValidModel"

            with allure.step("Should raise error for garbage collected model"):
                with pytest.raises(ValueError):
                    get_model_by_name("GarbageCollected")

    @allure.story("Edge cases")
    @allure.title("Should handle non-callable registry entries")
    def test_non_callable_registry_entries(self):
        """Test handling of registry entries that are not callable (direct class references)."""

        # Create a mock class that's stored directly (not as weakref)
        mock_table = Mock()
        mock_table.schema = "public"
        MockModel = Mock(spec=type)
        MockModel.__name__ = "DirectModel"
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)

        # Registry entry is the class directly, not a callable weakref
        registry_items = [("DirectModel", MockModel)]

        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            with allure.step("Should handle direct class reference"):
                model = get_model_by_name("DirectModel")
                assert model.__name__ == "DirectModel"

    @allure.story("Edge cases")
    @allure.title("Should handle class instances in registry")
    def test_class_instances_in_registry(self):
        """Test when registry contains model instances instead of model classes."""

        metadata = MetaData()
        test_table = Table(
            "instance_model",
            metadata,
            Column("id", Integer, primary_key=True),
            Column("description", String, nullable=False),
            schema="public")

        class InstanceModel(Base):
            __tablename__ = "instance_model"
            __table__ = test_table

        mock_instance = InstanceModel()

        # Registry returns instance instead of class
        mock_weakref = Mock()
        mock_weakref.return_value = mock_instance
        registry_items = [("InstanceModel", mock_weakref)]

        with patch.object(Base.registry, "_class_registry") as mock_registry:
            mock_registry.items.return_value = registry_items

            with allure.step("Should extract class type from instance and return model class"):
                model = get_model_by_name("InstanceModel", schema="public")
                assert model == InstanceModel

    @allure.story("Edge cases")
    @allure.title("Should handle class instances in registry using pure mocks")
    def test_class_instances_in_registry_mock_only(self):
        """Simulate registry containing a model instance, using mocks only."""

        # Create a fake __table__ with a public schema
        mock_table = Mock()
        mock_table.schema = "public"

        # Create a mock class to simulate a SQLAlchemy model
        MockModelClass = Mock(spec=type)
        MockModelClass.__name__ = "InstanceModel"
        MockModelClass.__table__ = mock_table
        MockModelClass.__bases__ = (Base,)  # for isinstance/issubclass checks

        # Create an instance of the mock class
        mock_instance = Mock()
        mock_instance.__class__ = MockModelClass

        # Patch issubclass to recognize our mock class as a subclass of Base
        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        def mock_issubclass(cls, base_class):
            if cls is MockModelClass and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        # Prepare the mock registry with a weakref-like callable
        mock_weakref = Mock()
        mock_weakref.return_value = mock_instance
        registry_items = [("InstanceModel", mock_weakref)]

        with patch.object(Base.registry, "_class_registry") as mock_registry, \
                patch("builtins.issubclass", side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            with allure.step("Should return mock class from instance registry entry"):
                model = get_model_by_name("InstanceModel", schema="public")
                assert model is MockModelClass


    @allure.story("Edge cases")
    @allure.title("Should handle models without schema attribute")
    def test_model_without_schema_attribute(self):
        """Test handling of models where __table__ has no schema attribute."""

        # Create mock table without schema attribute
        mock_table = Mock()
        # Don't set schema attribute - it should be None when accessed with getattr

        MockModel = Mock(spec=type)
        MockModel.__name__ = "NoSchemaModel"
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)

        mock_weakref = Mock()
        mock_weakref.return_value = MockModel
        registry_items = [("NoSchemaModel", mock_weakref)]

        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            with allure.step("Should find model when no schema filter provided"):
                model = get_model_by_name("NoSchemaModel")
                assert model.__name__ == "NoSchemaModel"

            with allure.step("Should not find model when schema filter provided but model has no schema"):
                with pytest.raises(ValueError) as exc_info:
                    get_model_by_name("NoSchemaModel", schema="public")
                assert "Model 'NoSchemaModel' with schema 'public' not found." in str(exc_info.value)

    @allure.story("Edge cases")
    @allure.title("Should handle non-Base subclasses in registry")
    def test_non_base_subclasses(self):
        """Test that non-Base subclasses are ignored."""

        # Create a mock class that's not a subclass of Base
        NonBaseModel = Mock(spec=type)
        NonBaseModel.__name__ = "NonBaseModel"
        NonBaseModel.__table__ = Mock()

        # Create a valid Base subclass
        mock_table = Mock()
        mock_table.schema = "public"
        ValidModel = Mock(spec=type)
        ValidModel.__name__ = "ValidModel"
        ValidModel.__table__ = mock_table
        ValidModel.__bases__ = (Base,)

        mock_weakref_non_base = Mock()
        mock_weakref_non_base.return_value = NonBaseModel
        mock_weakref_valid = Mock()
        mock_weakref_valid.return_value = ValidModel

        registry_items = [
            ("NonBaseModel", mock_weakref_non_base),
            ("ValidModel", mock_weakref_valid)
        ]

        def mock_issubclass(cls, base_class):
            if cls is ValidModel and base_class is Base:
                return True
            if cls is NonBaseModel and base_class is Base:
                return False  # Not a Base subclass
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            with allure.step("Should ignore non-Base subclass"):
                with pytest.raises(ValueError):
                    get_model_by_name("NonBaseModel")

            with allure.step("Should find valid Base subclass"):
                model = get_model_by_name("ValidModel")
                assert model.__name__ == "ValidModel"

    @pytest.mark.parametrize(
        "model_name, use_snake_case, expected_tablename, actual_schema, input_schema, should_match",
        [
            ("IssueClassification", True, "issue_classification", "public", None, True),
            ("IssueClassification", False, "issueclassification", "public", None, True),
            ("UserAccount", True, "user_account", "acq", "acq", True),
            ("UserAccount", False, "useraccount", "acq", "acq", True),
            ("SimpleClass", True, "simple_class", "public", "public", True),
            ("SimpleClass", False, "simpleclass", "public", "public", True),
        ]
    )
    @allure.story("Snake case conversion")
    @allure.title("Should handle snake_case tablename conversion")
    def test_snake_case_tablename_conversion(self, model_name, use_snake_case, expected_tablename,
                                             actual_schema, input_schema, should_match):
        """Test snake_case conversion for model tablenames."""

        # Create a simple mock class that doesn't inherit from Base
        mock_table = Mock()
        mock_table.schema = actual_schema

        # Create mock class without inheriting from Base
        MockModel = Mock(spec=type)
        MockModel.__name__ = model_name
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)
        MockModel.use_snake_case = use_snake_case
        MockModel.__tablename__ = expected_tablename

        # Mock issubclass to return True for our mock class
        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        # Create mock weakref that returns the class
        mock_weakref = Mock()
        mock_weakref.return_value = MockModel

        registry_items = [(model_name, mock_weakref)]

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            if should_match:
                with allure.step(f"Model {model_name} should be found with tablename {expected_tablename}"):
                    model = get_model_by_name(model_name, schema=input_schema)
                    assert model.__name__ == model_name
                    assert model.__table__.schema == actual_schema
                    assert model.__tablename__ == expected_tablename
                    assert model.use_snake_case == use_snake_case

    @pytest.mark.parametrize(
        "model_name, table_args_schema, input_schema, should_match",
        [
            ("User", "public", None, True),  # No schema, should find public User
            ("User", "public", "public", True),  # Explicit public schema match
            ("User", "public", "private", False),  # Schema mismatch
            ("Admin", "admin", "admin", True),  # Custom schema match
            ("Admin", "admin", None, True),  # No schema, should find admin Admin
            ("Admin", "admin", "public", False),  # Schema mismatch
        ]
    )
    @allure.story("Table args schema")
    @allure.title("Should respect __table_args__ schema definitions")
    def test_table_args_schema_definition(self, model_name, table_args_schema, input_schema, should_match):
        """Test models with __table_args__ schema definitions."""

        # Create a simple mock class that doesn't inherit from Base
        mock_table = Mock()
        mock_table.schema = table_args_schema

        # Create mock class without inheriting from Base
        MockModel = Mock(spec=type)
        MockModel.__name__ = model_name
        MockModel.__table__ = mock_table
        MockModel.__bases__ = (Base,)
        MockModel.__table_args__ = ({'schema': table_args_schema},)

        # Mock issubclass to return True for our mock class
        def mock_issubclass(cls, base_class):
            if cls is MockModel and base_class is Base:
                return True
            return original_issubclass(cls, base_class)

        original_issubclass = __builtins__['issubclass'] if isinstance(__builtins__, dict) else __builtins__.issubclass

        # Create mock weakref that returns the class
        mock_weakref = Mock()
        mock_weakref.return_value = MockModel

        registry_items = [(model_name, mock_weakref)]

        with patch.object(Base.registry, '_class_registry') as mock_registry, \
                patch('builtins.issubclass', side_effect=mock_issubclass):
            mock_registry.items.return_value = registry_items

            if should_match:
                with allure.step(f"Model {model_name} should be found in {table_args_schema} schema"):
                    model = get_model_by_name(model_name, schema=input_schema)
                    assert model.__name__ == model_name
                    assert model.__table__.schema == table_args_schema
            else:
                with allure.step("Model should raise ValueError"):
                    with pytest.raises(ValueError) as exc_info:
                        get_model_by_name(model_name, schema=input_schema)

                    # Verify the error message format matches your implementation
                    expected_schema_msg = f" with schema '{input_schema}'" if input_schema else ""
                    expected_msg = f"Model '{model_name}'{expected_schema_msg} not found."
                    assert str(exc_info.value) == expected_msg