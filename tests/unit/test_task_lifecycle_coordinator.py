# coding=utf-8
"""
Comprehensive test suite for TaskLifecycleCoordinator.

This test suite covers:
- Task lifecycle coordination using asyncio.Condition
- Graceful shutdown of monitors when all tasks complete
- Synchronization between task completion and monitor shutdown
- Error handling and timeout scenarios
"""

import pytest
import allure
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from dataclasses import dataclass
from typing import List, Optional, Callable, Any

# We'll implement this class next
from dags.data_pipeline.utility_code import TaskInfo, TaskLifecycleCoordinator


@pytest.fixture
@allure.title("Mock Logger")
def mock_logger():
    """Fixture providing a mock logger."""
    return MagicMock()


@pytest.fixture
@allure.title("Sample Task Definitions")
def sample_task_definitions():
    """Fixture providing sample task definitions."""
    async def sample_task_1():
        await asyncio.sleep(0.1)
        return "task_1_result"
        
    async def sample_task_2():
        await asyncio.sleep(0.2)
        return "task_2_result"
        
    def sync_task():
        return "sync_task_result"
        
    return [
        TaskInfo(function=sample_task_1, args=(), kwargs={}),
        TaskInfo(function=sample_task_2, args=(), kwargs={}),
        TaskInfo(function=sync_task, args=(), kwargs={})
    ]


@allure.epic("Task Lifecycle Management")
@allure.feature("TaskLifecycleCoordinator")
class TestTaskLifecycleCoordinator:
    """Test suite for TaskLifecycleCoordinator."""
    
    @allure.story("Initialization")
    @allure.title("Should initialize with correct parameters")
    def test_initialization(self, mock_logger):
        """Test TaskLifecycleCoordinator initialization."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        assert coordinator.logger == mock_logger
        assert isinstance(coordinator._condition, asyncio.Condition)
        assert coordinator._active_tasks == set()
        assert coordinator._completed_tasks == set()
        assert coordinator._shutdown_requested is False
        assert coordinator._monitors == {}
        
    @allure.story("Task Registration")
    @allure.title("Should register and track tasks")
    @pytest.mark.asyncio
    async def test_task_registration(self, mock_logger):
        """Test task registration and tracking."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Register tasks
        task_id_1 = await coordinator.register_task("task_1")
        task_id_2 = await coordinator.register_task("task_2")
        
        assert task_id_1 in coordinator._active_tasks
        assert task_id_2 in coordinator._active_tasks
        assert len(coordinator._active_tasks) == 2
        
    @allure.story("Task Completion")
    @allure.title("Should handle task completion correctly")
    @pytest.mark.asyncio
    async def test_task_completion(self, mock_logger):
        """Test task completion handling."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Register and complete tasks
        task_id = await coordinator.register_task("test_task")
        await coordinator.mark_task_completed(task_id, "success")
        
        assert task_id not in coordinator._active_tasks
        assert task_id in coordinator._completed_tasks
        
    @allure.story("Monitor Registration")
    @allure.title("Should register and manage monitors")
    @pytest.mark.asyncio
    async def test_monitor_registration(self, mock_logger):
        """Test monitor registration and management."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Create mock monitor tasks
        monitor_task_1 = AsyncMock()
        monitor_task_2 = AsyncMock()
        
        # Register monitors
        await coordinator.register_monitor(monitor_task_1, "monitor_1")
        await coordinator.register_monitor(monitor_task_2, "monitor_2")
        
        assert len(coordinator._monitors) == 2
        assert coordinator._monitors["monitor_1"] == monitor_task_1
        assert coordinator._monitors["monitor_2"] == monitor_task_2
        # assert any(m['task'] == monitor_task_1 for m in coordinator._monitors)
        # assert any(m['task'] == monitor_task_2 for m in coordinator._monitors)
        
    @allure.story("Graceful Shutdown")
    @allure.title("Should shutdown monitors when all tasks complete")
    @pytest.mark.asyncio
    async def test_graceful_shutdown_on_completion(self, mock_logger):
        """Test graceful shutdown when all tasks are completed."""
        coordinator = TaskLifecycleCoordinator(mock_logger)

        # Create a real asyncio task that we can control
        async def mock_monitor():
            try:
                while True:
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                raise  # Let the cancellation propagate

        monitor_task = asyncio.create_task(mock_monitor())

        await coordinator.register_monitor(monitor_task, "test_monitor")

        # Register and complete all tasks
        task_id_1 = await coordinator.register_task("task_1")
        task_id_2 = await coordinator.register_task("task_2")

        await coordinator.mark_task_completed(task_id_1, "success")
        await coordinator.mark_task_completed(task_id_2, "success")

        # Wait for shutdown to be triggered
        await coordinator.wait_for_all_tasks_completion()

        # Verify monitor was cancelled
        assert monitor_task.cancelled() or monitor_task.done()
        
    @allure.story("Condition Synchronization")
    @allure.title("Should use asyncio.Condition for coordination")
    @pytest.mark.asyncio
    async def test_condition_synchronization(self, mock_logger):
        """Test asyncio.Condition usage for task coordination."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Track condition notifications
        notifications_received = []
        
        async def waiter():
            async with coordinator._condition:
                await coordinator._condition.wait()
                notifications_received.append("notified")
                
        # Start waiter
        waiter_task = asyncio.create_task(waiter())
        await asyncio.sleep(0.01)  # Let waiter start waiting
        
        # Register and complete a task (should trigger notification)
        task_id = await coordinator.register_task("test_task")
        await coordinator.mark_task_completed(task_id, "success")
        
        # Wait for notification
        await asyncio.wait_for(waiter_task, timeout=1.0)
        
        assert len(notifications_received) == 1
        
    @allure.story("Error Handling")
    @allure.title("Should handle task failures gracefully")
    @pytest.mark.asyncio
    async def test_task_failure_handling(self, mock_logger):
        """Test handling of task failures."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Register task and mark as failed
        task_id = await coordinator.register_task("failing_task")
        await coordinator.mark_task_failed(task_id, Exception("Task failed"))
        
        assert task_id not in coordinator._active_tasks
        assert task_id in coordinator._completed_tasks  # Failed tasks are still "completed"
        
    @allure.story("Timeout Handling")
    @allure.title("Should handle shutdown timeouts")
    @pytest.mark.asyncio
    async def test_shutdown_timeout(self, mock_logger):
        """Test shutdown timeout handling."""
        coordinator = TaskLifecycleCoordinator(mock_logger)

        # Create a monitor that ignores cancellation
        async def unresponsive_monitor():
            try:
                while True:
                    await asyncio.sleep(1)  # Long sleep to simulate unresponsive behavior
            except asyncio.CancelledError:
                # Ignore cancellation to simulate unresponsive behavior
                while True:
                    await asyncio.sleep(1)

        monitor_task = asyncio.create_task(unresponsive_monitor())

        await coordinator.register_monitor(monitor_task, "unresponsive_monitor")

        # Register and complete all tasks
        task_id = await coordinator.register_task("task_1")
        await coordinator.mark_task_completed(task_id, "success")

        # Test shutdown with timeout
        start_time = asyncio.get_event_loop().time()
        await coordinator.shutdown_monitors(timeout=0.1)
        end_time = asyncio.get_event_loop().time()

        # Should have timed out quickly
        assert end_time - start_time < 0.5
        # The task should have been cancelled (even if it ignores it)
        assert monitor_task.cancelled() or not monitor_task.done()
        
    @allure.story("Integration")
    @allure.title("Should coordinate multiple tasks and monitors")
    @pytest.mark.asyncio
    async def test_full_coordination_scenario(self, mock_logger, sample_task_definitions):
        """Test full coordination scenario with multiple tasks and monitors."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Create real asyncio tasks for monitors
        async def mock_monitor_1():
            try:
                while True:
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                raise

        async def mock_monitor_2():
            try:
                while True:
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                raise

        monitor_1 = asyncio.create_task(mock_monitor_1())
        monitor_2 = asyncio.create_task(mock_monitor_2())

        await coordinator.register_monitor(monitor_1, "monitor_1")
        await coordinator.register_monitor(monitor_2, "monitor_2")
        
        # Register multiple tasks
        task_ids = []
        for i, task_def in enumerate(sample_task_definitions):
            task_id = await coordinator.register_task(f"task_{i}")
            task_ids.append(task_id)
            
        # Complete tasks one by one
        for i, task_id in enumerate(task_ids):
            await coordinator.mark_task_completed(task_id, f"result_{i}")
            
        # Wait for all tasks completion
        await coordinator.wait_for_all_tasks_completion()
        
        # Verify all monitors were cancelled
        assert monitor_1.cancelled() or monitor_1.done()
        assert monitor_2.cancelled() or monitor_2.done()
        
        # Verify all tasks are completed
        assert len(coordinator._active_tasks) == 0
        assert len(coordinator._completed_tasks) == len(task_ids)


@allure.epic("Task Lifecycle Management")
@allure.feature("Integration with process_task")
class TestProcessTaskIntegration:
    """Test integration with the process_task function."""
    
    @allure.story("Monitor Coordination")
    @allure.title("Should coordinate with existing monitor tasks")
    @pytest.mark.asyncio
    async def test_monitor_coordination_integration(self, mock_logger):
        """Test integration with existing monitor and db_monitor tasks."""
        coordinator = TaskLifecycleCoordinator(mock_logger)
        
        # Simulate the monitor tasks from process_task
        async def mock_monitor():
            while not coordinator._shutdown_requested:
                await asyncio.sleep(0.1)
                
        async def mock_db_monitor():
            while not coordinator._shutdown_requested:
                await asyncio.sleep(0.1)
                
        monitor_task = asyncio.create_task(mock_monitor())
        db_monitor_task = asyncio.create_task(mock_db_monitor())
        
        # Register monitors with coordinator
        await coordinator.register_monitor(monitor_task, "monitor")
        await coordinator.register_monitor(db_monitor_task, "db_monitor")
        
        # Simulate task execution
        task_id = await coordinator.register_task("process_jira_issues")
        
        # Let monitors run briefly
        await asyncio.sleep(0.2)
        
        # Complete the task
        await coordinator.mark_task_completed(task_id, "success")
        
        # Wait for shutdown
        await coordinator.wait_for_all_tasks_completion()
        
        # Verify monitors were shut down
        assert monitor_task.cancelled() or monitor_task.done()
        assert db_monitor_task.cancelled() or db_monitor_task.done()
