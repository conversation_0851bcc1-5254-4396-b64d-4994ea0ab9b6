"""
Comprehensive tests for the refactored process_upsert_queue function.

This test suite verifies:
1. DataFrame consolidation by model name
2. Polars integration for memory efficiency
3. Memory optimization with reduce_mem_usage
4. Special handling for Issue hierarchy levels
5. Proper error handling and logging
"""

import pytest
import pandas as pd
import polars as pl
import numpy as np
from unittest.mock import AsyncMock, MagicMock, patch, call
from typing import Dict, List, Any
import asyncio
from datetime import datetime

from dags.data_pipeline.utility_code import (
    process_upsert_queue,
    _process_consolidated_dataframes,
    _process_issue_hierarchy_levels,
    _process_single_model,
    reduce_mem_usage
)


class TestRefactoredProcessUpsertQueue:
    """Test suite for the refactored process_upsert_queue function."""
    
    @pytest.fixture
    def mock_queue_container(self):
        """Mock queue container with upsert queue."""
        container = MagicMock()
        queue = AsyncMock()
        container.queue_selector.return_value = {"queue_upsert_issue": queue}
        return container
    
    @pytest.fixture
    def mock_app_container(self):
        """Mock application container with session manager."""
        container = MagicMock()
        session_manager = AsyncMock()
        container.base_session_manager_rw.return_value.update_schema.return_value = session_manager
        return container
    
    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        return MagicMock()
    
    @pytest.fixture
    def sample_issue_dataframes(self):
        """Create sample Issue DataFrames with different hierarchy levels."""
        base_data = {
            'id': [1, 2, 3, 4, 5, 6, 7, 8],
            'key': ['ISSUE-1', 'ISSUE-2', 'ISSUE-3', 'ISSUE-4', 'ISSUE-5', 'ISSUE-6', 'ISSUE-7', 'ISSUE-8'],
            'summary': ['Initiative 1', 'Epic 1', 'Story 1', 'Subtask 1', 'Initiative 2', 'Epic 2', 'Story 2', 'Subtask 2'],
            'issue_hierarchy_level': [2, 1, 0, -1, 2, 1, 0, -1],
            'status': ['Open', 'Open', 'Open', 'Open', 'Open', 'Open', 'Open', 'Open']
        }
        
        # Split into multiple DataFrames to test consolidation
        df1 = pd.DataFrame({
            'id': [1, 2, 3, 4],
            'key': ['ISSUE-1', 'ISSUE-2', 'ISSUE-3', 'ISSUE-4'],
            'summary': ['Initiative 1', 'Epic 1', 'Story 1', 'Subtask 1'],
            'issue_hierarchy_level': [2, 1, 0, -1],
            'status': ['Open', 'Open', 'Open', 'Open']
        })
        
        df2 = pd.DataFrame({
            'id': [5, 6, 7, 8],
            'key': ['ISSUE-5', 'ISSUE-6', 'ISSUE-7', 'ISSUE-8'],
            'summary': ['Initiative 2', 'Epic 2', 'Story 2', 'Subtask 2'],
            'issue_hierarchy_level': [2, 1, 0, -1],
            'status': ['Open', 'Open', 'Open', 'Open']
        })
        
        return [df1, df2]
    
    @pytest.fixture
    def sample_changelog_dataframes(self):
        """Create sample ChangelogJSON DataFrames."""
        df1 = pd.DataFrame({
            'id': [1, 2],
            'issue_id': [100, 101],
            'issue_key': ['ISSUE-100', 'ISSUE-101'],
            'created': [datetime.now(), datetime.now()],
            'author_accountId': ['user1', 'user2']
        })
        
        df2 = pd.DataFrame({
            'id': [3, 4],
            'issue_id': [102, 103],
            'issue_key': ['ISSUE-102', 'ISSUE-103'],
            'created': [datetime.now(), datetime.now()],
            'author_accountId': ['user3', 'user4']
        })
        
        return [df1, df2]
    
    @pytest.mark.asyncio
    async def test_process_consolidated_dataframes_issue_model(self, mock_logger):
        """Test consolidation and processing of Issue DataFrames with hierarchy levels."""
        # Create consolidated dataframes
        consolidated_dataframes = {
            "Issue": [
                pd.DataFrame({
                    'id': [1, 2, 3, 4],
                    'issue_hierarchy_level': [2, 1, 0, -1],
                    'key': ['ISSUE-1', 'ISSUE-2', 'ISSUE-3', 'ISSUE-4']
                }),
                pd.DataFrame({
                    'id': [5, 6, 7, 8],
                    'issue_hierarchy_level': [2, 1, 0, -1],
                    'key': ['ISSUE-5', 'ISSUE-6', 'ISSUE-7', 'ISSUE-8']
                })
            ]
        }
        
        consolidated_configs = {
            "Issue": {
                "model": MagicMock(__name__="Issue"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        with patch('dags.data_pipeline.utility_code._process_issue_hierarchy_levels') as mock_process_issue:
            await _process_consolidated_dataframes(
                consolidated_dataframes,
                consolidated_configs,
                session_manager,
                mock_logger,
                1
            )
            
            # Verify that _process_issue_hierarchy_levels was called
            mock_process_issue.assert_called_once()
            call_args = mock_process_issue.call_args
            df_passed = call_args[0][0]  # First argument is the DataFrame
            
            # Verify the consolidated DataFrame has all records
            assert len(df_passed) == 8
            assert list(df_passed['id']) == [1, 2, 3, 4, 5, 6, 7, 8]
    
    @pytest.mark.asyncio
    async def test_process_consolidated_dataframes_changelog_model(self, mock_logger):
        """Test consolidation and processing of ChangelogJSON DataFrames."""
        consolidated_dataframes = {
            "ChangelogJSON": [
                pd.DataFrame({
                    'id': [1, 2],
                    'issue_id': [100, 101],
                    'issue_key': ['ISSUE-100', 'ISSUE-101']
                }),
                pd.DataFrame({
                    'id': [3, 4],
                    'issue_id': [102, 103],
                    'issue_key': ['ISSUE-102', 'ISSUE-103']
                })
            ]
        }
        
        consolidated_configs = {
            "ChangelogJSON": {
                "model": MagicMock(__name__="ChangelogJSON"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        with patch('dags.data_pipeline.utility_code._process_single_model') as mock_process_single:
            await _process_consolidated_dataframes(
                consolidated_dataframes,
                consolidated_configs,
                session_manager,
                mock_logger,
                1
            )
            
            # Verify that _process_single_model was called
            mock_process_single.assert_called_once()
            call_args = mock_process_single.call_args
            df_passed = call_args[0][0]  # First argument is the DataFrame
            
            # Verify the consolidated DataFrame has all records
            assert len(df_passed) == 4
            assert list(df_passed['id']) == [1, 2, 3, 4]
    
    @pytest.mark.asyncio
    async def test_process_issue_hierarchy_levels(self, mock_logger):
        """Test special handling of Issue hierarchy levels."""
        df = pd.DataFrame({
            'id': [1, 2, 3, 4, 5, 6, 7, 8],
            'issue_hierarchy_level': [2, 1, 0, -1, 2, 1, 0, -1],
            'key': ['ISSUE-1', 'ISSUE-2', 'ISSUE-3', 'ISSUE-4', 'ISSUE-5', 'ISSUE-6', 'ISSUE-7', 'ISSUE-8']
        })
        
        model = MagicMock(__name__="Issue")
        config = {
            "no_update_cols": (),
            "on_conflict_update": True,
            "conflict_condition": None
        }
        
        # Create proper async context manager mock
        session_mock = AsyncMock()
        session_manager = AsyncMock()
        session_manager.async_session.return_value = session_mock
        
        with patch('dags.data_pipeline.utility_code.upsert_async') as mock_upsert:
            mock_upsert.return_value = True
            
            await _process_issue_hierarchy_levels(
                df, model, config, session_manager, mock_logger, 1
            )
            
            # Verify upsert_async was called 4 times (once for each hierarchy level)
            assert mock_upsert.call_count == 4
            
            # Verify the order: Initiative (2) -> Epic (1) -> Story (0) -> Subtask (-1)
            calls = mock_upsert.call_args_list
            
            # Check that each call has the correct number of records
            # Initiative level (2): 2 records
            assert len(calls[0][1]['rows']) == 2
            # Epic level (1): 2 records  
            assert len(calls[1][1]['rows']) == 2
            # Story level (0): 2 records
            assert len(calls[2][1]['rows']) == 2
            # Subtask level (-1): 2 records
            assert len(calls[3][1]['rows']) == 2
    
    @pytest.mark.asyncio
    async def test_process_single_model_with_sorting(self, mock_logger):
        """Test processing of single model with proper sorting."""
        df = pd.DataFrame({
            'id': [3, 1, 4, 2],
            'issue_id': [103, 101, 104, 102],
            'issue_key': ['ISSUE-103', 'ISSUE-101', 'ISSUE-104', 'ISSUE-102']
        })
        
        model = MagicMock(__name__="ChangelogJSON")
        config = {
            "no_update_cols": (),
            "on_conflict_update": True,
            "conflict_condition": None
        }
        
        # Create proper async context manager mock
        session_mock = AsyncMock()
        session_manager = AsyncMock()
        session_manager.async_session.return_value = session_mock
        
        with patch('dags.data_pipeline.utility_code.upsert_async') as mock_upsert:
            mock_upsert.return_value = True
            
            await _process_single_model(
                df, model, config, session_manager, mock_logger, 1
            )
            
            # Verify upsert_async was called once
            mock_upsert.assert_called_once()
            
            # Verify the DataFrame was sorted by issue_id
            call_args = mock_upsert.call_args
            df_passed = call_args[1]['rows']
            assert list(df_passed['issue_id']) == [101, 102, 103, 104]
    
    @pytest.mark.asyncio
    async def test_memory_optimization_integration(self, mock_logger):
        """Test that memory optimization is applied during consolidation."""
        # Create DataFrames with different data types to test memory optimization
        df1 = pd.DataFrame({
            'id': [1, 2, 3],
            'large_int': [1000000, 2000000, 3000000],  # Will be optimized
            'small_int': [1, 2, 3],  # Will be optimized
            'float_col': [1.5, 2.5, 3.5],  # Will be optimized
            'category_col': ['A', 'B', 'A']  # Will be optimized
        })
        
        df2 = pd.DataFrame({
            'id': [4, 5, 6],
            'large_int': [4000000, 5000000, 6000000],
            'small_int': [4, 5, 6],
            'float_col': [4.5, 5.5, 6.5],
            'category_col': ['B', 'C', 'B']
        })
        
        consolidated_dataframes = {"TestModel": [df1, df2]}
        consolidated_configs = {
            "TestModel": {
                "model": MagicMock(__name__="TestModel"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        with patch('dags.data_pipeline.utility_code._process_single_model') as mock_process_single:
            await _process_consolidated_dataframes(
                consolidated_dataframes,
                consolidated_configs,
                session_manager,
                mock_logger,
                1
            )
            
            # Verify that memory optimization was applied
            # The reduce_mem_usage function should have been called on each DataFrame
            # and the final consolidated DataFrame should have optimized dtypes
            
            mock_process_single.assert_called_once()
            call_args = mock_process_single.call_args
            final_df = call_args[0][0]
            
            # Verify the consolidated DataFrame has all records
            assert len(final_df) == 6
            assert list(final_df['id']) == [1, 2, 3, 4, 5, 6]
    
    @pytest.mark.asyncio
    async def test_polars_integration(self, mock_logger):
        """Test that Polars is used for memory-efficient concatenation."""
        df1 = pd.DataFrame({'id': [1, 2], 'value': ['A', 'B']})
        df2 = pd.DataFrame({'id': [3, 4], 'value': ['C', 'D']})
        
        consolidated_dataframes = {"TestModel": [df1, df2]}
        consolidated_configs = {
            "TestModel": {
                "model": MagicMock(__name__="TestModel"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        with patch('dags.data_pipeline.utility_code._process_single_model') as mock_process_single:
            await _process_consolidated_dataframes(
                consolidated_dataframes,
                consolidated_configs,
                session_manager,
                mock_logger,
                1
            )
            
            # Verify that the function was called with the correct consolidated DataFrame
            mock_process_single.assert_called_once()
            call_args = mock_process_single.call_args
            final_df = call_args[0][0]
            
            # Verify the DataFrame was properly consolidated
            assert len(final_df) == 4
            assert list(final_df['id']) == [1, 2, 3, 4]
            assert list(final_df['value']) == ['A', 'B', 'C', 'D']
    
    @pytest.mark.asyncio
    async def test_error_handling_in_consolidation(self, mock_logger):
        """Test error handling during DataFrame consolidation."""
        # Create DataFrames that will cause an error during consolidation
        df1 = pd.DataFrame({'id': [1, 2], 'value': ['A', 'B']})
        df2 = pd.DataFrame({'id': [3, 4], 'value': ['C', 'D']})
        
        consolidated_dataframes = {"TestModel": [df1, df2]}
        consolidated_configs = {
            "TestModel": {
                "model": MagicMock(__name__="TestModel"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        # Mock an error during processing
        with patch('dags.data_pipeline.utility_code._process_single_model') as mock_process_single:
            mock_process_single.side_effect = Exception("Test error")
            
            with pytest.raises(Exception, match="Test error"):
                await _process_consolidated_dataframes(
                    consolidated_dataframes,
                    consolidated_configs,
                    session_manager,
                    mock_logger,
                    1
                )
            
            # Verify error was logged
            mock_logger.error.assert_called_with(
                "Error processing consolidated TestModel: Test error"
            )
    
    @pytest.mark.asyncio
    async def test_empty_dataframes_handling(self, mock_logger):
        """Test handling of empty DataFrames during consolidation."""
        consolidated_dataframes = {"TestModel": []}
        consolidated_configs = {
            "TestModel": {
                "model": MagicMock(__name__="TestModel"),
                "no_update_cols": (),
                "on_conflict_update": True,
                "conflict_condition": None
            }
        }
        
        session_manager = AsyncMock()
        
        # Should not raise an exception for empty DataFrames
        await _process_consolidated_dataframes(
            consolidated_dataframes,
            consolidated_configs,
            session_manager,
            mock_logger,
            1
        )
        
        # Verify no processing was attempted
        # (The function should skip empty DataFrames)
    
    def test_reduce_mem_usage_optimization(self):
        """Test that reduce_mem_usage properly optimizes memory usage."""
        # Create a DataFrame with various data types
        df = pd.DataFrame({
            'large_int': [1000000, 2000000, 3000000],
            'small_int': [1, 2, 3],
            'float_col': [1.5, 2.5, 3.5],
            'category_col': ['A', 'B', 'A'],
            'object_col': ['text1', 'text2', 'text3']
        })
        
        # Get original memory usage
        original_memory = df.memory_usage(deep=True).sum()
        
        # Apply memory optimization
        optimized_df = reduce_mem_usage(df, None)
        
        # Get optimized memory usage
        optimized_memory = optimized_df.memory_usage(deep=True).sum()
        
        # Verify memory usage was reduced
        assert optimized_memory < original_memory
        
        # Verify data integrity
        assert len(optimized_df) == len(df)
        assert list(optimized_df['large_int']) == list(df['large_int'])
        assert list(optimized_df['small_int']) == list(df['small_int'])
    
    @pytest.mark.asyncio
    async def test_upsert_failure_handling(self, mock_logger):
        """Test handling of upsert failures."""
        df = pd.DataFrame({'id': [1, 2], 'value': ['A', 'B']})
        model = MagicMock(__name__="TestModel")
        config = {
            "no_update_cols": (),
            "on_conflict_update": True,
            "conflict_condition": None
        }
        
        # Create proper async context manager mock
        session_mock = AsyncMock()
        session_manager = AsyncMock()
        session_manager.async_session.return_value = session_mock
        
        with patch('dags.data_pipeline.utility_code.upsert_async') as mock_upsert:
            mock_upsert.return_value = False  # Simulate upsert failure
            
            with pytest.raises(Exception, match="Upsert failed for TestModel"):
                await _process_single_model(
                    df, model, config, session_manager, mock_logger, 1
                )
            
            # Verify error was logged
            mock_logger.error.assert_called_with(
                "Upsert Failed for TestModel batch=1, records = 2"
            )


class TestMemoryOptimization:
    """Test suite for memory optimization functionality."""
    
    def test_reduce_mem_usage_int_optimization(self):
        """Test memory optimization for integer columns."""
        df = pd.DataFrame({
            'int64_col': [1, 2, 3, 4, 5],
            'large_int': [1000000, 2000000, 3000000, 4000000, 5000000]
        })
        
        optimized_df = reduce_mem_usage(df, None)
        
        # Verify smaller integers were optimized
        # Check the actual dtype name instead of string representation
        assert str(optimized_df['int64_col'].dtype) in ['Int8', 'Int16', 'Int32', 'int8', 'int16', 'int32']
        
        # Verify large integers were optimized appropriately
        assert str(optimized_df['large_int'].dtype) in ['Int32', 'Int64', 'int32', 'int64']
    
    def test_reduce_mem_usage_float_optimization(self):
        """Test memory optimization for float columns."""
        df = pd.DataFrame({
            'float64_col': [1.5, 2.5, 3.5, 4.5, 5.5],
            'small_float': [1.1, 2.1, 3.1, 4.1, 5.1]
        })
        
        optimized_df = reduce_mem_usage(df, None)
        
        # Verify floats were optimized to float32 where appropriate
        assert str(optimized_df['small_float'].dtype) == 'float32'
    
    def test_reduce_mem_usage_category_optimization(self):
        """Test memory optimization for categorical columns."""
        df = pd.DataFrame({
            'category_col': ['A', 'B', 'A', 'B', 'A', 'B', 'A', 'B']
        })
        
        optimized_df = reduce_mem_usage(df, None)
        
        # Verify categorical optimization was applied
        assert str(optimized_df['category_col'].dtype) == 'category'
    
    def test_reduce_mem_usage_preserves_data(self):
        """Test that memory optimization preserves data integrity."""
        original_data = {
            'int_col': [1, 2, 3, 4, 5],
            'float_col': [1.5, 2.5, 3.5, 4.5, 5.5],
            'string_col': ['A', 'B', 'C', 'D', 'E'],
            'category_col': ['X', 'Y', 'X', 'Y', 'X']
        }
        
        df = pd.DataFrame(original_data)
        optimized_df = reduce_mem_usage(df, None)
        
        # Verify all data is preserved
        for col in original_data:
            assert list(optimized_df[col]) == original_data[col]


class TestPolarsIntegration:
    """Test suite for Polars integration."""
    
    def test_pandas_to_polars_conversion(self):
        """Test conversion from pandas to polars DataFrame."""
        pandas_df = pd.DataFrame({
            'id': [1, 2, 3],
            'value': ['A', 'B', 'C'],
            'number': [1.5, 2.5, 3.5]
        })
        
        # Convert to polars
        polars_df = pl.from_pandas(pandas_df)
        
        # Verify conversion
        assert isinstance(polars_df, pl.DataFrame)
        assert polars_df.height == 3
        assert polars_df.width == 3
    
    def test_polars_concat_memory_efficiency(self):
        """Test that polars concatenation is memory efficient."""
        df1 = pd.DataFrame({'id': [1, 2], 'value': ['A', 'B']})
        df2 = pd.DataFrame({'id': [3, 4], 'value': ['C', 'D']})
        
        # Convert to polars
        pl_df1 = pl.from_pandas(df1)
        pl_df2 = pl.from_pandas(df2)
        
        # Concatenate using polars
        concatenated = pl.concat([pl_df1, pl_df2], how="vertical")
        
        # Verify concatenation
        assert concatenated.height == 4
        assert concatenated.width == 2
        assert list(concatenated['id']) == [1, 2, 3, 4]
    
    def test_polars_to_pandas_conversion(self):
        """Test conversion back from polars to pandas DataFrame."""
        polars_df = pl.DataFrame({
            'id': [1, 2, 3],
            'value': ['A', 'B', 'C']
        })
        
        # Convert back to pandas
        pandas_df = polars_df.to_pandas()
        
        # Verify conversion
        assert isinstance(pandas_df, pd.DataFrame)
        assert len(pandas_df) == 3
        assert list(pandas_df['id']) == [1, 2, 3] 