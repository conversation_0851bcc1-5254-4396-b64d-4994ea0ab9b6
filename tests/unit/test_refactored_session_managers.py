# coding=utf-8
"""
Comprehensive test suite for refactored session manager classes.

This test suite covers:
- BaseSessionManager and AsyncSessionManager functionality
- ConnectionRecoveryMixin, LifecycleManagementMixin, SchemaManagementMixin
- RefactoredPostgresSessionManager, EnhancedSessionManager, ManagedSessionManager
- FullyEnhancedSessionManager with all capabilities
- Error handling, retry logic, and cleanup scenarios
"""

import pytest
import allure
import asyncio

from unittest.mock import MagicMock, patch, AsyncMock
from contextlib import asynccontextmanager

from dags.data_pipeline.containers import (
    BaseSessionManager,
    AsyncSessionManager,
    ConnectionRecoveryMixin,
    SchemaManagementMixin,
    RefactoredPostgresSessionManager,
    EnhancedSessionManager,
    ManagedSessionManager,
    FullyEnhancedSessionManager,
    EntryDetails
)


@pytest.fixture
@allure.title("Mock Database Entry Details")
def mock_entry_details():
    """Fixture providing mock database entry details."""
    return EntryDetails(
        username="test_user",
        password="test_password",
        url="postgresql://test_user:test_password@localhost:5432/testdb",
        custom_properties={
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "testdb"
        }
    )


@pytest.fixture
@allure.title("Mock Logger")
def mock_logger():
    """Fixture providing a mock logger."""
    return MagicMock()


@allure.epic("Session Manager Refactoring")
@allure.feature("Base Classes")
class TestBaseSessionManager:
    """Test suite for BaseSessionManager abstract class."""
    
    @allure.story("Abstract Interface")
    @allure.title("Should enforce abstract methods")
    def test_base_session_manager_abstract_methods(self, mock_entry_details, mock_logger):
        """Test that BaseSessionManager enforces abstract method implementation."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            BaseSessionManager(mock_entry_details, "test_schema", True, mock_logger)


@allure.epic("Session Manager Refactoring")
@allure.feature("Async Session Manager")
class TestAsyncSessionManager:
    """Test suite for AsyncSessionManager base class."""
    
    @allure.story("Initialization")
    @allure.title("Should initialize with correct parameters")
    @patch('dags.data_pipeline.containers.create_async_engine')
    def test_async_session_manager_initialization(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test AsyncSessionManager initialization."""
        mock_engine = MagicMock()
        mock_engine_with_options = MagicMock()
        mock_engine.execution_options.return_value = mock_engine_with_options
        mock_create_engine.return_value = mock_engine

        manager = AsyncSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        assert manager.entry == mock_entry_details
        assert manager.schema == "test_schema"
        assert manager.rw is True
        assert manager.logger == mock_logger
        assert manager.closed is False
        assert manager.engine_async == mock_engine_with_options  # Should be the engine with options

        # Verify engine creation was called with correct parameters
        mock_create_engine.assert_called_once()
        mock_engine.execution_options.assert_called_once_with(
            schema_translate_map={None: "test_schema"}
        )
        
    @allure.story("Schema Management")
    @allure.title("Should update schema correctly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    def test_update_schema(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test schema update functionality."""
        mock_engine = MagicMock()
        mock_engine_with_initial_options = MagicMock()
        mock_engine_with_new_options = MagicMock()

        # Setup the chain of execution_options calls
        mock_engine.execution_options.return_value = mock_engine_with_initial_options
        mock_engine_with_initial_options.execution_options.return_value = mock_engine_with_new_options
        mock_create_engine.return_value = mock_engine

        manager = AsyncSessionManager(mock_entry_details, "initial_schema", True, mock_logger)

        # Update schema
        result = manager.update_schema("new_schema")

        assert result is manager  # Should return self for chaining
        assert manager.schema == "new_schema"
        assert manager.engine_async == mock_engine_with_new_options

        # Verify execution_options was called twice (initial + update)
        assert mock_engine.execution_options.call_count == 1  # Initial call
        mock_engine_with_initial_options.execution_options.assert_called_with(
            schema_translate_map={None: "new_schema"}
        )
        
    @allure.story("Error Handling")
    @allure.title("Should handle closed manager gracefully")
    @patch('dags.data_pipeline.containers.create_async_engine')
    def test_closed_manager_error_handling(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test error handling when manager is closed."""
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        manager = AsyncSessionManager(mock_entry_details, "test_schema", True, mock_logger)
        manager.closed = True
        
        # Should raise RuntimeError for operations on closed manager
        with pytest.raises(RuntimeError, match="SessionManager has been closed"):
            manager.update_schema("new_schema")
            
    @allure.story("Cleanup")
    @allure.title("Should cleanup resources properly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @pytest.mark.asyncio
    async def test_async_cleanup(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test async cleanup functionality."""
        mock_engine = MagicMock()
        mock_engine.dispose = AsyncMock()
        mock_engine_with_options = MagicMock()
        mock_engine.execution_options.return_value = mock_engine_with_options
        mock_engine_with_options.dispose = AsyncMock()
        mock_create_engine.return_value = mock_engine

        manager = AsyncSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        # Perform cleanup
        await manager.aclose()

        assert manager.closed is True
        assert manager.engine_async is None
        mock_engine_with_options.dispose.assert_called_once()


@allure.epic("Session Manager Refactoring")
@allure.feature("Connection Recovery Mixin")
class TestConnectionRecoveryMixin:
    """Test suite for ConnectionRecoveryMixin."""
    
    class TestableConnectionRecoveryMixin(ConnectionRecoveryMixin, AsyncSessionManager):
        """Testable implementation of ConnectionRecoveryMixin."""
        pass
    
    @allure.story("Error Handling")
    @allure.title("Should identify connection errors correctly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @pytest.mark.asyncio
    async def test_handle_connection_error(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test connection error identification."""
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        mixin = self.TestableConnectionRecoveryMixin(mock_entry_details, "test_schema", True, mock_logger)

        # Test connection-related errors
        from sqlalchemy.exc import InterfaceError, DisconnectionError

        # Provide realistic args for InterfaceError
        connection = MagicMock()
        cursor = MagicMock()
        error = InterfaceError(connection, cursor, "Some DB error")


        assert await mixin._handle_connection_error(InterfaceError("test", "test", "test"), "test_op") is True
        # assert await mixin._handle_connection_error(DisconnectionError("test"), "test_op") is True
        # assert await mixin._handle_connection_error(ConnectionError("test"), "test_op") is True

        # Test non-connection errors
        # assert await mixin._handle_connection_error(ValueError("test"), "test_op") is False
        
    @allure.story("Engine Recreation")
    @allure.title("Should recreate engine on connection failure")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @pytest.mark.asyncio
    async def test_recreate_async_engine(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test async engine recreation."""
        mock_old_engine = AsyncMock()
        mock_old_engine_with_options = MagicMock()
        mock_new_engine = AsyncMock()
        mock_new_engine_with_options = MagicMock()

        # Set up the chain for initial engine creation
        # mock_old_engine.execution_options.return_value = mock_old_engine_with_options
        # mock_new_engine.execution_options.return_value = mock_new_engine_with_options

        # Setup the chain for initial engine creation
        mock_old_engine.execution_options = MagicMock(return_value=mock_old_engine_with_options)
        mock_new_engine.execution_options = MagicMock(return_value=mock_new_engine_with_options)

        mock_create_engine.side_effect = [mock_old_engine, mock_new_engine]

        mixin = self.TestableConnectionRecoveryMixin(mock_entry_details, "test_schema", True, mock_logger)
        assert mixin.engine_async == mock_old_engine_with_options

        print(f"Engine before recreation: {mixin.engine_async}")

        # Recreate engine
        await mixin._recreate_async_engine()

        # Verify old engine was disposed and new one created
        mock_old_engine_with_options.dispose.assert_called_once()
        assert mock_create_engine.call_count == 2  # Initial + recreation
        assert mixin.engine_async == mock_new_engine_with_options


@allure.epic("Session Manager Refactoring")
@allure.feature("Schema Management Mixin")
class TestSchemaManagementMixin:
    """Test suite for SchemaManagementMixin."""
    
    class TestableSchemaManagementMixin(SchemaManagementMixin, AsyncSessionManager):
        """Testable implementation of SchemaManagementMixin."""
        pass
    
    @allure.story("Schema History")
    @allure.title("Should track schema changes")
    @patch('dags.data_pipeline.containers.create_async_engine')
    def test_schema_history_tracking(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test schema change history tracking."""
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        mixin = self.TestableSchemaManagementMixin(mock_entry_details, "initial_schema", True, mock_logger)
        
        # Initial schema should be in history
        assert mixin.get_schema_history() == ["initial_schema"]
        
        # Update schema
        mixin.update_schema("schema_1")
        assert mixin.get_schema_history() == ["initial_schema", "schema_1"]
        
        # Update again
        mixin.update_schema("schema_2")
        assert mixin.get_schema_history() == ["initial_schema", "schema_1", "schema_2"]
        
    @allure.story("Schema Reversion")
    @allure.title("Should revert to previous schema")
    @patch('dags.data_pipeline.containers.create_async_engine')
    def test_schema_reversion(self, mock_create_engine, mock_entry_details, mock_logger):
        """Test schema reversion functionality."""
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        mixin = self.TestableSchemaManagementMixin(mock_entry_details, "initial_schema", True, mock_logger)
        
        # Update schema
        mixin.update_schema("new_schema")
        assert mixin.schema == "new_schema"
        
        # Revert schema
        result = mixin.revert_schema()
        assert result is mixin  # Should return self for chaining
        assert mixin.schema == "initial_schema"
        
        # Try to revert when no previous schema exists
        result = mixin.revert_schema()
        assert result is mixin
        assert mixin.schema == "initial_schema"  # Should remain unchanged


@allure.epic("Session Manager Refactoring")
@allure.feature("Refactored Postgres Session Manager")
class TestRefactoredPostgresSessionManager:
    """Test suite for RefactoredPostgresSessionManager."""
    
    @allure.story("Initialization")
    @allure.title("Should initialize with both sync and async engines")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    def test_initialization(self, mock_create_sync_engine, mock_create_async_engine, mock_entry_details, mock_logger):
        """Test RefactoredPostgresSessionManager initialization."""
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()

        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = RefactoredPostgresSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        assert manager.engine == mock_sync_engine_with_options
        assert manager.engine_async == mock_async_engine_with_options
        assert manager in RefactoredPostgresSessionManager._instances
        
    @allure.story("Session Context Managers")
    @allure.title("Should provide sync session context manager")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.sessionmaker')
    def test_sync_session_context_manager(self, mock_sessionmaker, mock_create_sync_engine, 
                                         mock_create_async_engine, mock_entry_details, mock_logger):
        """Test sync session context manager."""
        mock_sync_engine = MagicMock()
        mock_async_engine = MagicMock()
        mock_session = MagicMock()
        mock_session_factory = MagicMock(return_value=mock_session)
        
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        mock_sessionmaker.return_value = mock_session_factory
        
        manager = RefactoredPostgresSessionManager(mock_entry_details, "test_schema", True, mock_logger)
        
        # Test successful session usage
        with manager.session() as session:
            assert session == mock_session
            
        mock_session.close.assert_called_once()
        
        # Test session rollback on exception
        mock_session.reset_mock()
        with pytest.raises(ValueError):
            with manager.session() as session:
                raise ValueError("Test error")
                
        mock_session.rollback.assert_called_once()
        mock_session.close.assert_called_once()

    @allure.story("Cleanup")
    @allure.title("Should cleanup all instances")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_cleanup_all_instances(self, mock_create_sync_engine, mock_create_async_engine,
                                       mock_entry_details, mock_logger):
        """Test cleanup of all instances."""
        mock_sync_engine = MagicMock()
        mock_async_engine = AsyncMock()
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        # Create multiple instances
        manager1 = RefactoredPostgresSessionManager(mock_entry_details, "schema1", True, mock_logger)
        manager2 = RefactoredPostgresSessionManager(mock_entry_details, "schema2", True, mock_logger)

        # Cleanup all instances
        await RefactoredPostgresSessionManager.cleanup_all_instances()

        # Verify both instances were closed
        assert manager1.closed is True
        assert manager2.closed is True


@allure.epic("Session Manager Refactoring")
@allure.feature("Enhanced Session Manager")
class TestEnhancedSessionManager:
    """Test suite for EnhancedSessionManager with connection recovery."""

    @allure.story("Connection Recovery")
    @allure.title("Should use retry logic for async sessions")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_async_session_with_retry(self, mock_create_sync_engine, mock_create_async_engine,
                                          mock_entry_details, mock_logger):
        """Test async session with retry logic."""
        mock_sync_engine = MagicMock()
        mock_async_engine = AsyncMock()
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = EnhancedSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        # Mock the async_session_with_retry method
        with patch.object(manager, 'async_session_with_retry') as mock_retry:
            mock_session = AsyncMock()

            @asynccontextmanager
            async def mock_session_context():
                yield mock_session

            mock_retry.return_value = mock_session_context()

            # Test that async_session uses retry logic
            async with manager.async_session() as session:
                assert session == mock_session

            mock_retry.assert_called_once()


@allure.epic("Session Manager Refactoring")
@allure.feature("Managed Session Manager")
class TestManagedSessionManager:
    """Test suite for ManagedSessionManager with lifecycle management."""

    @allure.story("Lifecycle Management")
    @allure.title("Should register for automatic cleanup")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.db_lifecycle_manager')
    def test_automatic_registration(self, mock_lifecycle_manager, mock_create_sync_engine,
                                  mock_create_async_engine, mock_entry_details, mock_logger):
        """Test automatic registration with lifecycle manager."""
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()

        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = ManagedSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        # Verify registration was called
        mock_lifecycle_manager.register_session_manager.assert_called_once_with(manager)


@allure.epic("Session Manager Refactoring")
@allure.feature("Fully Enhanced Session Manager")
class TestFullyEnhancedSessionManager:
    """Test suite for FullyEnhancedSessionManager with all capabilities."""

    @allure.story("Combined Functionality")
    @allure.title("Should combine all mixin capabilities")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @patch('dags.data_pipeline.containers.db_lifecycle_manager')
    @pytest.mark.asyncio
    async def test_combined_functionality(self, mock_lifecycle_manager, mock_create_sync_engine,
                                        mock_create_async_engine, mock_entry_details, mock_logger):
        """Test that FullyEnhancedSessionManager combines all capabilities."""
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()

        mock_async_engine = AsyncMock()
        mock_async_engine_with_options = AsyncMock()


        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options

        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = FullyEnhancedSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        # Test lifecycle management registration
        mock_lifecycle_manager.register_session_manager.assert_called_once_with(manager)

        # Test schema management capabilities
        assert hasattr(manager, 'get_schema_history')
        assert hasattr(manager, 'revert_schema')
        assert manager.get_schema_history() == ["test_schema"]

        # Test connection recovery capabilities
        assert hasattr(manager, 'async_session_with_retry')
        assert hasattr(manager, '_handle_connection_error')

        # Test that async_session uses retry logic
        with patch.object(manager, 'async_session_with_retry') as mock_retry:
            mock_session = AsyncMock()

            @asynccontextmanager
            async def mock_session_context():
                yield mock_session

            mock_retry.return_value = mock_session_context()

            async with manager.async_session() as session:
                assert session == mock_session

            mock_retry.assert_called_once()


@allure.epic("Session Manager Refactoring")
@allure.feature("Integration Tests")
class TestSessionManagerIntegration:
    """Integration tests for session manager interactions."""

    @allure.story("Error Scenarios")
    @allure.title("Should handle connection failures gracefully")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_connection_failure_handling(self, mock_create_sync_engine, mock_create_async_engine,
                                             mock_entry_details, mock_logger):
        """Test handling of connection failures in enhanced session manager."""
        from sqlalchemy.exc import InterfaceError

        mock_sync_engine = MagicMock()
        mock_async_engine = AsyncMock()
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = EnhancedSessionManager(mock_entry_details, "test_schema", True, mock_logger)

        # Mock connection error on first attempt, success on second
        connection_error = InterfaceError("Connection failed", "test", "test")

        # Create a mock session that will be yielded on success
        mock_session = AsyncMock()

        # Track call count to simulate failure then success
        call_count = 0

        @asynccontextmanager
        async def mock_async_session():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                yield AsyncMock()
                raise connection_error
            else:
                yield mock_session

        @asynccontextmanager
        async def failing_cm():
            raise connection_error
            yield

        # Second succeeds
        @asynccontextmanager
        async def success_cm():
            yield mock_session

        # Patch the parent async_session method
        with patch.object(AsyncSessionManager, 'async_session', side_effect=[failing_cm(), success_cm()]):
            # Test retry logic
            with patch.object(manager, '_handle_connection_error', return_value=True) as mock_handle_error:
                with patch.object(manager, '_recreate_async_engine') as mock_recreate:
                    with patch('asyncio.sleep') as mock_sleep:
                        async with manager.async_session_with_retry() as session:
                            assert session == mock_session

                        # Verify retry logic was executed
                        mock_handle_error.assert_called_once_with(connection_error, "session_creation_attempt_1")
                        mock_recreate.assert_called_once()
                        mock_sleep.assert_called_once()

    @allure.story("Performance")
    @allure.title("Should handle concurrent session creation")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_concurrent_session_creation(self, mock_create_sync_engine, mock_create_async_engine,
                                             mock_entry_details, mock_logger):
        """Test concurrent session creation and cleanup."""
        import tracemalloc
        tracemalloc.start()

        mock_sync_engine = MagicMock()
        mock_async_engine = AsyncMock()
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        manager = RefactoredPostgresSessionManager(mock_entry_details, "test_schema", True, mock_logger)
        # Optional: Take snapshot before
        snapshot1 = tracemalloc.take_snapshot()

        # Create multiple concurrent sessions
        async def create_session(session_id):
            with patch('dags.data_pipeline.containers.sessionmaker') as mock_sessionmaker:
                mock_session = MagicMock()
                mock_session_factory = MagicMock(return_value=mock_session)
                mock_sessionmaker.return_value = mock_session_factory

                with manager.session() as session:
                    await asyncio.sleep(0.01)  # Simulate some work
                    return f"session_{session_id}"

        # Run concurrent sessions
        tasks = [create_session(i) for i in range(5)]
        results = await asyncio.gather(*tasks)
        # Optional: Take snapshot after
        snapshot2 = tracemalloc.take_snapshot()
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')

        print("\n[ Top 10 memory differences after concurrent session creation ]")
        for stat in top_stats[:10]:
            print(stat)

        # Verify all sessions completed successfully
        assert len(results) == 5
        assert all(result.startswith("session_") for result in results)

