"""
Test for dataframe_utils logging fixes to prevent multiple debugger_*.log files.
"""
import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, Mock

from dags.data_pipeline.dataframe_utils.dataframe_debugger import AsyncDataframeDebugger


@pytest.mark.asyncio
async def test_shared_logging_prevents_multiple_files():
    """Test that AsyncDataframeDebug<PERSON> uses shared logging instead of creating multiple files."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create multiple debugger instances
        temp_dir = Path("c:/vishal/log")
        debugger1 = AsyncDataframeDebugger(max_workers=1)
        debugger2 = AsyncDataframeDebugger(max_workers=1)
        debugger3 = AsyncDataframeDebugger(max_workers=1)
        
        # Create sample dataframes
        df1 = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
        df2 = pd.DataFrame({'x': [7, 8, 9], 'y': [10, 11, 12]})
        
        try:
            # Save files using different debugger instances
            await debugger1.save_dataframe(df1, "test1.csv", path=temp_dir)
            await debugger2.save_dataframe(df2, "test2.csv", path=temp_dir)
            
            # Wait for completion
            await debugger1.wait_for_completion()
            await debugger2.wait_for_completion()
            
            # Check that files were created
            assert (Path(temp_dir) / "test1.csv").exists()
            assert (Path(temp_dir) / "test2.csv").exists()
            
            # Check that no individual debugger_*.log files were created in temp_dir
            log_files = list(Path(temp_dir).glob("debugger_*.log"))
            assert len(log_files) == 0, f"Found unexpected log files: {log_files}"
            
        finally:
            # Cleanup
            await debugger1.cleanup()
            await debugger2.cleanup()
            await debugger3.cleanup()



@pytest.mark.asyncio
async def test_no_file_descriptor_leaks():
    """Test that creating multiple debugger instances doesn't create file descriptor leaks."""
    
    debuggers = []
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create multiple debugger instances
            for i in range(5):
                debugger = AsyncDataframeDebugger(max_workers=1, path=temp_dir)
                debuggers.append(debugger)

                # Get logger to trigger handler creation
                logger = debugger._get_logger()
                assert logger is not None

            # All debuggers should be functional
            df = pd.DataFrame({'test': [1, 2, 3]})

            for i, debugger in enumerate(debuggers):
                await debugger.save_dataframe(df, f"test_{i}.csv")

            # Wait for all to complete
            for debugger in debuggers:
                await debugger.wait_for_completion()

            # Verify files were created
            for i in range(5):
                assert (Path(temp_dir) / f"test_{i}.csv").exists()

    finally:
        # Cleanup all debuggers
        for debugger in debuggers:
            await debugger.cleanup()


if __name__ == "__main__":
    import asyncio
    
    async def run_tests():
        await test_shared_logging_prevents_multiple_files()
        await test_logger_container_integration()
        await test_fallback_logging_uses_shared_file()
        await test_no_file_descriptor_leaks()
        print("✅ All dataframe utils logging tests passed!")
    
    asyncio.run(run_tests())
