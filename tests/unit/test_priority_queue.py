# test_priority_queue_allure.py

import asyncio
import time
import random
from dataclasses import dataclass, field
from enum import IntEnum
from typing import Any
from collections import Counter
from asyncio import PriorityQueue
import matplotlib.pyplot as plt
from io import BytesIO
import pytest
import allure

# ---------- Configuration ----------
BASE_ISSUES_PER_GROUP = 1000
GROUPS = 5
MAX_WORKLOGS = 5000
MAX_CHANGELOGS = 300
MAX_ISSUELINKS = 10
MAX_COMMENTS = 200
MAX_INITIATIVES_PER_GROUP = 5
# ----------------------------------

class MessageType(IntEnum):
    ISSUE_INITIATIVE = 0
    ISSUE_DIRECT = 1
    WORKLOG = 2
    CHANGELOG = 3
    COMMENTS = 4
    ISSUELINKS = 5
    SENTINEL = 999


@dataclass(order=True)
class PriorityMessage:
    priority: int = field(init=False)
    message_type_priority: int
    task_id: int = field(compare=False)
    counter: int = field(compare=False)
    data: Any = field(compare=False)
    enqueue_time: float = field(default_factory=time.time, compare=False)

    def __post_init__(self):
        if self.message_type_priority == MessageType.SENTINEL:
            self.priority = (1 << 63)
        else:
            self.priority = (
                (self.message_type_priority << 32) +
                (self.task_id << 16) +
                self.counter
            )


class PriorityQueueManager:
    def __init__(self):
        self._counters = {}

    def _get_next_counter(self, task_id: int) -> int:
        self._counters.setdefault(task_id, 0)
        val = self._counters[task_id]
        self._counters[task_id] += 1
        return val

    def create_priority_message(self, data: Any, message_type: MessageType, task_id: int) -> PriorityMessage:
        counter = self._get_next_counter(task_id)
        return PriorityMessage(
            message_type_priority=message_type.value,
            task_id=task_id,
            counter=counter,
            data=data
        )

    async def put_priority_message(self, queue: PriorityQueue, data: Any, message_type: MessageType, task_id: int):
        message = self.create_priority_message(data, message_type, task_id)
        await queue.put(message)


async def simulate_issue_group(group_id: int, queue: PriorityQueue, manager: PriorityQueueManager, use_unique_task_ids: bool):
    task_base = group_id * 10 if use_unique_task_ids else 0
    num_issues = int(BASE_ISSUES_PER_GROUP * random.uniform(0.9, 1.1))

    for i in range(num_issues):
        issue_id = f"ISSUE-{group_id}-{i}"
        issue_task_id = task_base + 1
        await manager.put_priority_message(
            queue,
            data={"source": "Issue", "group_id": group_id, "issue_id": issue_id},
            message_type=MessageType.ISSUE_DIRECT,
            task_id=issue_task_id
        )

        for _ in range(random.randint(0, 5)):
            await manager.put_priority_message(
                queue,
                data={"source": "Initiative", "group_id": group_id, "issue_id": issue_id},
                message_type=MessageType.ISSUE_INITIATIVE,
                task_id=task_base + 2
            )

        for _ in range(random.randint(0, MAX_WORKLOGS // num_issues)):
            await manager.put_priority_message(
                queue,
                data={"source": "Worklog", "group_id": group_id, "issue_id": issue_id},
                message_type=MessageType.WORKLOG,
                task_id=task_base + 3
            )

        for _ in range(random.randint(0, MAX_CHANGELOGS // num_issues)):
            await manager.put_priority_message(
                queue,
                data={"source": "Changelog", "group_id": group_id, "issue_id": issue_id},
                message_type=MessageType.CHANGELOG,
                task_id=task_base + 4
            )

        for _ in range(random.randint(0, MAX_ISSUELINKS // num_issues)):
            await manager.put_priority_message(
                queue,
                data={"source": "IssueLink", "group_id": group_id, "issue_id": issue_id},
                message_type=MessageType.ISSUELINKS,
                task_id=task_base + 5
            )

        for _ in range(random.randint(0, MAX_COMMENTS // num_issues)):
            await manager.put_priority_message(
                queue,
                data={"source": "Comment", "group_id": group_id, "issue_id": issue_id},
                message_type=MessageType.COMMENTS,
                task_id=task_base + 6
            )

    await manager.put_priority_message(
        queue,
        data={"source": "END", "group_id": group_id},
        message_type=MessageType.SENTINEL,
        task_id=task_base + 9
    )


async def run_priority_queue_test(use_unique_task_ids: bool = True):
    queue = PriorityQueue()
    manager = PriorityQueueManager()
    results = []

    tasks = [
        simulate_issue_group(gid, queue, manager, use_unique_task_ids)
        for gid in range(GROUPS)
    ]
    await asyncio.gather(*tasks)

    while not queue.empty():
        msg: PriorityMessage = await queue.get()
        dequeue_time = time.time()
        results.append({
            "priority": msg.priority,
            "type": msg.message_type_priority,
            "task_id": msg.task_id,
            "source": msg.data["source"],
            "group": msg.data["group_id"],
            "enqueue_time": msg.enqueue_time,
            "dequeue_time": dequeue_time,
            "delay_ms": round((dequeue_time - msg.enqueue_time) * 1000, 2),
        })

    return results


def plot_combined_scatter(results):
    type_names = {
        0: "Initiative", 1: "Issue", 2: "Worklog", 3: "Changelog",
        4: "Comment", 5: "IssueLink", 999: "Sentinel"
    }

    import matplotlib.pyplot as plt
    fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True)

    # Scatter by Message Type
    ax1 = axes[0]
    for typ in sorted(set(r["type"] for r in results)):
        filtered = [r for r in results if r["type"] == typ]
        ax1.scatter(
            [r["dequeue_time"] for r in filtered],
            [r["priority"] for r in filtered],
            label=type_names.get(typ, str(typ)),
            alpha=0.5,
            s=10
        )
    ax1.set_title("Priority vs Time (Grouped by Message Type)")
    ax1.set_ylabel("Priority")
    ax1.legend(loc="upper right")

    # Scatter by Task ID
    ax2 = axes[1]
    task_ids = sorted(set(r["task_id"] for r in results))
    color_map = plt.cm.get_cmap("tab20", len(task_ids))
    task_id_to_color = {tid: color_map(i) for i, tid in enumerate(task_ids)}

    for tid in task_ids:
        filtered = [r for r in results if r["task_id"] == tid]
        ax2.scatter(
            [r["dequeue_time"] for r in filtered],
            [r["priority"] for r in filtered],
            label=f"Task {tid}",
            alpha=0.5,
            s=10,
            color=task_id_to_color[tid]
        )
    ax2.set_title("Priority vs Time (Grouped by Task ID)")
    ax2.set_xlabel("Dequeue Time")
    ax2.set_ylabel("Priority")
    ax2.legend(loc="upper right", fontsize="small", ncol=2)

    fig.tight_layout()
    buf = BytesIO()
    plt.savefig(buf, format="png")
    buf.seek(0)
    return buf


@pytest.mark.asyncio
@pytest.mark.parametrize("use_unique_task_ids", [False, True])
@allure.title("Priority Queue Priority Behavior (Parametrized)")
@allure.description("Compare message priority behavior with shared vs unique task IDs.")
async def test_priority_queue_priority_behavior(use_unique_task_ids):
    mode = "Unique Task IDs" if use_unique_task_ids else "Same Task ID"
    with allure.step(f"Running test with {mode}"):
        results = await run_priority_queue_test(use_unique_task_ids=use_unique_task_ids)

    # Log output
    drain_log = "Idx\tPriority\tType\tSource\tGroup\tTaskID\tDelay(ms)\n"
    for idx, r in enumerate(results):
        drain_log += f"{idx}\t{r['priority']}\t{r['type']}\t{r['source']}\t{r['group']}\t{r['task_id']}\t{r['delay_ms']}\n"
    allure.attach(drain_log, name=f"Queue Drain Log ({mode})", attachment_type=allure.attachment_type.TEXT)

    # Summary
    summary = Counter(r["source"] for r in results)
    summary_table = "Message Type\tCount\n" + "\n".join(f"{k}\t{v}" for k, v in summary.items())
    allure.attach(summary_table, name=f"Message Type Summary ({mode})", attachment_type=allure.attachment_type.TEXT)

    # Plot
    buf = plot_combined_scatter(results)
    allure.attach(buf.read(), name=f"Priority-Time Plot ({mode})", attachment_type=allure.attachment_type.PNG)

    # Validation
    priorities = [r["priority"] for r in results]
    assert priorities == sorted(priorities), f"Messages not dequeued in priority order in mode: {mode}"
