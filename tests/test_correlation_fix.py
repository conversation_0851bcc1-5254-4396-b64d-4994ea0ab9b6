#!/usr/bin/env python3
# coding=utf-8
"""
Test script to verify correlation ID tracking is working correctly.
"""

import asyncio
import logging
import os
import sys
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_correlation_with_factory():
    """Test correlation tracking with log record factory."""
    print("🧪 Testing correlation tracking with log record factory...")

    from dags.data_pipeline.logging_utils import install_correlation_log_record_factory
    from dags.data_pipeline.custom_logger import CorrelationContext

    # Install the correlation factory
    install_correlation_log_record_factory()

    # Create a simple logger
    logger = logging.getLogger("test_correlation")
    logger.setLevel(logging.INFO)

    # Create a handler that captures log records
    captured_records = []

    class CaptureHandler(logging.Handler):
        def emit(self, record):
            captured_records.append(record)

    handler = CaptureHandler()
    logger.addHandler(handler)

    # Test with correlation context
    with CorrelationContext("test_operation") as ctx:
        logger.info("Test message 1")
        logger.info("Test message 2")

    # Test without correlation context
    logger.info("Test message 3")

    # Verify results
    assert len(captured_records) == 3

    # First two messages should have correlation ID
    record1 = captured_records[0]
    record2 = captured_records[1]
    record3 = captured_records[2]

    print(f"Record 1 - correlation_id: {getattr(record1, 'correlation_id', 'MISSING')}")
    print(f"Record 1 - task_id: {getattr(record1, 'task_id', 'MISSING')}")
    print(f"Record 1 - coroutine_name: {getattr(record1, 'coroutine_name', 'MISSING')}")

    print(f"Record 2 - correlation_id: {getattr(record2, 'correlation_id', 'MISSING')}")
    print(f"Record 3 - correlation_id: {getattr(record3, 'correlation_id', 'MISSING')}")

    # Verify correlation IDs
    assert hasattr(record1, 'correlation_id'), "Record 1 missing correlation_id"
    assert hasattr(record2, 'correlation_id'), "Record 2 missing correlation_id"
    assert hasattr(record3, 'correlation_id'), "Record 3 missing correlation_id"

    assert record1.correlation_id != 'N/A', f"Record 1 correlation_id is N/A: {record1.correlation_id}"
    assert record2.correlation_id != 'N/A', f"Record 2 correlation_id is N/A: {record2.correlation_id}"
    assert record1.correlation_id == record2.correlation_id, "Records 1 and 2 should have same correlation_id"
    assert record3.correlation_id == 'N/A', f"Record 3 should have N/A correlation_id: {record3.correlation_id}"

    print("✅ Correlation tracking with factory test passed")


async def test_async_correlation_with_factory():
    """Test async correlation tracking with log record factory."""
    print("🧪 Testing async correlation tracking with factory...")

    from dags.data_pipeline.logging_utils import install_correlation_log_record_factory
    from dags.data_pipeline.custom_logger import CorrelationContext

    # Install the correlation factory
    install_correlation_log_record_factory()

    # Create a simple logger
    logger = logging.getLogger("test_async_correlation")
    logger.setLevel(logging.INFO)

    # Create a handler that captures log records
    captured_records = []

    class CaptureHandler(logging.Handler):
        def emit(self, record):
            captured_records.append(record)

    handler = CaptureHandler()
    logger.addHandler(handler)

    async def async_task(task_id):
        async with CorrelationContext(f"async_task_{task_id}") as ctx:
            logger.info(f"Starting async task {task_id}")
            await asyncio.sleep(0.01)
            logger.info(f"Completing async task {task_id}")
            return ctx.correlation_id

    # Run async tasks sequentially to avoid context interference
    correlation_ids = []
    for i in range(2):
        corr_id = await async_task(i)
        correlation_ids.append(corr_id)

    # Verify results
    assert len(captured_records) == 4  # 2 messages per task

    # Check that each task has its own correlation ID
    task1_records = captured_records[0:2]
    task2_records = captured_records[2:4]

    print(f"Task 1 Record 1 - correlation_id: {getattr(task1_records[0], 'correlation_id', 'MISSING')}")
    print(f"Task 1 Record 1 - task_id: {getattr(task1_records[0], 'task_id', 'MISSING')}")
    print(f"Task 1 Record 1 - coroutine_name: {getattr(task1_records[0], 'coroutine_name', 'MISSING')}")

    print(f"Task 2 Record 1 - correlation_id: {getattr(task2_records[0], 'correlation_id', 'MISSING')}")
    print(f"Task 2 Record 1 - task_id: {getattr(task2_records[0], 'task_id', 'MISSING')}")
    print(f"Task 2 Record 1 - coroutine_name: {getattr(task2_records[0], 'coroutine_name', 'MISSING')}")

    # Verify correlation IDs
    for record in captured_records:
        assert hasattr(record, 'correlation_id'), f"Record missing correlation_id: {record.getMessage()}"
        assert record.correlation_id != 'N/A', f"Record has N/A correlation_id: {record.getMessage()}"
        assert hasattr(record, 'task_id'), f"Record missing task_id: {record.getMessage()}"
        assert hasattr(record, 'coroutine_name'), f"Record missing coroutine_name: {record.getMessage()}"

    # Verify task separation
    task1_corr_id = task1_records[0].correlation_id
    task2_corr_id = task2_records[0].correlation_id

    assert task1_corr_id != task2_corr_id, "Different tasks should have different correlation IDs"
    assert task1_records[0].correlation_id == task1_records[1].correlation_id, "Same task records should have same correlation ID"
    assert task2_records[0].correlation_id == task2_records[1].correlation_id, "Same task records should have same correlation ID"

    print("✅ Async correlation tracking with factory test passed")


def test_container_integration():
    """Test correlation tracking with container integration."""
    print("🧪 Testing correlation tracking with container integration...")

    try:
        from dags.data_pipeline.containers import LoggerContainer
        from dags.data_pipeline.custom_logger import CorrelationContext

        # Initialize logger container
        container = LoggerContainer()
        container.init_resources()  # This should install the correlation factory

        # Get logger from container
        logger = container.logger()

        # Create a handler that captures log records
        captured_records = []

        class CaptureHandler(logging.Handler):
            def emit(self, record):
                captured_records.append(record)

        handler = CaptureHandler()
        logger.addHandler(handler)

        # Test with correlation context
        with CorrelationContext("container_test") as ctx:
            logger.info("Container test message 1")
            logger.info("Container test message 2")

        # Verify results
        assert len(captured_records) == 2

        record1 = captured_records[0]
        record2 = captured_records[1]

        print(f"Container Record 1 - correlation_id: {getattr(record1, 'correlation_id', 'MISSING')}")
        print(f"Container Record 2 - correlation_id: {getattr(record2, 'correlation_id', 'MISSING')}")

        assert hasattr(record1, 'correlation_id'), "Container record 1 missing correlation_id"
        assert hasattr(record2, 'correlation_id'), "Container record 2 missing correlation_id"
        assert record1.correlation_id != 'N/A', f"Container record 1 correlation_id is N/A: {record1.correlation_id}"
        assert record2.correlation_id != 'N/A', f"Container record 2 correlation_id is N/A: {record2.correlation_id}"
        assert record1.correlation_id == record2.correlation_id, "Container records should have same correlation_id"

        print("✅ Container integration test passed")

    except ImportError as e:
        print(f"⚠️ Container integration test skipped: {e}")
        print("   This is expected if dependencies are not installed")
        # Skip this test gracefully
    except Exception as e:
        print(f"❌ Container integration test failed: {e}")
        import traceback
        traceback.print_exc()
        raise  # Re-raise to fail the test


async def run_all_tests():
    """Run all correlation tracking tests."""
    print("🚀 Correlation ID Fix Tests")
    print("=" * 40)

    tests = [
        ("Basic Correlation Factory", test_correlation_with_factory),
        ("Async Correlation Factory", test_async_correlation_with_factory),
        ("Container Integration", test_container_integration),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                await test_func()
            else:
                test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            failed += 1

    print("\n" + "=" * 40)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All correlation tracking tests passed!")
        print("\nCorrelation ID tracking is now working correctly with:")
        print("- ✅ Log record factory installation")
        print("- ✅ Context variable propagation")
        print("- ✅ Async task correlation")
        print("- ✅ Container integration")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")

    return failed == 0


if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Tests failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
