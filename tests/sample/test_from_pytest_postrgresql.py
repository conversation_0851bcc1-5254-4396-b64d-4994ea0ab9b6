"""Template database tests."""
import os
import subprocess
import sys
import tempfile

import pytest
from psycopg import Connection

from pytest_postgresql.factories import postgresql, postgresql_proc
from tests.loader import load_database

temp_dir = tempfile.mkdtemp()

# Monkey patch for Windows compatibility
if sys.platform == "win32" and not hasattr(os, 'killpg'):
    def killpg_windows(pgid, sig):
        """Windows replacement for os.killpg"""
        try:
            os.kill(pgid, sig)
        except (PermissionError, ProcessLookupError, OSError):
            try:
                subprocess.run(['taskkill', '/F', '/PID', str(pgid)],
                               capture_output=True, check=False)
            except:
                pass



    os.killpg = killpg_windows

postgresql_proc_with_template = postgresql_proc(
    port=21987,
    dbname="stories_templated",
    load=[load_database],
)

postgresql_template = postgresql(
    "postgresql_proc_with_template",
    dbname="stories_templated",
)


@pytest.mark.xdist_group(name="template_database")
@pytest.mark.parametrize("_", range(5))
def test_template_database(postgresql_template: Connection, _: int) -> None:
    """Check that the database structure gets recreated out of a template."""
    with postgresql_template.cursor() as cur:
        cur.execute("SELECT * FROM stories")
        res = cur.fetchall()
        assert len(res) == 4
        cur.execute("TRUNCATE stories")
        cur.execute("SELECT * FROM stories")
        res = cur.fetchall()
        assert len(res) == 0