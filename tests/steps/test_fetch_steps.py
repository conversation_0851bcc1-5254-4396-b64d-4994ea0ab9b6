import asyncio

import pytest
from pytest_bdd import scenarios, given, when, then

import dags.data_pipeline.jira.api_client
from tests.test_fetch_with_retries_consistency import <PERSON><PERSON><PERSON><PERSON><PERSON>, MockCircuitBreaker
import dags.data_pipeline.utility_code as uc

# Load scenarios from feature file
scenarios("../features/fetch_with_retries.feature")

@pytest.fixture
def context():
    return {}

@pytest.mark.bdd
@pytest.mark.feature("Retry logic for HTTP fetches")
@given("a mock HTTP server returning 200")
def mock_success_response(mocker, context):
    response = mocker.AsyncMock()
    response.status = 200
    response.headers = {}
    response.json.return_value = {"data": "test"}

    context_manager = mocker.AsyncMock()
    context_manager.__aenter__.return_value = response
    context_manager.__aexit__.return_value = None

    mock_session = mocker.Mock()
    mock_session.request = mocker.Mock(return_value=context_manager)

    context["session"] = mock_session

@pytest.mark.bdd
@pytest.mark.feature("Retry logic for HTTP fetches")
@given("a mock HTTP server returning 500")
def mock_500_response(mocker, context):
    response = mocker.AsyncMock()
    response.status = 500
    response.headers = {}
    response.json.return_value = {"error": "Internal Server Error"}

    context_manager = mocker.AsyncMock()
    context_manager.__aenter__.return_value = response
    context_manager.__aexit__.return_value = None

    mock_session = mocker.Mock()
    mock_session.request = mocker.Mock(return_value=context_manager)

    context["session"] = mock_session

@pytest.mark.bdd
@pytest.mark.feature("Retry logic for HTTP fetches")
@when("the fetch_with_retries function is called")
def call_fetch(context):
    async def _call_fetch():
        try:
            result = await dags.data_pipeline.jira.api_client.fetch_with_retries(
                session=context["session"],
                method="GET",
                url="http://test.com",
            )
            print(f"Result: {result}")
            context["result"] = result
            context["exception"] = None
        except Exception as e:
            context["result"] = None
            context["exception"] = e
            print(f"Exception: {e}")

    # Run the async function synchronously
    asyncio.run(_call_fetch())

@pytest.mark.bdd
@pytest.mark.feature("Retry logic for HTTP fetches")
@then("the result should indicate success")
def assert_success(context):
    print("Context for success:", context)
    assert context.get("exception") is None, f"Unexpected exception: {context.get('exception')}"
    assert context.get("result") is not None, "Result is None"
    assert context["result"]["success"] is True

@pytest.mark.bdd
@pytest.mark.feature("Retry logic for HTTP fetches")
@then("the result should indicate failure")
def assert_failure(context):  # Fixed: renamed from assert_success
    print("Context for failure:", context)
    # Check if we got a result with success=False or an exception
    if context.get("result") is not None:
        assert context["result"]["success"] is False
    else:
        # If we got an exception instead of a result, that's also considered a failure
        assert context.get("exception") is not None
