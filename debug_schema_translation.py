#!/usr/bin/env python3
"""
Debug script to test schema translation.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the env.py functions
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'alembic'))

def test_schema_translation():
    """Test the schema translation logic."""
    
    # Mock the context.get_x_argument function
    class MockContext:
        def __init__(self, schema):
            self.schema = schema
        
        def get_x_argument(self, as_dictionary=True):
            return {'schema': self.schema}
    
    # Import the functions from env.py
    import env
    
    # Test different schemas
    test_cases = [
        ('public', {}),
        ('plat', {None: 'plat'}),
        ('plp', {None: 'plp'}),
        ('acq', {None: 'acq'}),
    ]
    
    for schema, expected_map in test_cases:
        # Mock the context
        original_context = getattr(env, 'context', None)
        env.context = MockContext(schema)
        
        # Test the schema translation
        result = env.get_schema_translate_map()
        print(f"Schema: {schema} -> Translation map: {result}")
        
        if result == expected_map:
            print(f"  ✅ Correct translation for {schema}")
        else:
            print(f"  ❌ Wrong translation for {schema}. Expected: {expected_map}, Got: {result}")
        
        # Test include_object
        class MockTable:
            def __init__(self, schema):
                self.schema = schema
        
        # Test public schema table
        public_table = MockTable('public')
        result = env.include_object(public_table, 'test_table', 'table', False, None)
        expected = (schema == 'public')
        print(f"  Public table included for {schema}: {result} (expected: {expected})")
        
        # Test custom schema table (None schema)
        custom_table = MockTable(None)
        result = env.include_object(custom_table, 'test_table', 'table', False, None)
        expected = (schema in ['plat', 'plp', 'acq'])
        print(f"  Custom table included for {schema}: {result} (expected: {expected})")
        
        print()

if __name__ == "__main__":
    test_schema_translation()
