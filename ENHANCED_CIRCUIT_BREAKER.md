# Enhanced GlobalCircuitBreaker

## Overview

The Enhanced GlobalCircuitBreaker provides sophisticated error handling with cancellable sleep, error classification, and health monitoring to prevent data inconsistency and improve system resilience.

## Key Features

### 1. Error Classification System

Errors are automatically classified into different types for appropriate handling:

- **RATE_LIMIT**: Rate limiting errors (429, Retry-After headers)
  - Processes wait and resume after rate limit clears
  - No process abortion
  - Coordinated recovery across all threads

- **CONNECTION_POOL**: Connection pool exhaustion errors
  - Retry with exponential backoff
  - Don't abort processes
  - Monitor connection usage

- **NETWORK**: Network/connectivity issues (timeouts, connection resets, DNS)
  - Retry with doubled retry count
  - Enhanced backoff strategies
  - Graceful degradation

- **SERVICE**: Service errors (5xx HTTP status codes)
  - Standard circuit breaker logic
  - Open circuit after threshold failures

- **UNRECOVERABLE**: Critical errors requiring shutdown
  - Trigger graceful shutdown
  - Prevent data corruption

### 2. Cancellable Sleep with asyncio.Event()

Traditional `asyncio.sleep()` replaced with event-based waiting:

```python
# Old approach
await asyncio.sleep(delay)  # Cannot be cancelled

# New approach
await circuit_breaker.wait_for_recovery(error_type=ErrorType.RATE_LIMIT)
# Can be cancelled when conditions change
```

Benefits:
- Immediate recovery when conditions improve
- No unnecessary waiting
- Better resource utilization
- Coordinated recovery across all processes

### 3. Health Monitoring Thread

Proactive system monitoring:
- Memory and CPU usage tracking
- Active connection monitoring
- Asyncio task count monitoring
- Automatic rate limit warnings when system load is high
- Prevents system overload

### 4. Enhanced Recovery Mechanisms

Different recovery strategies per error type:

#### Rate Limit Recovery
- Processes wait for rate limit to clear
- Coordinated recovery using `_rate_limit_recovery_event`
- No process abortion
- Automatic resumption when rate limit expires

#### Connection Pool Recovery
- Exponential backoff with configurable multiplier
- Connection usage monitoring
- Automatic recovery when connections become available

#### Network Recovery
- Doubled retry attempts for network issues
- Progressive backoff delays
- Graceful shutdown only after extended failures

#### Service Recovery
- Standard circuit breaker pattern
- Half-open state testing
- Automatic recovery after timeout

## Configuration

```python
config = CircuitBreakerConfig(
    failure_threshold=3,                    # Standard circuit breaker threshold
    recovery_timeout=30.0,                  # Circuit recovery timeout
    half_open_max_calls=3,                  # Half-open state test calls
    max_consecutive_rate_limits=10,         # Rate limit threshold
    connection_pool_retry_multiplier=2.0,   # Connection pool backoff multiplier
    network_error_retry_multiplier=2,       # Network error retry multiplier
    health_check_interval=30.0,             # Health check frequency
    max_concurrent_connections=50           # Connection monitoring threshold
)
```

## Usage Examples

### Basic Error Handling

```python
try:
    # Make HTTP request
    response = await session.get(url)
    await circuit_breaker.record_success()
except aiohttp.ClientConnectorError as e:
    # Connection pool error - will retry with backoff
    await circuit_breaker.record_error(e)
    await circuit_breaker.wait_for_recovery(error_type=ErrorType.CONNECTION_POOL)
except aiohttp.ClientResponseError as e:
    if e.status == 429:
        # Rate limit - will wait and resume
        await circuit_breaker.record_error(e, retry_delay_ms)
        await circuit_breaker.wait_for_recovery(error_type=ErrorType.RATE_LIMIT)
```

### Enhanced fetch_with_retries

The `fetch_with_retries` function now automatically:
- Classifies errors appropriately
- Uses error-specific retry counts
- Waits for appropriate recovery
- Avoids unnecessary shutdowns
- Returns deferred responses for recoverable errors

### Circuit Status Monitoring

```python
status = await circuit_breaker.get_circuit_status()
print(f"State: {status['state']}")
print(f"Active connections: {status['active_connections']}")
print(f"Rate limits: {status['consecutive_rate_limits']}")
print(f"Recovery events: {status['recovery_events']}")
```

## Error Handling Improvements

### Before Enhancement
```
2025-07-11T10:12:43.453 ERROR [N/A] [209409574368:can_execute] fetch_worklog can_execute 1171 | Failed to fetch worklogs for issue CPP-77735: Circuit breaker OPEN
2025-07-11T10:12:43.453 ERROR [N/A] [209409574368:can_execute] fetch_worklog_data can_execute 0463 | Failed to fetch worklog for CPP-77735: Circuit breaker OPEN
2025-07-11T10:12:43.453 ERROR [N/A] [209400936569:can_execute] fetch_with_retries can_execute 0067 | Circuit breaker still OPEN after waiting. Aborting request.
```

### After Enhancement
```
2025-07-11T10:12:43.453 WARNING [N/A] [209409574368] fetch_worklog | Rate limited. Waiting for coordinated recovery. Retry delay: 2.0s
2025-07-11T10:12:43.453 INFO [N/A] [209409574368] fetch_worklog | Rate limit recovery completed, resuming processing
2025-07-11T10:12:45.453 INFO [N/A] [209409574368] fetch_worklog | Successfully fetched worklogs for issue CPP-77735
```

## Benefits

1. **Data Consistency**: No process abortion during recoverable errors
2. **Better Resource Utilization**: Cancellable sleep prevents unnecessary waiting
3. **Improved Resilience**: Error-specific handling strategies
4. **Proactive Monitoring**: Health checks prevent system overload
5. **Coordinated Recovery**: All processes resume together when conditions improve
6. **Graceful Degradation**: Only shutdown for truly unrecoverable errors

## Migration Guide

### Existing Code
```python
# Old usage
await circuit_breaker.record_failure()
await circuit_breaker.wait_for_recovery()
```

### Enhanced Code
```python
# New usage with error classification
await circuit_breaker.record_error(exception, retry_delay_if_rate_limit)
await circuit_breaker.wait_for_recovery(error_type=ErrorType.RATE_LIMIT)
```

The enhanced circuit breaker is backward compatible - existing `record_failure()` calls will work but won't benefit from error classification.

## Testing

Run the test suite to verify functionality:

```bash
python tests/container/test_enhanced_circuit_breaker.py
```

Run usage examples:

```bash
python examples/enhanced_circuit_breaker_usage.py
```

## Health Check Integration

The circuit breaker includes optional health monitoring that can be enhanced with system metrics:

```python
# Optional: Install psutil for system monitoring
pip install psutil
```

When psutil is available, the health monitor will track:
- Memory usage percentage
- CPU usage percentage
- Active asyncio tasks
- Connection pool usage

This enables proactive circuit management based on system load.
