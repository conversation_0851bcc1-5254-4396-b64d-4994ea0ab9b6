#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to clean up the database and start fresh.
"""

import sys
import os
from sqlalchemy import create_engine, text
from alembic.config import Config

def clean_database():
    """Clean up all tables and schemas."""
    
    # Get database URL from alembic.ini
    alembic_cfg = Config("alembic.ini")
    database_url = alembic_cfg.get_main_option("sqlalchemy.url")
    
    engine = create_engine(database_url)
    
    schemas = ['public', 'plat', 'plp', 'acq']
    
    with engine.connect() as connection:
        with connection.begin():
            # Drop all custom schemas
            for schema in ['plat', 'plp', 'acq']:
                try:
                    connection.execute(text(f"DROP SCHEMA IF EXISTS {schema} CASCADE"))
                    print(f"Dropped schema {schema}")
                except Exception as e:
                    print(f"Could not drop schema {schema}: {e}")
            
            # Drop all tables in public schema except system tables
            try:
                result = connection.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE'
                    AND table_name NOT LIKE 'pg_%'
                    AND table_name NOT LIKE 'sql_%'
                """))
                
                tables = [row[0] for row in result]
                for table in tables:
                    try:
                        connection.execute(text(f"DROP TABLE IF EXISTS public.{table} CASCADE"))
                        print(f"Dropped table public.{table}")
                    except Exception as e:
                        print(f"Could not drop table {table}: {e}")
                        
            except Exception as e:
                print(f"Error dropping public tables: {e}")

if __name__ == "__main__":
    print("Cleaning database...")
    clean_database()
    print("Database cleaned!")
