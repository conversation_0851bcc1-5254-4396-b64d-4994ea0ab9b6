# Alembic Multi-Schema Migration Setup - Complete Summary

## What Was Implemented

### 1. Enhanced `alembic/env.py`
- **Schema Translation Support**: Uses `schema_translate_map` to handle multiple schemas
- **Dynamic Schema Creation**: Automatically creates custom schemas during migration
- **Schema-Aware Filtering**: `include_object` function filters tables based on current schema
- **Environment Variable Control**: Uses `ALEMBIC_SCHEMA` to control target schema

### 2. Migration Files Created

#### Public Schema Migration (`0d6c02afdf3a_create_public_schema_tables.py`)
Creates tables with `{'schema': 'public'}`:
- `all_boards`
- `user` 
- `issue_fields`
- `nlp_training_data`
- `permission`
- `requesttracker`
- `role`
- `deleted_worklog`

#### Custom Schema Migration (`68d72ca579f7_create_custom_schema_tables.py`)
Creates tables with `{'schema': None}` in all custom schemas (`plat`, `plp`, `acq`):
- `issue` (with self-referential foreign keys)
- `issue_comments`
- `issue_links`
- `work_log`
- `changelog_json`
- `initiative_attribute`
- `versions`
- `run_details_jira`

### 3. Management Script (`manage_migrations.py`)
Comprehensive script for multi-schema operations:
- `upgrade --all` / `upgrade --schema <name>`
- `downgrade --schema <name> --target <revision>`
- `status` (shows all schemas)
- `history`
- `revision --message <msg> --schema <name> --autogenerate`
- `stamp --all --revision <rev>` / `stamp --schema <name> --revision <rev>`

### 4. Documentation
- `ALEMBIC_MULTI_SCHEMA_GUIDE.md`: Comprehensive usage guide
- `MIGRATION_SETUP_SUMMARY.md`: This summary document

## Current Database State

All schemas are now at migration `68d72ca579f7` (head):
- **Public Schema**: Contains user management and configuration tables
- **Plat Schema**: Contains full JIRA data structure
- **Plp Schema**: Contains full JIRA data structure  
- **Acq Schema**: Contains full JIRA data structure

## Key Features Implemented

### 1. Schema Separation Logic
```python
# In env.py
def include_object(object, name, type_, reflected, compare_to):
    current_schema = os.environ.get('ALEMBIC_SCHEMA', 'public')
    
    if type_ == "table":
        table_schema = getattr(object, 'schema', None)
        
        if current_schema == 'public':
            return table_schema == 'public'  # Only public tables
        elif current_schema in CUSTOM_SCHEMAS:
            return table_schema is None      # Only schema-None tables
```

### 2. Schema Translation
```python
def get_schema_translate_map():
    current_schema = os.environ.get('ALEMBIC_SCHEMA', None)
    
    if current_schema and current_schema in CUSTOM_SCHEMAS:
        return {None: current_schema}  # Translate None -> current_schema
    else:
        return {}  # No translation for public
```

### 3. Cross-Schema Foreign Keys
Tables in custom schemas can reference public schema tables:
```sql
-- Custom schema table referencing public schema
CONSTRAINT fk_issue_assignee_user 
FOREIGN KEY(assignee) REFERENCES public.user(accountId)
```

## Usage Examples

### Daily Operations
```powershell
# Check status of all schemas
python manage_migrations.py status

# Create new migration for public schema tables
python manage_migrations.py revision --message "Add new public table" --schema public --autogenerate

# Create new migration for custom schema tables  
python manage_migrations.py revision --message "Add new custom table" --schema plat --autogenerate

# Apply migrations to all schemas
python manage_migrations.py upgrade --all
```

### Manual Commands (if needed)
```powershell
# Public schema operations
$env:ALEMBIC_SCHEMA='public'; uv run alembic current
$env:ALEMBIC_SCHEMA='public'; uv run alembic upgrade head

# Custom schema operations
$env:ALEMBIC_SCHEMA='plat'; uv run alembic current
$env:ALEMBIC_SCHEMA='plat'; uv run alembic upgrade head
```

## Model Development Guidelines

### For New Public Schema Tables
```python
class NewPublicTable(Base):
    id = Column(Integer, primary_key=True)
    name = Column(String)
    
    __table_args__ = (
        {'schema': 'public'}  # Explicit public schema
    )
```

### For New Custom Schema Tables
```python
class NewCustomTable(Base):
    id = Column(Integer, primary_key=True)
    name = Column(String)
    issue_id = Column(Integer, ForeignKey('issue.id'))  # Same schema reference
    user_id = Column(String, ForeignKey('public.user.accountId'))  # Cross-schema reference
    
    __table_args__ = (
        {'schema': None}  # Will be created in all custom schemas
    )
```

## Troubleshooting Commands

### Reset Schema Migration State
```powershell
# If schemas get out of sync, stamp them to correct revision
python manage_migrations.py stamp --all --revision 68d72ca579f7
```

### Check Migration History
```powershell
python manage_migrations.py history
```

### Manual Schema Creation (if needed)
```sql
-- Connect to PostgreSQL and run if schemas don't exist
CREATE SCHEMA IF NOT EXISTS plat;
CREATE SCHEMA IF NOT EXISTS plp;
CREATE SCHEMA IF NOT EXISTS acq;
```

## Next Steps for Development

1. **Adding New Tables**: Use the management script with `--autogenerate`
2. **Schema Changes**: Always test on development database first
3. **Production Deployment**: Use `python manage_migrations.py upgrade --all`
4. **Backup Strategy**: Always backup before running migrations in production

## Files in Your Project

### Core Alembic Files
- `alembic.ini` - Configuration (database URL)
- `alembic/env.py` - Multi-schema environment setup
- `alembic/versions/0d6c02afdf3a_*.py` - Public schema migration
- `alembic/versions/68d72ca579f7_*.py` - Custom schema migration

### Management Tools
- `manage_migrations.py` - Multi-schema management script
- `create_custom_schema_migration.py` - Helper script (can be removed)

### Documentation
- `ALEMBIC_MULTI_SCHEMA_GUIDE.md` - Detailed usage guide
- `MIGRATION_SETUP_SUMMARY.md` - This summary

## Success Indicators

✅ All schemas created and migrated successfully
✅ Management script working for all operations
✅ Cross-schema foreign keys properly configured
✅ Schema translation working correctly
✅ Documentation complete

Your multi-schema Alembic setup is now fully functional and ready for development!
