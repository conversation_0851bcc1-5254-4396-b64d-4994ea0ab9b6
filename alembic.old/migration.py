from enum import Enum
from sqlalchemy import Column, Integer, String, create_engine
from sqlalchemy.ext.declarative import declarative_base
from alembic import op
from sqlalchemy.dialects.postgresql import TEXT, BOOLEAN, ENUM
from citext import CIText
import os
from pykeepass import PyKeePass as pkp
import json
from urllib.parse import quote, urlparse
from sqlalchemy.orm import sessionmaker, aliased

Base = declarative_base()


class CacheyCIText(CIText):
    # Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    # Can remove when that issue is fixed
    cache_ok = True


class AccountType(Enum):
    Atlassian = 'user'
    App = 'system'
    Customer = 'customer'


class User(Base):
    __tablename__ = 'users'
    accountId = Column(TEXT, primary_key=True, nullable=False, unique=True, index=True)
    accountType = Column(TEXT, nullable=False)
    new_accountType = Column(ENUM('user', 'system', 'customer', name="AccountType"))
    emailAddress = Column(CacheyCIText())
    displayName = Column(TEXT)
    active = Column(BOOLEAN)
    timeZone = Column(TEXT)
    locale = Column(TEXT)
    __table_args__ = (
        # CheckConstraint("now() AT TIME ZONE timeZone IS NOT NULL", name='user_timezone'),
        {'schema': 'public'}
    )


def start_session(prjkey: str = "plat"):
    # dbschema = 'public,PLAT'  # Searches left-to-right
    dbschema = f"{prjkey},public"
    conn_str = None
    if os.name == "nt":
        filename = "../dags/data_pipeline/config.json"
    elif os.uname().nodename == "airflow":
        filename = "/home/<USER>/airflow/dags/data_pipeline/config.json"
    else:
        filename = "/opt/airflow/dags/airflow/data_pipeline/config.json"
    keedb = f'KeePassDB_{os.name}'
    keepass = f'KeyPassKey_{os.name}'

    with open(filename, "r") as fp:
        data = json.load(fp)
    ref = pkp(filename=data[keedb], keyfile=data[keepass])
    if os.name == "nt":
        entry = ref.find_entries(title='lilly', first=True)
        user = entry.username
        pwd = entry.password
        conn_str = 'postgresql+psycopg2://{username}:{password}@localhost:5432/{database}'.format(
            username=user,
            password=quote(pwd),
            database='jira'
        )
        # Test to see if DB can be created
        # print("Testing database creation!!!!")
        entry = ref.find_entries(title='jiradb', first=True)

        # print(entry.PG_SERVER)
        # print(entry.PG_PORT)
        # conn_str_test = 'postgresql+psycopg2://{username}:{password}@localhost:5432/{database}'.format(
        #     username=user,
        #     password=quote(pwd),
        #     database='test'
        # )
        #
        # engine_test = create_engine(conn_str_test)
        # if not database_exists(engine_test.url):
        #     create_database(engine_test.url)
        # print(database_exists(engine_test.url))
        # End Test DB creation

    elif os.uname().nodename == 'airflow':
        entry = ref.find_entries(title='jiradb', first=True)
        user = entry.username
        pwd = entry.password

        conn_str = 'postgresql+psycopg2://{username}:{password}@***********:5432/{database}'.format(
            username=user,
            password=quote(pwd),
            database='jira'
        )
    else:
        entry = ref.find_entries(title='PG_DB', first=True)
        user = entry.username
        pwd = entry.password

        server_name = os.getenv('PG_SERVER_NAME', None)
        server_port = os.getenv('PG_SERVER_PORT', None)
        server_db = os.getenv('PG_DB_NAME', None)

        conn_str = f'postgresql+psycopg2://' + user + ':' + quote(
            pwd) + '@' + server_name + ':' + server_port + '/' + server_db

        # SQLALCHEMY_ENGINE_OPTIONS = {'pool_size': pool_size, 'max_overflow': max_overflow, 'pool_pre_ping': True,
        #                              'pool_timeout': DB_POOL_TIMEOUT, 'pool_recycle': 3600,
        #                              'connect_args': {'connect_timeout': 5, 'options': '-c statement_timeout=5000'}}

    _engine = create_engine(
        conn_str,
        connect_args={
            'options': '-csearch_path={}'.format(dbschema), 'connect_timeout': 30,
            "keepalives": 1,
            "keepalives_idle": 30,
            "keepalives_interval": 10,
            "keepalives_count": 5,
                      },
        echo=False,
        pool_pre_ping=True,
        pool_use_lifo=True,
        pool_recycle=300, max_overflow=2, pool_size=10
    ).execution_options(schema_translate_map={None: prjkey})

    return _engine


def upgrade():
    # Create a new column with the desired type
    new_column = Column('new_accountType', ENUM(AccountType))
    op.add_column('users', new_column)

    # Copy the data from the old column to the new column

    engine = start_session('plat')
    conn = engine.connect()
    conn.execute("UPDATE users SET new_accountType = accountType::text")

    # Drop the old column
    op.drop_column('users', 'accountType')

    # Rename the new column to the original name
    op.alter_column('users', 'new_accountType', new_column_name='AccountType')


def downgrade():
    op.drop_column('users', 'accountType')
    op.add_column('users', Column('accountType', TEXT))

# Create a new revision that incorporates the changes

revision = "Upgrade User model"
down_revision = None
branch_labels = None
depends_on = None