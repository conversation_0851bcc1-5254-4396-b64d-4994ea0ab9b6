#!/usr/bin/env python3
import sys
import logging
sys.path.insert(0, 'dags/data_pipeline')

# Import the module first to ensure it's in sys.modules
import dags.data_pipeline.custom_logger as custom_logger_module
from dags.data_pipeline.logging_utils import install_correlation_log_record_factory

# Now import from the same module
CorrelationContext = custom_logger_module.CorrelationContext
correlation_id_var = custom_logger_module.correlation_id_var

print("=== Debug Correlation ID Issue ===")

# Test 1: Context variables
print("\n1. Testing context variables:")
print(f"Before context: {correlation_id_var.get()}")

with CorrelationContext('test123') as ctx:
    print(f"Inside context: {correlation_id_var.get()}")
    print(f"Context object correlation_id: {ctx.correlation_id}")

print(f"After context: {correlation_id_var.get()}")

# Test 2: Log record factory
print("\n2. Testing log record factory:")
install_correlation_log_record_factory()

logger = logging.getLogger('test')
logger.setLevel(logging.INFO)  # Set level to ensure logging works
records = []

class TestHandler(logging.Handler):
    def emit(self, record):
        records.append(record)
        print(f"Handler received record: {record.getMessage()}")

handler = TestHandler()
handler.setLevel(logging.INFO)
logger.addHandler(handler)

# Test without context
logger.info('message without context')
if records:
    print(f"Without context - correlation_id: {getattr(records[0], 'correlation_id', 'MISSING')}")
else:
    print("No records captured without context")

# Test with context
with CorrelationContext('test456') as ctx:
    print(f"Context variable during logging: {correlation_id_var.get()}")
    print(f"Module context variable: {custom_logger_module.correlation_id_var.get()}")

    # Test if they're the same object
    print(f"Same object? {correlation_id_var is custom_logger_module.correlation_id_var}")

    # Let's also test the factory directly
    original_factory = logging.getLogRecordFactory()
    test_record = original_factory('test', logging.INFO, __file__, 1, 'test message', (), None)
    print(f"Direct factory test - correlation_id: {getattr(test_record, 'correlation_id', 'MISSING')}")

    logger.info('message with context')

if len(records) > 1:
    print(f"With context - correlation_id: {getattr(records[1], 'correlation_id', 'MISSING')}")
else:
    print(f"Only {len(records)} records captured")

# Test 3: Check if factory is actually installed
print(f"\n3. Factory installed: {getattr(install_correlation_log_record_factory, '_factory_installed', 'Unknown')}")

# Test 4: Check current log record factory
current_factory = logging.getLogRecordFactory()
print(f"Current factory: {current_factory}")
print(f"Factory name: {getattr(current_factory, '__name__', 'Unknown')}")
