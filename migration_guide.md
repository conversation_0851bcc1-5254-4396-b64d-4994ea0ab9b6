# Session Manager Migration Guide

## Overview
This guide provides step-by-step instructions for migrating from the legacy session managers to the new refactored architecture with enhanced capabilities.

## Migration Summary

### Before (Legacy)
```python
# Old session manager usage
from dags.data_pipeline.containers import PostgresSessionManager

# Manual session management
session_manager = PostgresSessionManager(entry, schema, rw=True)
async with session_manager.async_session() as session:
    # Database operations
    pass
# Manual cleanup required
await session_manager.aclose()
```

### After (Refactored)
```python
# New enhanced session manager usage
from dags.data_pipeline.containers import FullyEnhancedSessionManager

# Automatic lifecycle management with connection recovery
session_manager = FullyEnhancedSessionManager(entry, schema, rw=True)
async with session_manager.async_session() as session:
    # Database operations with automatic retry on connection failures
    pass
# Automatic cleanup via lifecycle manager
```

## Key Improvements

### 1. Enhanced Session Managers
- **RefactoredPostgresSessionManager**: Base functionality with sync/async support
- **EnhancedSessionManager**: Adds connection recovery with exponential backoff
- **ManagedSessionManager**: Adds automatic lifecycle management
- **FullyEnhancedSessionManager**: All capabilities combined

### 2. Connection Recovery
```python
# Automatic retry on connection failures
async with enhanced_session_manager.async_session() as session:
    # If connection fails, automatically retries up to 3 times
    # with exponential backoff (1s, 2s, 4s)
    result = await session.execute(query)
```

### 3. TaskLifecycleCoordinator Integration
```python
# In process_task function
task_coordinator = TaskLifecycleCoordinator(logger)

# Register monitors
await task_coordinator.register_monitor(monitor_task, "monitor")
await task_coordinator.register_monitor(db_monitor_task, "db_monitor")

# Execute tasks with coordination
await _execute_all_tasks_with_coordinator(
    task_definitions, job_tasks, task_executor,
    progress_manager, task_coordinator, logger
)

# Automatic graceful shutdown when all tasks complete
await task_coordinator.wait_for_all_tasks_completion()
```

## Migration Steps

### Step 1: Update Container Dependencies
```python
# OLD
@inject
async def my_function(
    db_session=Provide[DatabaseSessionManagerContainer.database_rw]
):
    pass

# NEW
@inject
async def my_function(
    db_session=Provide[DatabaseSessionManagerContainer.database_rw_enhanced]
):
    pass
```

### Step 2: Update Session Usage
```python
# OLD - Manual error handling
try:
    async with session_manager.async_session() as session:
        result = await session.execute(query)
except InterfaceError:
    # Manual retry logic
    await session_manager._recreate_engine()
    async with session_manager.async_session() as session:
        result = await session.execute(query)

# NEW - Automatic error handling
async with enhanced_session_manager.async_session() as session:
    # Automatic retry with exponential backoff
    result = await session.execute(query)
```

### Step 3: Update Monitor Integration
```python
# OLD - Manual cancellation
monitor_task = asyncio.create_task(monitor())
# ... execute tasks ...
monitor_task.cancel()
try:
    await monitor_task
except asyncio.CancelledError:
    pass

# NEW - Coordinated shutdown
task_coordinator = TaskLifecycleCoordinator(logger)
monitor_task = asyncio.create_task(monitor(task_coordinator=task_coordinator))
await task_coordinator.register_monitor(monitor_task, "monitor")
# ... execute tasks ...
await task_coordinator.wait_for_all_tasks_completion()  # Automatic graceful shutdown
```

## Performance Improvements

### 1. Connection Pooling
- Enhanced connection pool management with pre-ping
- Automatic connection recovery reduces downtime
- Configurable pool sizes for different workloads

### 2. Resource Management
- Automatic cleanup via lifecycle manager
- WeakSet-based instance tracking prevents memory leaks
- Proper async context manager usage

### 3. Error Recovery
- Exponential backoff reduces database load during failures
- Automatic engine recreation for persistent connection issues
- Graceful degradation under high load

## Testing Strategy

### 1. Unit Tests
```python
@pytest.mark.asyncio
async def test_enhanced_session_manager():
    manager = FullyEnhancedSessionManager(entry, "test_schema")
    
    # Test connection recovery
    with patch.object(manager, '_handle_connection_error', return_value=True):
        async with manager.async_session() as session:
            # Test database operations
            pass
```

### 2. Integration Tests
```python
@pytest.mark.asyncio
async def test_task_lifecycle_coordination():
    coordinator = TaskLifecycleCoordinator(logger)
    
    # Test monitor registration and shutdown
    monitor_task = asyncio.create_task(mock_monitor())
    await coordinator.register_monitor(monitor_task, "test_monitor")
    
    # Test task completion triggers shutdown
    task_id = await coordinator.register_task("test_task")
    await coordinator.mark_task_completed(task_id, "success")
    await coordinator.wait_for_all_tasks_completion()
    
    assert monitor_task.cancelled() or monitor_task.done()
```

## Rollback Plan

If issues arise, rollback is straightforward:

1. **Revert container dependencies**:
   ```python
   # Change back to legacy providers
   database_rw=Provide[DatabaseSessionManagerContainer.database_rw]
   ```

2. **Disable TaskLifecycleCoordinator**:
   ```python
   # Comment out coordinator usage, revert to manual cancellation
   # task_coordinator = TaskLifecycleCoordinator(logger)
   ```

3. **Restore manual session management**:
   ```python
   # Use legacy session managers with manual cleanup
   session_manager = PostgresSessionManager(entry, schema, rw=True)
   ```

## Monitoring and Observability

### 1. Enhanced Logging
```python
# Automatic correlation IDs and structured logging
logger.info("Task completed", extra={
    "task_id": coordinator_task_id,
    "result": result,
    "duration": execution_time
})
```

### 2. Metrics Collection
```python
# Built-in metrics for connection recovery
logger.info(f"Connection recovered after {retry_count} attempts")
logger.info(f"Monitor shutdown complete - Cancelled: {cancelled_count}, Errors: {error_count}")
```

### 3. Health Checks
```python
# Automatic health monitoring
coordinator.get_active_task_count()  # Monitor active tasks
coordinator.get_completed_task_count()  # Track completion rate
```

## Best Practices

1. **Always use enhanced session managers** for new code
2. **Register all long-running tasks** with TaskLifecycleCoordinator
3. **Use async context managers** for proper resource cleanup
4. **Monitor connection recovery metrics** in production
5. **Test graceful shutdown scenarios** thoroughly

## Support and Troubleshooting

### Common Issues
1. **Connection timeouts**: Check pool configuration and network connectivity
2. **Memory leaks**: Ensure proper async context manager usage
3. **Shutdown hangs**: Verify all monitors are registered with coordinator

### Debug Commands
```python
# Check active instances
RefactoredPostgresSessionManager._instances

# Monitor task states
coordinator.get_active_task_count()
coordinator.is_shutdown_requested()

# Check schema history
session_manager.get_schema_history()
```
