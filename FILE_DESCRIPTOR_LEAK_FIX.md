# File Descriptor Leak Fix for DataFrameUtils

## Problem Description

The `quick_save_dataframe` function in the dataframe_utils module was causing "Too many open files" errors (errno 24) due to file descriptor leaks. This was happening because:

1. **Per-instance loggers**: Each `DataframeDebugger` instance created its own logger with a `TimedRotatingFileHandler`
2. **High-frequency usage**: Functions like `ChangelogDataProcessor` and `WorklogDataProcessor` called `quick_save_dataframe` multiple times per processing cycle
3. **Improper cleanup**: File handlers weren't being properly closed when debugger instances were destroyed
4. **Accumulating handlers**: Multiple logger instances led to accumulation of open file descriptors

## Root Cause Analysis

The issue was in `DataframeDebugger._setup_logger()`:

```python
# BEFORE (problematic code)
def _setup_logger(self, log_level: int) -> logging.Logger:
    logger = logging.getLogger(f"{__name__}.{id(self)}")  # Unique logger per instance
    # ... creates new TimedRotatingFileHandler for each instance
    handler = TimedRotatingFileHandler(
        filename=log_file_path,  # Unique file per instance
        # ...
    )
```

This created:
- Unique logger per `DataframeDebugger` instance
- Unique log file per instance (e.g., `debugger_2241638293072.log`)
- New file handler for each logger
- No proper cleanup of file descriptors

## Solution Implemented

### 1. Shared Logger Cache

Implemented a global logger cache to reuse loggers instead of creating new ones:

```python
# Global logger cache to prevent file descriptor leaks
_logger_cache = {}
_logger_cache_lock = Lock()
_active_handlers = weakref.WeakSet()
```

### 2. Improved Logger Setup

Modified `_setup_logger()` to use shared loggers:

```python
def _setup_logger(self, log_level: int) -> logging.Logger:
    logger_name = f"{__name__}.dataframe_debugger"  # Shared name
    
    with _logger_cache_lock:
        if logger_name in _logger_cache:
            return cached_logger  # Reuse existing logger
        
        # Create shared log file instead of per-instance files
        log_file_path = os.path.join(log_dir, "dataframe_debugger_shared.log")
        
        # Use SafeTimedRotatingFileHandler with delay=True
        handler = SafeTimedRotatingFileHandler(
            filename=log_file_path,
            delay=True  # Don't open file until first write
        )
```

### 3. Proper Cleanup Mechanism

Added cleanup functions to properly close file handlers:

```python
def cleanup_logger_cache():
    """Clean up cached loggers and their handlers to prevent file descriptor leaks."""
    with _logger_cache_lock:
        # Close all active handlers
        for handler in _active_handlers:
            handler.close()
        _logger_cache.clear()
        _active_handlers.clear()

# Register cleanup function to run at exit
atexit.register(cleanup_logger_cache)
```

### 4. Minimal Save Function

Created a new `quick_save_dataframe_minimal()` function for high-frequency operations:

```python
def quick_save_dataframe_minimal(df, filename=None, path=None, file_format="xlsx"):
    """
    Minimal DataFrame save function without logging to prevent file descriptor leaks.
    Optimized for high-frequency operations where logging is not critical.
    """
    # Direct file saving without logger creation
    # Uses print() instead of logging for errors
```

### 5. Updated Processors

Modified specialized processors to use the minimal save function:

- `ChangelogDataProcessor`: Uses `quick_save_dataframe_minimal`
- `WorklogDataProcessor`: Uses `quick_save_dataframe_minimal`  
- `CommentDataProcessor`: Uses `quick_save_dataframe_minimal`

## Benefits of the Fix

1. **Eliminates File Descriptor Leaks**: Shared loggers prevent accumulation of file handlers
2. **Improved Performance**: Reduced overhead from logger creation
3. **Better Resource Management**: Proper cleanup of file descriptors
4. **Backward Compatibility**: Original `quick_save_dataframe` still works
5. **Optimized for High-Frequency Use**: New minimal function for frequent operations

## Usage Recommendations

### For High-Frequency Operations (Recommended)
```python
from dags.data_pipeline.dataframe_utils.dataframe_debug_sync import quick_save_dataframe_minimal

# Use for frequent debug saves in processors
success = quick_save_dataframe_minimal(df, "debug_file.xlsx", "/path/to/logs")
```

### For Regular Operations
```python
from dags.data_pipeline.dataframe_utils.dataframe_debug_sync import quick_save_dataframe

# Use when logging is important
success = quick_save_dataframe(df, "important_file.xlsx", "/path/to/logs")
```

### For Advanced Usage
```python
from dags.data_pipeline.dataframe_utils.dataframe_debug_sync import DataframeDebugger

# Use context manager for multiple operations
with DataframeDebugger(path="/path/to/logs") as debugger:
    debugger.debug_dataframe(df1, "file1.xlsx")
    debugger.debug_dataframe(df2, "file2.xlsx")
    debugger.wait_for_completion()
```

## Testing

A comprehensive test script `test_file_descriptor_fix.py` has been created to verify:

1. **Minimal Save Function**: Tests high-frequency operations without logging
2. **Regular Save Function**: Tests improved logging with proper cleanup
3. **High-Frequency Usage**: Simulates the original problematic usage pattern

Run the test with:
```bash
python test_file_descriptor_fix.py
```

## Files Modified

1. **`dags/data_pipeline/dataframe_utils/dataframe_debug_sync.py`**:
   - Added logger caching mechanism
   - Improved `_setup_logger()` method
   - Added `quick_save_dataframe_minimal()` function
   - Added cleanup functions

2. **`dags/data_pipeline/specialized_field_mappers.py`**:
   - Updated imports to use `quick_save_dataframe_minimal`
   - Modified all processor classes to use minimal save function

3. **`test_file_descriptor_fix.py`** (new):
   - Comprehensive test suite for the fix

4. **`FILE_DESCRIPTOR_LEAK_FIX.md`** (new):
   - This documentation file

## Monitoring

To monitor file descriptor usage in production:

1. **Check open files on Unix systems**:
   ```bash
   lsof -p <process_id> | wc -l
   ```

2. **Monitor log files**:
   - Old pattern: Multiple `debugger_*.log` files
   - New pattern: Single `dataframe_debugger_shared.log` file

3. **Application logs**: Look for "Too many open files" errors (should be eliminated)

## Future Improvements

1. **Async Version**: Apply similar fixes to `dataframe_debug_async.py`
2. **Metrics**: Add file descriptor usage metrics to monitoring system
3. **Configuration**: Make log file paths configurable via environment variables
4. **Rotation**: Implement more aggressive log rotation for high-volume environments
