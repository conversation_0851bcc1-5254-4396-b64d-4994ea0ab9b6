#!/usr/bin/env python3
"""
Multi-Schema Migration Management Script for Alembic

This script helps manage Alembic migrations across multiple PostgreSQL schemas.
It provides utilities to run migrations for specific schemas or all schemas.

Usage:
    python manage_migrations.py upgrade --schema public
    python manage_migrations.py upgrade --schema plat
    python manage_migrations.py upgrade --all
    python manage_migrations.py status
    python manage_migrations.py revision --message "Add new table" --schema public
"""

import os
import sys
import subprocess
import argparse
from typing import List, Optional

# Define the schemas
PUBLIC_SCHEMA = 'public'
CUSTOM_SCHEMAS = ['plat', 'plp', 'acq']
ALL_SCHEMAS = [PUBLIC_SCHEMA] + CUSTOM_SCHEMAS

def run_alembic_command(command: List[str], schema: Optional[str] = None) -> int:
    """Run an alembic command with optional schema argument."""
    if schema:
        # Insert -x schema=<name> before the alembic command
        command = command[:1] + ['-x', f'schema={schema}'] + command[1:]
        print(f"Running alembic command for schema '{schema}': {' '.join(command)}")
    else:
        print(f"Running alembic command: {' '.join(command)}")

    # Use uv run to execute alembic
    full_command = ['uv', 'run'] + command
    result = subprocess.run(full_command)
    return result.returncode

def upgrade_schema(schema: str, target: str = 'head') -> bool:
    """Upgrade a specific schema to target revision."""
    print(f"\n=== Upgrading schema '{schema}' to '{target}' ===")
    return run_alembic_command(['alembic', 'upgrade', target], schema) == 0

def upgrade_all_schemas(target: str = 'head') -> bool:
    """Upgrade all schemas to target revision."""
    print(f"\n=== Upgrading all schemas to '{target}' ===")
    success = True
    
    # First upgrade public schema
    if not upgrade_schema(PUBLIC_SCHEMA, target):
        print(f"Failed to upgrade {PUBLIC_SCHEMA} schema")
        success = False
    
    # Then upgrade custom schemas
    for schema in CUSTOM_SCHEMAS:
        if not upgrade_schema(schema, target):
            print(f"Failed to upgrade {schema} schema")
            success = False
    
    return success

def downgrade_schema(schema: str, target: str) -> bool:
    """Downgrade a specific schema to target revision."""
    print(f"\n=== Downgrading schema '{schema}' to '{target}' ===")
    return run_alembic_command(['alembic', 'downgrade', target], schema) == 0

def show_status() -> None:
    """Show migration status for all schemas."""
    print("\n=== Migration Status ===")
    
    for schema in ALL_SCHEMAS:
        print(f"\nSchema: {schema}")
        print("-" * 40)
        run_alembic_command(['alembic', 'current'], schema)
        print()

def show_history() -> None:
    """Show migration history."""
    print("\n=== Migration History ===")
    run_alembic_command(['alembic', 'history'])

def create_revision(message: str, schema: Optional[str] = None, autogenerate: bool = False) -> bool:
    """Create a new migration revision."""
    command = ['alembic', 'revision', '-m', message]
    if autogenerate:
        command.append('--autogenerate')

    if schema:
        print(f"\n=== Creating revision for schema '{schema}' ===")
        return run_alembic_command(command, schema) == 0
    else:
        print(f"\n=== Creating revision ===")
        return run_alembic_command(command) == 0

def stamp_schema(schema: str, revision: str) -> bool:
    """Stamp a specific schema with a revision."""
    print(f"\n=== Stamping schema '{schema}' with revision '{revision}' ===")
    return run_alembic_command(['alembic', 'stamp', revision], schema) == 0

def stamp_all_schemas(revision: str) -> bool:
    """Stamp all schemas with a revision."""
    print(f"\n=== Stamping all schemas with revision '{revision}' ===")
    success = True

    for schema in ALL_SCHEMAS:
        if not stamp_schema(schema, revision):
            print(f"Failed to stamp {schema} schema")
            success = False

    return success

def validate_schema(schema: str) -> bool:
    """Validate that the schema is supported."""
    if schema not in ALL_SCHEMAS:
        print(f"Error: Schema '{schema}' is not supported. Supported schemas: {', '.join(ALL_SCHEMAS)}")
        return False
    return True

def main():
    parser = argparse.ArgumentParser(description='Multi-Schema Migration Management')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Upgrade command
    upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade database schema')
    upgrade_parser.add_argument('--schema', help='Specific schema to upgrade')
    upgrade_parser.add_argument('--all', action='store_true', help='Upgrade all schemas')
    upgrade_parser.add_argument('--target', default='head', help='Target revision (default: head)')
    
    # Downgrade command
    downgrade_parser = subparsers.add_parser('downgrade', help='Downgrade database schema')
    downgrade_parser.add_argument('--schema', required=True, help='Specific schema to downgrade')
    downgrade_parser.add_argument('--target', required=True, help='Target revision')
    
    # Status command
    subparsers.add_parser('status', help='Show migration status for all schemas')
    
    # History command
    subparsers.add_parser('history', help='Show migration history')
    
    # Revision command
    revision_parser = subparsers.add_parser('revision', help='Create new migration revision')
    revision_parser.add_argument('--message', '-m', required=True, help='Migration message')
    revision_parser.add_argument('--schema', help='Specific schema for the revision')
    revision_parser.add_argument('--autogenerate', action='store_true', help='Auto-generate migration')

    # Stamp command
    stamp_parser = subparsers.add_parser('stamp', help='Stamp database with specific revision')
    stamp_parser.add_argument('--schema', help='Specific schema to stamp')
    stamp_parser.add_argument('--all', action='store_true', help='Stamp all schemas')
    stamp_parser.add_argument('--revision', required=True, help='Revision to stamp')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == 'upgrade':
            if args.all:
                success = upgrade_all_schemas(args.target)
            elif args.schema:
                if not validate_schema(args.schema):
                    return 1
                success = upgrade_schema(args.schema, args.target)
            else:
                print("Error: Must specify either --schema or --all")
                return 1
            
            return 0 if success else 1
        
        elif args.command == 'downgrade':
            if not validate_schema(args.schema):
                return 1
            success = downgrade_schema(args.schema, args.target)
            return 0 if success else 1
        
        elif args.command == 'status':
            show_status()
            return 0
        
        elif args.command == 'history':
            show_history()
            return 0
        
        elif args.command == 'revision':
            if args.schema and not validate_schema(args.schema):
                return 1
            success = create_revision(args.message, args.schema, args.autogenerate)
            return 0 if success else 1

        elif args.command == 'stamp':
            if args.all:
                success = stamp_all_schemas(args.revision)
            elif args.schema:
                if not validate_schema(args.schema):
                    return 1
                success = stamp_schema(args.schema, args.revision)
            else:
                print("Error: Must specify either --schema or --all")
                return 1

            return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
