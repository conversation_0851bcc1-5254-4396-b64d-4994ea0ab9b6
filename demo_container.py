from dataclasses import dataclass, field
from typing import Optional, Any, Dict

from dependency_injector import containers, providers
from pykeepass import PyKeePass

@dataclass
class EntryDetails:
    username: str
    password: str
    url: str | None = None
    custom_properties: Dict[str, Any] = field(default_factory=dict)

    def __repr__(self) -> str:
        return (f"EntryDetails(username={self.username!r}, password='****', "
                f"url={self.url!r}, custom_properties={self.custom_properties!r})")

    def __str__(self) -> str:
        return (f"EntryDetails:\n"
                f"  Username: {self.username}\n"
                f"  Password: ****\n"
                f"  URL: {self.url}\n"
                f"  Custom Properties: {self.custom_properties}")



class EntryDetailsBuilder:
    def __init__(self):
        # self._entry_details = EntryDetails(username='', password='')
        self._username: str | None = None
        self._password: str | None = None
        self._url: str | None = None
        self._custom_properties: Dict[str, Any] = {}

    def set_username(self, username: str) -> 'EntryDetailsBuilder':
        # self._entry_details.username = username
        self._username = username
        return self

    def set_password(self, password: str) -> 'EntryDetailsBuilder':
        # self._entry_details.password = password
        self._password = password
        return self

    def set_url(self, url: Optional[str]) -> 'EntryDetailsBuilder':
        # self._entry_details.url = url
        self._url = url
        return self

    def add_custom_property(self, key: str, value: Any) -> 'EntryDetailsBuilder':
        # self._entry_details.custom_properties[key] = value
        self._custom_properties[key] = value
        return self

    def set_rw_connection(self, host: str, port: int, db_name: str) -> 'EntryDetailsBuilder':
        self._custom_properties.update({
            "DB_SERVER_NAME": host,
            "DB_SERVER_RW_PORT": port,
            "DB_NAME": db_name,
        })
        return self

    def set_ro_connection(self, host: str, port: int, db_name: str) -> 'EntryDetailsBuilder':
        self._custom_properties.update({
            "DB_SERVER_NAME": host,
            "DB_SERVER_RO_PORT": port,
            "DB_NAME": db_name,
        })
        return self

    def build(self) -> EntryDetails:
        # return self._entry_details
        if not self._username or not self._password:
            raise ValueError("Username and password must be set before building EntryDetails")
        return EntryDetails(
            username=self._username,
            password=self._password,
            url=self._url,
            custom_properties=self._custom_properties
        )

def build_entry_details(keepass_manager: PyKeePass, title: str) -> EntryDetails:
    """Logic to retrieve and build EntryDetails, previously in get_kp_entry_details."""
    entry = keepass_manager.find_entries(title=title, first=True)
    if not entry:
        raise LookupError(f"No entry found with given title {title}")
    try:
        builder = (
            EntryDetailsBuilder()
            .set_username(entry.username or "")
            .set_password(entry.password or "")
            .set_url(entry.url)
        )

        # for custom_property in entry.custom_properties:
        #     builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))
        for key, value in (entry.custom_properties or {}).items():
            builder.add_custom_property(key, value)

        return builder.build()

    except ValueError as ve:
        raise RuntimeError(f"Invalid KeePass entry '{title}': {ve}") from ve
    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


class DynamicSchemaContainer(containers.DynamicContainer):
    def __init__(self, config, keepass_manager):
        super().__init__()
        self.config = config
        self.keepass_manager = keepass_manager


        # Jira
        self.jira_entry_details = providers.Factory(
            build_entry_details,
            keepass_manager=self.keepass_manager,
            title=self.config['entries']['jira']
        )

        # Schemas
        for schema in self.config['schemas']:
            for mode in ['rw', 'ro']:
                setattr(
                    self,
                    f"{schema}_{mode}",
                    providers.Factory(
                        build_entry_details,
                        keepass_manager=self.keepass_manager,
                        title=f"{schema}_{mode}"  # This matches KeePass entry title
                    )
                )


if __name__ == "__main__":
    import yaml
    with open("dags/data_pipeline/config.yaml", "r") as file:
        config = yaml.safe_load(file)

    # Create KeePass manager (example)
    print(config)
    keepass_manager = PyKeePass(r"C:\KeePass\Database.kdbx", keyfile=r"C:\KeePass\Database.key")
    # Build container
    schemas_container = DynamicSchemaContainer(config, keepass_manager)

    # Call Jira provider
    jira_details = schemas_container.jira_entry_details()

    # Call RW/RO for a specific schema
    plat_rw_details = schemas_container.plat_rw()
    public_ro_details = schemas_container.public_ro()
    print(jira_details)
    print(plat_rw_details)
    print(public_ro_details)
