#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to reset Alembic state by clearing all alembic_version tables.
This is needed when we change the migration structure significantly.
"""

import sys
import os
from sqlalchemy import create_engine, text
from alembic.config import Config

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def reset_alembic_state():
    """Reset alembic state by clearing version tables in all schemas."""
    
    # Get database URL from alembic.ini
    alembic_cfg = Config("alembic.ini")
    database_url = alembic_cfg.get_main_option("sqlalchemy.url")
    
    engine = create_engine(database_url)
    
    schemas = ['public', 'plat', 'plp', 'acq']
    
    with engine.connect() as connection:
        with connection.begin():
            for schema in schemas:
                try:
                    # Try to delete from alembic_version table in each schema
                    connection.execute(text(f"DELETE FROM {schema}.alembic_version"))
                    print(f"✅ Cleared alembic_version table in {schema} schema")
                except Exception as e:
                    print(f"⚠️  Could not clear {schema}.alembic_version: {e}")
            
            # Also try to drop the tables entirely
            for schema in schemas:
                try:
                    connection.execute(text(f"DROP TABLE IF EXISTS {schema}.alembic_version"))
                    print(f"✅ Dropped alembic_version table in {schema} schema")
                except Exception as e:
                    print(f"⚠️  Could not drop {schema}.alembic_version: {e}")

if __name__ == "__main__":
    print("🔄 Resetting Alembic state...")
    reset_alembic_state()
    print("✅ Alembic state reset complete!")
