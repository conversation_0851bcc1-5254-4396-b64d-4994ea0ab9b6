import sys
import os
import asyncio

# https://stackoverflow.com/questions/67631/how-to-import-a-module-given-the-full-path
sys.path.append('/opt/airflow/dags/airflow')
sys.path.append('/home/<USER>/dags/')
sys.path.append('/opt/airflow/dags/airflow/data_pipeline')
sys.path.append('/opt/airflow/dags/airflow/data_pipeline/dbmodels')
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from datetime import datetime, timedelta
from airflow.decorators import dag, task
from airflow.operators.python import PythonOperator
from airflow.models.param import Param

from dags.data_pipeline import utility_code as uc

# Project list for processing
project_list = ["plat", "acq", "plp"]

# Define the default arguments for the DAG
default_args = {
    "owner": "airflow",
    'depends_on_past': False,
    "start_date": datetime(2023, 1, 1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

# Schedule: Monday to Friday every 3 hours between 8 AM IST to 6 PM IST (2:30 AM to 12:30 PM UTC)
# And 2 times between 6 PM IST to 8 AM IST (12:30 PM to 2:30 AM UTC)
# IST is UTC+5:30
# 8 AM IST = 2:30 AM UTC, 11 AM IST = 5:30 AM UTC, 2 PM IST = 8:30 AM UTC, 5 PM IST = 11:30 AM UTC
# Night runs at 9 PM IST (3:30 PM UTC) and 3 AM IST (9:30 PM UTC)
schedule_interval = '30 2,5,8,11,15,21 * * 1-5'

# Task functions
@task
def get_jira_users():
    """Get Jira users and store them in the database"""
    return asyncio.run(uc.get_jira_users())

@task
def process_jira_issues(project, scope="project", initial_load=True):
    """Process Jira issues for a specific project"""
    return asyncio.run(uc.process_jira_issues(project, scope, initial_load))

@task
def get_fields():
    """Get Jira fields and store them in the database"""
    return uc.get_fields()

@task
def create_db_extension():
    """Create database extensions"""
    return uc.create_db_extension()

@task
def create_schema_tables_ddl(schema_name):
    """Create schema and tables for a specific schema"""
    return uc.create_schema_tables_ddl(schema_name)

@task
def get_all_jira_boards():
    """Get all Jira boards and store them in the database"""
    from archived import archived_code
    return archived_code.add_to_db_all_jira_boards()

@task
def process_jira_versions(project):
    """Process Jira versions for a specific project"""
    from archived import archived_code
    return asyncio.run(archived_code.process_jira_versions(project_key=project))

@task
def get_sprint_details(project):
    """Get sprint details for a specific project"""
    from archived import archived_code
    return archived_code.insert_sprint_details(project)

@task
def delete_worklog(project):
    """Delete worklog for a specific project"""
    from archived import archived_code
    return archived_code.del_deleted_worklog(project)

@task
def upsert_issue_classification(project):
    """Upsert issue classification for a specific project"""
    from archived import archived_code
    return archived_code.upsert_issue_classification(project)

@task
def create_refresh_mv(project_list):
    """Create or refresh materialized views"""
    return uc.create_refresh_mv(project_list)

@task
def get_deleted_worklog():
    """Get deleted worklog entries"""
    return asyncio.run(uc.get_deleted_worklog())

@dag(
    dag_id="jira_dag",
    schedule=schedule_interval,
    default_args=default_args,
    tags=['JIRA', 'REST API', 'Airflow3.0'],
    catchup=False,
    doc_md="""
    # Jira Data Pipeline DAG

    This DAG extracts data from Jira and loads it into a PostgreSQL database.

    ## Schedule
    - Monday to Friday every 3 hours between 8 AM IST to 6 PM IST
    - Two times between 6 PM IST to 8 AM IST

    ## Flow
    1. Create database extensions
    2. Create schema and tables
    3. Get deleted worklog entries
    4. Get Jira fields
    5. Get Jira users
    6. Get all Jira boards
    7. Process Jira versions for each project
    8. Get sprint details for each project
    9. Process Jira issues for each project
    10. Upsert issue classification for each project
    11. Delete worklog for each project
    12. Create or refresh materialized views
    """
)
def jira_data_pipeline():
    """
    DAG to extract data from Jira and load it into a PostgreSQL database
    """
    # Create database extensions
    db_extension = create_db_extension()

    # Create schema and tables for each project
    schema_tables = create_schema_tables_ddl("plat")

    # Get deleted worklog entries
    deleted_worklog = get_deleted_worklog()

    # Get Jira fields
    fields = get_fields()

    # Get Jira users
    users = get_jira_users()

    # Get all Jira boards
    boards = get_all_jira_boards()

    # Process Jira versions for each project
    versions_tasks = []
    for project in project_list:
        versions_tasks.append(process_jira_versions(project))

    # Get sprint details for each project
    sprint_tasks = []
    for project in project_list:
        sprint_tasks.append(get_sprint_details(project))

    # Process Jira issues for each project
    issues_tasks = []
    for project in project_list:
        issues_tasks.append(process_jira_issues(project, 'project', True))

    # Upsert issue classification for each project
    classification_tasks = []
    for project in project_list:
        classification_tasks.append(upsert_issue_classification(project))

    # Delete worklog for each project
    worklog_tasks = []
    for project in project_list:
        worklog_tasks.append(delete_worklog(project))

    # Create or refresh materialized views
    refresh_mv = create_refresh_mv(project_list)

    # Define the task dependencies
    db_extension >> schema_tables >> deleted_worklog
    schema_tables >> fields >> users >> boards >> versions_tasks >> sprint_tasks >> issues_tasks >> classification_tasks >> worklog_tasks >> refresh_mv

# Instantiate the DAG
jira_dag = jira_data_pipeline()
