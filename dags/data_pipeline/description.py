from pyadf.document import Document

doc = Document()                           \
    .paragraph()                           \
        .emoji("success")                  \
        .text(' This is my document, ')    \
        .text('this is a link')            \
            .link('https://atlassian.com') \
    .end()                                 \
    .to_doc()


# print(dir(doc))
print(doc)
for key, value in doc.items():
    print(key, value)
    if isinstance(value, dict):
        for k, v in value.items():
            print(k, v)
            if isinstance(v, list):
                for i in v:
                    print(i)
