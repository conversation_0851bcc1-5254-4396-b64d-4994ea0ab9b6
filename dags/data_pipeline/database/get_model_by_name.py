from typing import Type, Optional
from sqlalchemy.ext.declarative import DeclarativeMeta

from dags.data_pipeline.dbmodels.base import Base


def get_model_by_name(model_name: str, schema: Optional[str] = None) -> Type[DeclarativeMeta]:
    """
    Get a SQLAlchemy model class by name and optional schema.

    Args:
        model_name: The name of the model class
        schema: Optional schema name to match

    Returns:
        The model class

    Raises:
        ValueError: If model is not found
    """
    # Handle weak references in the registry
    for name, cls_ref in Base.registry._class_registry.items():
        # Handle weak references
        if hasattr(cls_ref, '__call__'):
            cls = cls_ref()  # Call weak reference
        else:
            cls = cls_ref

        # Skip None entries (garbage collected weak references)
        if cls is None:
            continue

        # Check if it's a proper class and matches the name
        # The registry can contain either class types or class instances
        if isinstance(cls, type):
            # It's a class type
            if issubclass(cls, Base) and cls.__name__ == model_name:
                if schema is None:
                    return cls
                # Check schema match
                table_schema = getattr(cls.__table__, "schema", None)
                if table_schema == schema:
                    return cls
        else:
            # It's a class instance, get the class type
            cls_type = type(cls)
            if issubclass(cls_type, Base) and cls_type.__name__ == model_name:
                if schema is None:
                    return cls_type
                # Check schema match
                table_schema = getattr(cls_type.__table__, "schema", None)
                if table_schema == schema:
                    return cls_type

    schema_msg = f" with schema '{schema}'" if schema else ""
    raise ValueError(f"Model '{model_name}'{schema_msg} not found.")

if __name__ == "__main__":
    model = get_model_by_name("IssueLinks")
    print(model)
