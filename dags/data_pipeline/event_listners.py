# Source: https://docs.sqlalchemy.org/en/20/_modules/examples/extending_query/filter_public.html


# Check this code if it works
# Function to print registered event listeners
# def print_registered_listeners(target, event_name):
#     listeners = getattr(target.dispatch, event_name).listeners
#     for listener in listeners:
#         print(listener)
#
# # Print listeners for the 'do_orm_execute' event on Session
# print_registered_listeners(Session, 'do_orm_execute')