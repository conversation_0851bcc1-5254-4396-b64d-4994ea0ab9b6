import asyncio
import random
import aiohttp
from datetime import datetime, timezone
from aiohttp import ClientError
from dateutil import parser

MAX_RETRY_DELAY = 30000  # in milliseconds
JITTER_MULTIPLIER_RANGE = (0.5, 1.5)


async def calculate_retry_delay(
        response: aiohttp.ClientResponse, last_retry_delay: int
) -> int:
    """
    Calculate the delay for the next retry attempt.

    :param response: The HTTP response object.
    :param last_retry_delay: The last retry delay in milliseconds.
    :return: The new retry delay in milliseconds.
    """
    retry_delay = -1

    # Check for the Retry-After header
    if "Retry-After" in response.headers:
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Check for the X-RateLimit-Reset header
    elif "X-RateLimit-Reset" in response.headers:
        reset_time = parser.parse(response.headers["X-RateLimit-Reset"])
        current_time = datetime.now(timezone.utc)
        wait_time = (reset_time - current_time).total_seconds() * 1000  # Convert to milliseconds
        retry_delay = max(wait_time, last_retry_delay)

    # If no headers are present but 429 status is received, double the last delay
    elif response.status == 429:
        retry_delay = min(2 * last_retry_delay, MAX_RETRY_DELAY)

    # Apply jitter
    if retry_delay > 0:
        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        retry_delay += int(retry_delay * jitter)

    return retry_delay


class RestClientAioHttp:
    def __init__(
            self, base_url: str,
            username: str | None = None, password: str | None = None,
            max_retries: int = 3, backoff_factor: int = 2
    ):
        self.base_url = base_url
        if username:
            self.auth = aiohttp.BasicAuth(username, password)
        else:
            self.auth = None
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor

    async def _request(
            self, method, endpoint, params=None, data=None, headers=None
    ):
        for retry in range(self.max_retries):
            try:
                url = f"{self.base_url}/{endpoint}"
                headers = headers or {}
                async with aiohttp.ClientSession(auth=self.auth, headers=headers) as session:
                    response = await session.request(
                        method, url, params=params, json=data,
                    )
                    response.raise_for_status()
                    data = await response.json()
                    return data
            except ClientError as e:
                if retry < self.max_retries - 1:
                    wait_time = self.backoff_factor ** retry
                    await asyncio.sleep(wait_time)
                else:
                    raise e

    async def get(self, endpoint, params=None, headers=None):
        return await self._request('GET', endpoint, params=params, headers=None)

    async def post(self, endpoint, data=None, headers=None):
        return await self._request('POST', endpoint, data=data, headers=headers)

    async def put(self, endpoint, data=None, headers=None):
        return await self._request('PUT', endpoint, data=data, headers=headers)

    async def delete(self, endpoint, params=None, headers=None):
        return await self._request('DELETE', endpoint, params=params, headers=headers)
