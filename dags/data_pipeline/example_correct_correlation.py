#!/usr/bin/env python3
# coding=utf-8
"""
Example showing the correct way to initialize correlation tracking.
This demonstrates the proper initialization order to avoid "N/A" correlation IDs.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))


def main_application_setup():
    """
    Example of correct application setup with correlation tracking.
    This is the pattern you should follow in your main application.
    """
    print("🚀 Setting up application with proper correlation tracking...")
    
    # Step 1: Import required modules
    from containers import ApplicationContainer, initialize_database_logging
    from custom_logger import CorrelationContext
    
    # Step 2: Initialize the application container
    container = ApplicationContainer()
    
    # Step 3: Wire the container for dependency injection
    container.wire(modules=[__name__])
    
    # Step 4: Initialize logging resources (CRITICAL - this installs correlation factory)
    print("📋 Initializing logging resources...")
    container.logger_container.init_resources()
    
    # Step 5: Configure database logging (optional)
    print("📋 Configuring database logging...")
    db_handlers = initialize_database_logging(
        container=container,
        schema='plat',  # Use your preferred schema
        enabled=True
    )
    print(f"✅ Database logging configured with {len(db_handlers)} handlers")
    
    # Step 6: Get logger from container
    logger = container.logger_container.logger()
    
    print("✅ Application setup completed")
    return container, logger


def example_synchronous_operations():
    """Example of synchronous operations with correlation tracking."""
    print("\n🧪 Testing synchronous operations...")
    
    container, logger = main_application_setup()
    
    # Example 1: Single operation with correlation
    with CorrelationContext("user_registration") as ctx:
        logger.info("Starting user registration process")
        logger.info("Validating user input")
        logger.warning("Email domain not in whitelist - proceeding anyway")
        logger.info("Creating user account in database")
        logger.info("Sending welcome email")
        logger.info("User registration completed successfully")
        print(f"   Correlation ID used: {ctx.correlation_id}")
    
    # Example 2: Different operation with different correlation
    with CorrelationContext("data_export") as ctx:
        logger.info("Starting data export process")
        logger.info("Querying database for export data")
        logger.info("Formatting data for export")
        logger.info("Writing export file")
        logger.info("Data export completed")
        print(f"   Correlation ID used: {ctx.correlation_id}")
    
    # Example 3: Operation without correlation (should show N/A)
    logger.info("This log message has no correlation context")
    
    print("✅ Synchronous operations completed")


async def example_asynchronous_operations():
    """Example of asynchronous operations with correlation tracking."""
    print("\n🧪 Testing asynchronous operations...")
    
    container, logger = main_application_setup()
    
    async def process_batch(batch_id, items):
        """Process a batch of items with correlation tracking."""
        async with CorrelationContext(f"batch_processing_{batch_id}") as ctx:
            logger.info(f"Starting batch {batch_id} processing ({len(items)} items)")
            
            for i, item in enumerate(items):
                logger.info(f"Processing item {i+1}/{len(items)}: {item}")
                await asyncio.sleep(0.01)  # Simulate async work
            
            logger.info(f"Batch {batch_id} processing completed")
            return ctx.correlation_id
    
    # Process multiple batches concurrently
    batches = [
        (1, ["item1", "item2", "item3"]),
        (2, ["item4", "item5"]),
        (3, ["item6", "item7", "item8", "item9"])
    ]
    
    tasks = [process_batch(batch_id, items) for batch_id, items in batches]
    correlation_ids = await asyncio.gather(*tasks)
    
    print(f"   Processed {len(batches)} batches with correlation IDs: {correlation_ids}")
    print("✅ Asynchronous operations completed")


def example_error_handling():
    """Example of error handling with correlation tracking."""
    print("\n🧪 Testing error handling with correlation...")
    
    container, logger = main_application_setup()
    
    with CorrelationContext("error_handling_demo") as ctx:
        logger.info("Starting operation that will encounter errors")
        
        try:
            logger.info("Attempting risky operation")
            # Simulate an error
            raise ValueError("Simulated error for demonstration")
        except ValueError as e:
            logger.error(f"Operation failed: {e}", exc_info=True)
            logger.info("Attempting recovery")
            logger.info("Recovery successful")
        
        logger.info("Error handling demonstration completed")
        print(f"   All error logs have correlation ID: {ctx.correlation_id}")
    
    print("✅ Error handling completed")


def example_nested_operations():
    """Example of nested operations sharing correlation context."""
    print("\n🧪 Testing nested operations...")
    
    container, logger = main_application_setup()
    
    def validate_data(data):
        """Nested function that uses the same correlation context."""
        logger.info(f"Validating data: {data}")
        if not data:
            logger.warning("Empty data detected")
            return False
        logger.info("Data validation passed")
        return True
    
    def process_data(data):
        """Another nested function using the same correlation context."""
        logger.info(f"Processing validated data: {data}")
        # Simulate processing
        result = data.upper()
        logger.info(f"Processing completed, result: {result}")
        return result
    
    # All nested operations share the same correlation ID
    with CorrelationContext("nested_operations_demo") as ctx:
        logger.info("Starting nested operations demonstration")
        
        test_data = "sample data"
        
        if validate_data(test_data):
            result = process_data(test_data)
            logger.info(f"Final result: {result}")
        else:
            logger.error("Data validation failed")
        
        logger.info("Nested operations demonstration completed")
        print(f"   All nested operations shared correlation ID: {ctx.correlation_id}")
    
    print("✅ Nested operations completed")


async def run_all_examples():
    """Run all correlation tracking examples."""
    print("🚀 Correlation Tracking Examples")
    print("=" * 50)
    
    # Set up environment
    os.environ.setdefault("ENABLE_DATABASE_LOGGING", "true")
    os.environ.setdefault("DB_LOG_BATCH_SIZE", "10")
    
    try:
        # Run examples
        example_synchronous_operations()
        await example_asynchronous_operations()
        example_error_handling()
        example_nested_operations()
        
        print("=" * 50)
        print("🎉 All correlation tracking examples completed!")
        
        print("\n📊 What you should see in the logs:")
        print("- ✅ Correlation IDs that are NOT 'N/A'")
        print("- ✅ Same correlation ID for related operations")
        print("- ✅ Different correlation IDs for different operations")
        print("- ✅ Task IDs and coroutine names for async operations")
        print("- ✅ 'N/A' only for operations without correlation context")
        
        print("\n🔍 Log format example:")
        print("2024-01-15T10:30:45.123 INFO [abc12345] [task_789:process_batch] function_name 0042 | Processing started")
        print("                                    ↑         ↑        ↑")
        print("                            Correlation  Task ID  Coroutine")
        print("                                 ID                  Name")
        
    except Exception as e:
        print(f"\n❌ Examples failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(run_all_examples())
    except KeyboardInterrupt:
        print("\n⚠️ Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Examples failed with error: {e}")
        import traceback
        traceback.print_exc()
