import uuid
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, List, Any

from sqlalchemy import update, select
from sqlalchemy.ext.asyncio import AsyncSession

from dags.data_pipeline.dbmodels.task_monitoring import TaskStateChange, TaskExecution, TaskAwaitEvent, \
    TaskStackSnapshot, TaskMetrics


class TaskMonitoringDB:
    """Database integration for task monitoring"""

    def __init__(self, async_session: AsyncSession):
        self.session = async_session

    async def create_task_execution(self, task_name: str, session_id: uuid.UUID,
                                    process_id: str) -> tuple[uuid.UUID, datetime]:
        """Create a new task execution record"""
        start_time = datetime.now(timezone.utc)
        task_execution = TaskExecution(
            task_name=task_name,
            session_id=session_id,
            process_id=process_id,
            start_time=start_time,
            initial_state='PENDING'
        )

        self.session.add(task_execution)
        await self.session.flush()
        # Don't commit here - let the caller manage the transaction
        # Just flush to ensure the record is available for foreign key references
        return task_execution.id, start_time

    async def record_state_change(self, task_execution_id: uuid.UUID, task_start_time: datetime,
                                  from_state: Optional[str], to_state: str,
                                  cpu_percent: Optional[float] = None,
                                  memory_mb: Optional[float] = None,
                                  context_info: Optional[Dict] = None):
        """Record a task state change"""
        timestamp = datetime.now(timezone.utc)

        try:
            state_change = TaskStateChange(
                task_execution_id=task_execution_id,
                task_start_time=task_start_time,
                from_state=from_state,
                to_state=to_state,
                timestamp=timestamp,
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                context_info=context_info
            )

            self.session.add(state_change)
            await self.session.flush()
            return state_change.id
        except Exception as e:
            # Log the error but don't fail the monitoring
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to record state change for task {task_execution_id}: {e}")
            # Return None to indicate failure
            return None

    async def record_await_event(self, task_execution_id: uuid.UUID, task_start_time: datetime,

                                 await_location: Optional[str] = None,
                                 await_target: Optional[str] = None,
                                 await_category: Optional[str] = None) -> int:
        """Start recording an await event"""
        await_start = datetime.now(timezone.utc)
        await_event = TaskAwaitEvent(
            task_execution_id=task_execution_id,
            task_start_time=task_start_time,
            await_start=await_start,
            await_location=await_location,
            await_target=await_target,
            await_category=await_category
        )

        self.session.add(await_event)
        await self.session.flush()
        return await_event.id

    async def finish_await_event(self, await_event_id: int):
        """Finish an await event"""
        await_end = datetime.now(timezone.utc)

        # await_event = await self.session.get(TaskAwaitEvent, await_event_id)
        # if await_event and not await_event.await_end:
        #     await_event.await_end = datetime.now(timezone.utc)
        #     await_event.await_duration = (
        #             await_event.await_end - await_event.await_start
        #     ).total_seconds()
        #     await self.session.flush()
        # Update the await event
        stmt = update(TaskAwaitEvent).where(
            TaskAwaitEvent.id == await_event_id
        ).values(
            await_end=await_end,
            await_duration=None  # Will be calculated by trigger or application logic
        )

        await self.session.execute(stmt)
        await self.session.flush()

    async def record_stack_snapshot(self, task_execution_id: uuid.UUID,
                                    task_start_time: datetime,
                                    stack_frames: List[Dict], task_state: str,
                                    current_file: Optional[str] = None,
                                    current_line: Optional[int] = None,
                                    current_function: Optional[str] = None):
        """Record a stack snapshot"""
        timestamp = datetime.now(timezone.utc)
        snapshot = TaskStackSnapshot(
            task_execution_id=task_execution_id,
            task_start_time=task_start_time,
            timestamp=timestamp,
            stack_frames=stack_frames,
            stack_depth=len(stack_frames),
            task_state=task_state,
            current_file=current_file,
            current_line=current_line,
            current_function=current_function
        )

        self.session.add(snapshot)
        await self.session.flush()
        return snapshot.id

    async def finish_task_execution(self, task_execution_id: uuid.UUID,
                                    task_start_time: datetime,
                                    final_state: str, duration: float,
                                    result_type: Optional[str] = None,
                                    result_summary: Optional[str] = None,
                                    exception_type: Optional[str] = None,
                                    exception_message: Optional[str] = None,
                                    peak_memory_mb: Optional[float] = None,
                                    avg_cpu_percent: Optional[float] = None):
        """Finish a task execution"""
        # task_execution = await self.session.get(TaskExecution, task_execution_id)
        # if task_execution:
        #     task_execution.end_time = datetime.now(timezone.utc)
        #     task_execution.final_state = final_state
        #     task_execution.duration = duration
        #     task_execution.result_type = result_type
        #     task_execution.result_summary = result_summary
        #     task_execution.exception_type = exception_type
        #     task_execution.exception_message = exception_message
        #     task_execution.peak_memory_mb = peak_memory_mb
        #     task_execution.avg_cpu_percent = avg_cpu_percent
        #
        #     await self.session.flush()
        end_time = datetime.now(timezone.utc)

        stmt = update(TaskExecution).where(
            TaskExecution.id == task_execution_id,
            TaskExecution.start_time == task_start_time
        ).values(
            end_time=end_time,
            final_state=final_state,
            duration=duration,
            result_type=result_type,
            result_summary=result_summary,
            exception_type=exception_type,
            exception_message=exception_message,
            peak_memory_mb=peak_memory_mb,
            avg_cpu_percent=avg_cpu_percent
        )

        await self.session.execute(stmt)
        await self.session.flush()

    async def get_task_execution(self, task_execution_id: uuid.UUID,
                                 task_start_time: datetime) -> Optional[TaskExecution]:
        """Get a task execution by ID and start time"""
        stmt = select(TaskExecution).where(
            TaskExecution.id == task_execution_id,
            TaskExecution.start_time == task_start_time
        )

        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_tasks(self, session_id: Optional[uuid.UUID] = None) -> List[TaskExecution]:
        """Get currently active tasks"""
        # query = self.session.query(TaskExecution).filter(
        #     TaskExecution.end_time.is_(None)
        # )
        #
        # if session_id:
        #     query = query.filter(TaskExecution.session_id == session_id)
        #
        # result = await query.all()
        # return result
        stmt = select(TaskExecution).where(TaskExecution.end_time.is_(None))

        if session_id:
            stmt = stmt.where(TaskExecution.session_id == session_id)

        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def get_task_state_history(self, task_execution_id: uuid.UUID,
                                     task_start_time: datetime) -> List[TaskStateChange]:
        """Get state change history for a task"""
        stmt = select(TaskStateChange).where(
            TaskStateChange.task_execution_id == task_execution_id,
            TaskStateChange.task_start_time == task_start_time
        ).order_by(TaskStateChange.timestamp)

        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def get_task_await_events(self, task_execution_id: uuid.UUID,
                                    task_start_time: datetime) -> List[TaskAwaitEvent]:
        """Get await events for a task"""
        stmt = select(TaskAwaitEvent).where(
            TaskAwaitEvent.task_execution_id == task_execution_id,
            TaskAwaitEvent.task_start_time == task_start_time
        ).order_by(TaskAwaitEvent.await_start)

        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def get_task_stack_snapshots(self, task_execution_id: uuid.UUID,
                                       task_start_time: datetime) -> List[TaskStackSnapshot]:
        """Get stack snapshots for a task"""
        stmt = select(TaskStackSnapshot).where(
            TaskStackSnapshot.task_execution_id == task_execution_id,
            TaskStackSnapshot.task_start_time == task_start_time
        ).order_by(TaskStackSnapshot.timestamp)

        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def create_task_metrics(self, task_name: str, time_bucket: datetime,
                                  session_id: Optional[uuid.UUID] = None,
                                  **metrics_data) -> TaskMetrics:
        """Create or update task metrics"""
        task_metrics = TaskMetrics(
            task_name=task_name,
            time_bucket=time_bucket,
            session_id=session_id,
            **metrics_data
        )

        self.session.add(task_metrics)
        await self.session.flush()

        return task_metrics

    async def get_task_performance_summary(self, task_name: str,
                                           hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for a task"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

        # This would be a complex query - showing structure
        # In practice, you'd use raw SQL for better performance
        query = select(TaskExecution).filter(
            TaskExecution.task_name == task_name,
            TaskExecution.start_time >= cutoff_time
        )

        tasks = await self.session.execute(query)

        # Calculate summary statistics
        total_tasks = len(tasks)
        completed_tasks = [t for t in tasks if t.final_state == 'DONE']
        failed_tasks = [t for t in tasks if t.final_state == 'EXCEPTION']

        return {
            'total_executions': total_tasks,
            'success_rate': len(completed_tasks) / total_tasks if total_tasks > 0 else 0,
            'avg_duration': sum(t.duration for t in completed_tasks if t.duration) / len(
                completed_tasks) if completed_tasks else 0,
            'failure_count': len(failed_tasks)
        }