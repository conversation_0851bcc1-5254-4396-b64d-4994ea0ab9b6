{"fields": ["issuetype", "issuelinks", "comment", "parent", "summary", "description", "status", "statusCategory", "statuscategorychangedate", "assignee", "reporter", "fixVersions", "versions", "customfield_10020", "customfield_10024", "customfield_10067", "timetracking", "timeoriginalestimate", "aggregatetimeoriginalestimate", "timeestimate", "aggregatetimeestimate", "timespent", "aggregatetimespent", "progress", "aggregateprogress", "components", "customfield_10001", "resolution", "customfield_10019", "created", "resolutiondate", "updated", "customfield_10015", "duedate", "worklog", "customfield_10120", "customfield_10121", "customfield_10122", "customfield_10123", "customfield_10124", "customfield_10125", "customfield_10126", "customfield_10059", "customfield_10060", "customfield_10061", "customfield_10062", "customfield_10146", "customfield_10078", "customfield_10147", "priority", "customfield_10092", "customfield_10179", "customfield_10182", "customfield_10183", "customfield_10184", "customfield_10199", "customfield_10071", "customfield_10006", "customfield_10056", "customfield_10049", "customfield_10256"]}