# logging_utils.py
import asyncio
import logging
import threading

# Import the context variables - ensure we get the same module instance
def _get_context_vars():
    """Get context variables from the custom_logger module, handling different import paths."""
    import sys

    # Try to get the already imported module first
    if 'dags.data_pipeline.custom_logger' in sys.modules:
        module = sys.modules['dags.data_pipeline.custom_logger']
    elif 'custom_logger' in sys.modules:
        module = sys.modules['custom_logger']
    else:
        # Import it fresh
        try:
            from . import custom_logger as module
        except ImportError:
            try:
                import custom_logger as module
            except ImportError:
                import dags.data_pipeline.custom_logger as module

    return module.correlation_id_var, module.task_id_var, module.coroutine_name_var

# Get the context variables
correlation_id_var, task_id_var, coroutine_name_var = _get_context_vars()

# Global flag to ensure factory is only installed once
_factory_installed = False
_factory_lock = threading.Lock()


def install_correlation_log_record_factory():
    """Install a global log record factory that injects correlation metadata."""
    global _factory_installed

    with _factory_lock:
        if _factory_installed:
            return

        original_factory = logging.getLogRecordFactory()

        def correlation_record_factory(*args, **kwargs):
            record = original_factory(*args, **kwargs)

            # Get correlation information from context variables
            correlation_id = correlation_id_var.get()
            task_id = task_id_var.get()
            coroutine_name = coroutine_name_var.get()

            # If not in context, try to get current task info
            if not task_id or not coroutine_name:
                try:
                    loop = asyncio.get_running_loop()
                    task = asyncio.current_task(loop=loop)
                    if task:
                        if not task_id:
                            task_id = str(id(task))
                        if not coroutine_name:
                            coroutine_name = task.get_name()
                except RuntimeError:
                    # No event loop running
                    pass

            # Set the correlation information on the record
            record.correlation_id = correlation_id or "N/A"
            record.task_id = task_id or "N/A"
            record.coroutine_name = coroutine_name or "N/A"

            return record

        logging.setLogRecordFactory(correlation_record_factory)
        _factory_installed = True


def ensure_correlation_factory():
    """Ensure the correlation factory is installed (safe to call multiple times)."""
    if not _factory_installed:
        install_correlation_log_record_factory()
