# yaml-language-server: $schema=./schema/enhanced_fields.schema.json
fields:
  - id: id
    name: "Worklog ID"
    custom: false
    datatype: string
    source_column: id
    target_column: id
    target_type: int64

  - id: author
    name: "Author"
    custom: false
    datatype: string
    source_column: author.accountId
    target_column: author
    target_type: string

  - id: updateAuthor
    name: "Update Author"
    custom: false
    datatype: string
    source_column: updateAuthor.accountId
    target_column: updateauthor
    target_type: string

  - id: created
    name: "Created"
    custom: false
    datatype: string
    source_column: created
    target_column: created
    target_type: datetime

  - id: updated
    name: "Updated"
    custom: false
    datatype: string
    source_column: updated
    target_column: updated
    target_type: datetime

  - id: started
    name: "Started"
    custom: false
    datatype: string
    source_column: started
    target_column: started
    target_type: datetime
    
  - id: timeSpent
    name: "Time Spent"
    custom: false
    datatype: string
    target_type: string
    
  - id: timeSpentSeconds
    name: "Time Spent Seconds"
    custom: false
    datatype: int64
    target_type: int64

  - id: comment
    name: "Comment"
    custom: false
    datatype: json
    target_type: json

  - id: issue_key
    name: "Issue Key"
    custom: false
    datatype: string
    target_type: string

  - id: issue_id
    name: "Issue ID"
    custom: false
    source_column: issueId
    target_column: issue_id
    datatype: string
    target_type: int64

database_config:
  model: WorkLog
  no_update_cols: ("timeSpentHours",)

drop-column-prefixes:
  - self
  - author.
  - updateAuthor.

drop-column-exceptions:
  - author.accountId
  - updateAuthor.accountId
