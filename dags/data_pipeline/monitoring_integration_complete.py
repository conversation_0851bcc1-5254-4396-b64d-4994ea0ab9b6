import asyncio
import uuid
import time
import os
import psutil
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any, List

from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession
from contextlib import asynccontextmanager
from dags.data_pipeline.dbmodels.task_monitoring import TaskExecution
from dags.data_pipeline.task_monitoring_db import TaskMonitoringDB


class DatabaseTaskMonitor:
    """Enhanced task monitor with database persistence"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = TaskMonitoringDB(db_session)
        self.session_id = uuid.uuid4()
        self.process_id = str(os.getpid())
        self.task_executions: Dict[str, uuid.UUID] = {}
        self.task_start_times: Dict[str, datetime] = {}
        self.await_events: Dict[str, int] = {}
        self.previous_states: Dict[str, str] = {}
        self.task_creation_times: Dict[str, float] = {}
        self.monitoring = False
        self.monitor_task = None
        self.last_snapshot_time = 0
    
    async def start_monitoring(self, interval: float = 0.1):
        """Start monitoring with database persistence"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval), name="db_monitor")
    
    async def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False

        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                print(f"Error waiting for monitor task: {e}")
        
        # Finish all pending tasks
        for task_name in list(self.task_executions.keys()):
            try:
                await self._handle_completed_task(task_name)
            except Exception as e:
                print(f"Error finishing task {task_name}: {e}")
        
        # Commit any pending changes
        try:
            if self.db.session.in_transaction():
                await self.db.session.rollback()
            await self.db.session.commit()
        except Exception as e:
            print(f"Error committing changes: {e}")
            try:
                await self.db.session.rollback()
            except Exception as e:
                print(f"Error closing session: {e}")
    
    async def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                await self._update_task_states()
                await self.db.session.commit()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Monitor error: {e}")
                await asyncio.sleep(interval)
    
    async def _update_task_states(self):
        """Update task states and persist to database"""
        current_time = time.time()
        current_tasks = {task.get_name(): task for task in asyncio.all_tasks()}
        
        # Process each current task
        for task_name, task in current_tasks.items():
            current_state = self._get_task_state(task)
            
            # Check if this is a new task
            if task_name not in self.task_executions:
                await self._handle_new_task(task_name, task, current_state)
            
            # Check for state changes
            previous_state = self.previous_states.get(task_name)
            if previous_state != current_state:
                await self._handle_state_change(task_name, task, previous_state, current_state)
            
            # Handle await events
            await self._handle_await_events(task_name, task, current_state)
            
            # Record stack snapshots for pending tasks (less frequently)
            if current_state == 'PENDING' and current_time - self.last_snapshot_time > 5:
                await self._record_stack_snapshot(task_name, task, current_state)
                self.last_snapshot_time = current_time
            
            self.previous_states[task_name] = current_state
        
        # Handle completed tasks
        completed_tasks = set(self.task_executions.keys()) - set(current_tasks.keys())
        for task_name in completed_tasks:
            await self._handle_completed_task(task_name)
    
    async def _handle_new_task(self, task_name: str, task: asyncio.Task, current_state: str):
        """Handle a newly discovered task"""
        try:
            # Create task execution record
            task_execution_id, start_time = await self.db.create_task_execution(
                task_name=task_name,
                session_id=self.session_id,
                process_id=self.process_id
            )

            self.task_executions[task_name] = task_execution_id
            self.task_start_times[task_name] = start_time
            self.task_creation_times[task_name] = time.time()

            # Record initial state change
            state_change_id = await self.db.record_state_change(
                task_execution_id=task_execution_id,
                task_start_time=start_time,
                from_state=None,
                to_state=current_state,
                cpu_percent=psutil.Process().cpu_percent(),
                memory_mb=psutil.Process().memory_info().rss / 1024 / 1024
            )

            # Commit the transaction to ensure both records are persisted
            try:
                await self.db.session.commit()
            except Exception as commit_error:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to commit task creation for {task_name}: {commit_error}")
                # Try to rollback to clean state
                try:
                    await self.db.session.rollback()
                except Exception:
                    pass  # Ignore rollback errors

            if state_change_id is None:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to record initial state for task {task_name}")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to handle new task {task_name}: {e}")
            # Try to rollback the session to prevent it from being in a bad state
            try:
                await self.db.session.rollback()
            except Exception:
                pass  # Ignore rollback errors
            # Don't re-raise to avoid breaking the monitor loop
    
    async def _handle_state_change(self, task_name: str, task: asyncio.Task,
                                 previous_state: Optional[str], current_state: str):
        """Handle task state changes"""
        try:
            task_execution_id = self.task_executions[task_name]
            task_start_time = self.task_start_times[task_name]

            state_change_id = await self.db.record_state_change(
                task_execution_id=task_execution_id,
                task_start_time=task_start_time,
                from_state=previous_state,
                to_state=current_state,
                cpu_percent=psutil.Process().cpu_percent(),
                memory_mb=psutil.Process().memory_info().rss / 1024 / 1024
            )

            # Commit the state change
            if state_change_id is not None:
                try:
                    await self.db.session.commit()
                except Exception as commit_error:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Failed to commit state change for {task_name}: {commit_error}")
                    try:
                        await self.db.session.rollback()
                    except Exception:
                        pass

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to handle state change for {task_name}: {e}")
            try:
                await self.db.session.rollback()
            except Exception:
                pass
    
    async def _handle_await_events(self, task_name: str, task: asyncio.Task, current_state: str):
        """Handle await events"""
        task_execution_id = self.task_executions[task_name]
        task_start_time = self.task_start_times[task_name]

        if current_state == 'PENDING':
            # Task is awaiting - start tracking if not already
            if task_name not in self.await_events:
                await_location, await_target = self._get_await_info(task)
                await_category = self._categorize_await(await_target)
                
                await_event_id = await self.db.record_await_event(
                    task_execution_id=task_execution_id,
                    task_start_time=task_start_time,
                    await_location=await_location,
                    await_target=await_target,
                    await_category=await_category
                )
                
                self.await_events[task_name] = await_event_id
        
        else:
            # Task is not awaiting - finish tracking if we were
            if task_name in self.await_events:
                await_event_id = self.await_events.pop(task_name)
                await self.db.finish_await_event(await_event_id)
    
    async def _record_stack_snapshot(self, task_name: str, task: asyncio.Task, current_state: str):
        """Record a stack snapshot"""
        task_execution_id = self.task_executions[task_name]
        task_start_time = self.task_start_times[task_name]
        
        try:
            stack = task.get_stack()
            stack_frames = []
            current_file = None
            current_line = None
            current_function = None
            
            for frame in stack:
                frame_info = {
                    'filename': frame.f_code.co_filename,
                    'line_number': frame.f_lineno,
                    'function_name': frame.f_code.co_name,
                    'is_asyncio': 'asyncio' in frame.f_code.co_filename
                }
                stack_frames.append(frame_info)
                
                # Get current execution point (first non-asyncio frame)
                if not current_file and 'asyncio' not in frame.f_code.co_filename:
                    current_file = frame.f_code.co_filename
                    current_line = frame.f_lineno
                    current_function = frame.f_code.co_name
            
            await self.db.record_stack_snapshot(
                task_execution_id=task_execution_id,
                task_start_time=task_start_time,
                stack_frames=stack_frames,
                task_state=current_state,
                current_file=current_file,
                current_line=current_line,
                current_function=current_function
            )
        
        except Exception as e:
            print(f"Error recording stack snapshot: {e}")
    
    async def _handle_completed_task(self, task_name: str):
        """Handle a completed task"""
        task_execution_id = self.task_executions.pop(task_name)
        task_start_time = self.task_start_times.pop(task_name)
        start_time = self.task_start_times.pop(task_name, time.time())
        
        # Finish any pending await events
        if task_name in self.await_events:
            await_event_id = self.await_events.pop(task_name)
            await self.db.finish_await_event(await_event_id)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Get final state from previous states or default to DONE
        final_state = self.previous_states.get(task_name, 'DONE')
        
        # Get resource usage
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
        except:
            memory_mb = None
            cpu_percent = None
        
        await self.db.finish_task_execution(
            task_execution_id=task_execution_id,
            task_start_time=task_start_time,
            final_state=final_state,
            duration=duration,
            result_type='completed',
            result_summary=f'Task {task_name} completed',
            peak_memory_mb=memory_mb,
            avg_cpu_percent=cpu_percent
        )
    
    def _get_task_state(self, task: asyncio.Task) -> str:
        """Get task state"""
        if hasattr(task, '_state'):
            return task._state
        elif task.done():
            if task.cancelled():
                return 'CANCELLED'
            elif task.exception():
                return 'EXCEPTION'
            else:
                return 'DONE'
        else:
            return 'PENDING'
    
    def _get_await_info(self, task: asyncio.Task) -> tuple:
        """Get await information"""
        await_location = None
        await_target = None
        
        try:
            stack = task.get_stack()
            if stack:
                for frame in stack:
                    if 'asyncio' in frame.f_code.co_filename:
                        continue
                    
                    filename = frame.f_code.co_filename
                    lineno = frame.f_lineno
                    func_name = frame.f_code.co_name
                    
                    try:
                        with open(filename, 'r') as f:
                            lines = f.readlines()
                            if lineno <= len(lines):
                                line = lines[lineno - 1].strip()
                                if 'await' in line:
                                    await_location = f"{filename}:{lineno} ({func_name})"
                                    await_target = line
                                    break
                    except:
                        pass
        except:
            pass
        
        return await_location, await_target
    
    def _categorize_await(self, await_target: Optional[str]) -> Optional[str]:
        """Categorize await operations"""
        if not await_target:
            return None
        
        target_lower = await_target.lower()
        
        if any(keyword in target_lower for keyword in ['sleep', 'wait']):
            return 'timing'
        elif any(keyword in target_lower for keyword in ['http', 'request', 'fetch', 'get', 'post']):
            return 'network'
        elif any(keyword in target_lower for keyword in ['query', 'execute', 'select', 'insert', 'update', 'delete']):
            return 'database'
        elif any(keyword in target_lower for keyword in ['read', 'write', 'open', 'close', 'file']):
            return 'file_io'
        elif any(keyword in target_lower for keyword in ['redis', 'cache', 'memcached']):
            return 'cache'
        elif any(keyword in target_lower for keyword in ['queue', 'publish', 'subscribe', 'consume']):
            return 'messaging'
        elif any(keyword in target_lower for keyword in ['lock', 'semaphore', 'event', 'condition']):
            return 'synchronization'
        else:
            return 'other'
    
    async def get_task_stats(self) -> Dict[str, Any]:
        """Get current task statistics"""
        stats: Dict[str, Any] = {
            'active_tasks': len(self.task_executions),
            'awaiting_tasks': len(self.await_events),
            'session_id': str(self.session_id),
            'process_id': self.process_id,
            'monitoring': self.monitoring
        }
        
        # Get task states distribution
        current_tasks = {task.get_name(): task for task in asyncio.all_tasks()}
        states = {}
        for task_name, task in current_tasks.items():
            state = self._get_task_state(task)
            states[state] = states.get(state, 0) + 1
        
        stats['task_states'] = states
        return stats
    
    async def get_slow_tasks(self, min_duration: float = 1.0) -> List[Dict[str, Any]]:
        """Get tasks that have been running for a long time"""
        slow_tasks = []
        current_time = time.time()
        
        for task_name, start_time in self.task_start_times.items():
            if isinstance(start_time, datetime):
                start_ts = start_time.timestamp()
            else:
                start_ts = start_time
            duration = current_time - start_ts
            if duration >= min_duration:
                slow_tasks.append({
                    'task_name': task_name,
                    'duration': duration,
                    'start_time': start_time,
                    'state': self.previous_states.get(task_name, 'UNKNOWN')
                })
        
        return sorted(slow_tasks, key=lambda x: x['duration'], reverse=True)
    
    async def get_await_summary(self) -> Dict[str, Any]:
        """Get summary of current await operations"""
        await_summary = {}
        current_tasks = {task.get_name(): task for task in asyncio.all_tasks()}
        
        for task_name, task in current_tasks.items():
            if self._get_task_state(task) == 'PENDING':
                await_location, await_target = self._get_await_info(task)
                await_category = self._categorize_await(await_target)
                
                if await_category not in await_summary:
                    await_summary[await_category] = {
                        'count': 0,
                        'tasks': []
                    }
                
                await_summary[await_category]['count'] += 1
                await_summary[await_category]['tasks'].append({
                    'task_name': task_name,
                    'await_location': await_location,
                    'await_target': await_target
                })
        
        return await_summary


@asynccontextmanager
async def task_monitor(db_session: AsyncSession, interval: float = 0.1):
    """Context manager for database task monitoring"""
    monitor = DatabaseTaskMonitor(db_session)
    try:
        await monitor.start_monitoring(interval)
        yield monitor
    except Exception as e:
        raise
    finally:
        try:
            await monitor.stop_monitoring()
        except Exception as e:
            print(f"Error stopping monitoring: {e}")
            raise


# Example usage and testing functions
async def example_monitored_function():
    """Example function to demonstrate monitoring"""
    print("Starting example function")
    
    # Simulate some work
    await asyncio.sleep(0.5)
    
    # Simulate network request
    await asyncio.sleep(0.2)  # This would be an actual HTTP request
    
    # Simulate database query
    await asyncio.sleep(0.3)  # This would be an actual database query
    
    print("Example function completed")


async def run_monitoring_example():
    """Example of how to use the monitoring system"""
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    
    # Create database engine and session
    # engine = create_async_engine("postgresql+asyncpg://user:pass@localhost/db")
    engine = create_async_engine(URL.create(
        drivername="postgresql+asyncpg",
        username="postgres",
        password="Dope@123",
        host="localhost",
        port=5432,
        database="jira"
    ))
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        async with task_monitor(session, interval=0.1) as monitor:
            # Run some tasks
            tasks = [
                asyncio.create_task(example_monitored_function(), name="example_task_1"),
                asyncio.create_task(example_monitored_function(), name="example_task_2"),
                asyncio.create_task(example_monitored_function(), name="example_task_3")
            ]
            
            # Let them run for a bit
            await asyncio.sleep(0.5)
            
            # Check stats
            stats = await monitor.get_task_stats()
            print(f"Task stats: {stats}")
            
            # Check slow tasks
            slow_tasks = await monitor.get_slow_tasks(min_duration=0.3)
            print(f"Slow tasks: {slow_tasks}")
            
            # Check await summary
            await_summary = await monitor.get_await_summary()
            print(f"Await summary: {await_summary}")
            
            # Wait for tasks to complete
            await asyncio.gather(*tasks)


if __name__ == "__main__":
    asyncio.run(run_monitoring_example())
