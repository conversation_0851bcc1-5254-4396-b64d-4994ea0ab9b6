# Custom Logger Enhancements

This document outlines the comprehensive enhancements made to the `custom_logger.py` module to address the 5 key requirements and improve overall robustness.

## 🎯 Enhancement Summary

### 1. ✅ Async Correlation ID and Task Tracking

**Features Added:**
- **Correlation ID**: Unique identifier for grouping logs of a single operation
- **Task ID**: Asyncio Task ID for tracking async operations
- **Coroutine Name**: Name of the coroutine for better async debugging
- **Context Manager**: `CorrelationContext` for automatic correlation tracking

**Usage:**
```python
# Synchronous context
with CorrelationContext(operation_name="data_processing") as ctx:
    logger.info("Starting operation")  # Automatically includes correlation ID

# Asynchronous context
async with Correlation<PERSON>ontext(operation_name="async_processing") as ctx:
    logger.info("Async operation")  # Includes task ID and coroutine name
```

**Log Format Enhancement:**
```
2024-01-15T10:30:45.123 INFO [abc12345] [task_789:process_data] function_name 0042 | Processing started
```

### 2. ✅ PostgreSQL Database Logging Handler

**Features Added:**
- **Database Model**: `LogEntry` model with comprehensive fields
- **Monthly Partitioning**: Automatic table partitioning by month
- **Batch Processing**: Configurable batch size and flush intervals
- **Rich Metadata**: Captures correlation IDs, performance data, stack traces

**Database Schema:**
```sql
CREATE TABLE log_entry (
    id SERIAL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    level VARCHAR(20) NOT NULL,
    correlation_id VARCHAR(50),
    task_id VARCHAR(50),
    coroutine_name VARCHAR(100),
    message TEXT NOT NULL,
    extra_data JSONB,
    performance_data JSONB,
    -- ... additional fields
    PRIMARY KEY (id, timestamp)
) PARTITION BY RANGE (timestamp);
```

**Configuration (Programmatic):**
```python
# Initialize application container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Configure database logging after container setup
from containers import initialize_database_logging
db_handlers = initialize_database_logging(
    container=container,
    schema='your_schema',
    enabled=True
)
```

**Environment Variables:**
```bash
ENABLE_DATABASE_LOGGING=true
DB_LOG_BATCH_SIZE=50
DB_LOG_FLUSH_INTERVAL=30
DB_LOG_LEVEL=INFO
```

### 3. ✅ Cross-Platform Signal Handling

**Features Added:**
- **Platform Detection**: Automatic Windows/Unix detection
- **Windows Support**: Manual check methods for debugging
- **Unix Signals**: Traditional SIGUSR1/SIGUSR2 support
- **Unified Interface**: Same API across platforms

**Windows Usage:**
```python
# Manual debugging on Windows
debug_monitor.manual_check()          # System state check
debug_monitor.trigger_stack_dump()    # Stack trace dump
debug_handler.trigger_debug_dump()    # Combined debug info
```

**Unix Usage:**
```bash
# Signal-based debugging on Unix
kill -USR1 <pid>  # Stack traces
kill -USR2 <pid>  # System state dump
```

### 4. ✅ Configurable Monitoring Thresholds

**Features Added:**
- **Dynamic Thresholds**: Environment variable configuration
- **Smart Suppression**: Prevents excessive logging during long operations
- **Adaptive Intervals**: Configurable monitoring intervals
- **Threshold-Based Alerts**: Different log levels based on severity

**Environment Variables:**
```bash
ENABLE_DEBUG_MONITOR=true
DEBUG_MONITOR_INTERVAL=30
DEBUG_QUEUE_SIZE_WARNING=100
DEBUG_SLOW_OP_THRESHOLD=5.0
DEBUG_MEMORY_WARNING=80.0
DEBUG_LOG_SUPPRESSION=300
```

**Threshold Configuration:**
```python
threshold_config = {
    'max_queue_size_warning': 100,
    'slow_operation_threshold': 5.0,
    'memory_warning_threshold': 80.0,
    'log_suppression_interval': 300,
}
```

### 5. ✅ Unified Unicode Handling

**Consolidation:**
- **Merged Classes**: Combined `UnicodeAwareFilter`, `UniversalUnicodeFilter`, and `SafeUnicodeFormatter`
- **Single Handler**: `UnifiedUnicodeHandler` for all Unicode processing
- **Enhanced Mapping**: Comprehensive Unicode-to-ASCII mapping
- **Correlation Integration**: Automatic correlation info injection

**Features:**
- Cross-platform Unicode support (Windows/Unix)
- Automatic fallback to ASCII-safe alternatives
- Comprehensive emoji and symbol mapping
- Integrated correlation tracking

## 🔧 Implementation Details

### New Classes and Components

1. **`CorrelationContext`**: Context manager for correlation tracking
2. **`UnifiedUnicodeHandler`**: Consolidated Unicode processing
3. **`EnhancedTaskNameFilter`**: Enhanced task name and correlation filter
4. **`DatabaseLogHandler`**: PostgreSQL logging handler with dependency injection support
5. **`DatabaseLogHandlerFactory`**: Factory for creating database handlers
6. **`CrossPlatformDebugHandler`**: Cross-platform debugging support
7. **`LogEntry`**: Database model with partitioning support

### Dependency Injection Integration

The database logging system is designed to work seamlessly with the existing dependency injection framework:

```python
# LoggerContainer enhancement
class LoggerContainer(containers.DeclarativeContainer):
    @classmethod
    def configure_database_logging(cls, session_manager, schema='public'):
        """Configure database logging after container initialization."""
        # Configures database handlers for all loggers

# ApplicationContainer enhancement
class ApplicationContainer(containers.DeclarativeContainer):
    def configure_database_logging(self, schema=None, enabled=True):
        """Configure database logging for the application."""
        # Uses database_rw_managed session manager for logging
```

### Avoiding Circular Dependencies

The solution avoids circular dependencies by:
1. **Deferred Initialization**: Database handlers are created without session managers
2. **Post-Initialization Configuration**: Session managers are injected after container setup
3. **Factory Pattern**: `DatabaseLogHandlerFactory` creates handlers with proper dependencies
4. **Helper Functions**: `initialize_database_logging()` simplifies the setup process

### Enhanced Existing Classes

1. **`DebuggingMonitor`**: Added threshold configuration and platform detection
2. **`CustomFormatter`**: Enhanced with correlation field support
3. **`CustomLogger`**: Updated to use new unified handlers

### Database Integration

The `create_schema_tables_ddl` function in `utility_code.py` has been enhanced to:
- Create partitioned `log_entry` table
- Set up monthly partitions automatically
- Create appropriate indexes for performance
- Support multiple schemas

### Configuration Updates

The `logging_config.yaml` has been updated to:
- Include correlation IDs in log formats
- Add new filter configurations
- Support database logging handler
- Maintain backward compatibility

## 🚀 Usage Examples

### Setting Up Database Logging

```python
from containers import ApplicationContainer, initialize_database_logging

# Step 1: Initialize container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Step 2: Initialize basic logging
container.logger_container.init_resources()

# Step 3: Configure database logging
db_handlers = initialize_database_logging(
    container=container,
    schema='plat',  # Your preferred schema
    enabled=True
)

# Now all logging will go to both files and database
logger = container.logger_container.logger()
logger.info("This will be logged to database with correlation tracking")
```

### Basic Correlation Tracking
```python
with CorrelationContext("user_registration") as ctx:
    logger.info("Starting user registration")
    # All logs in this context will have the same correlation ID
    validate_user_data()
    create_user_account()
    send_welcome_email()
    logger.info("User registration completed")
```

### Async Operation Tracking
```python
async def process_data_batch(batch_id):
    async with CorrelationContext(f"batch_{batch_id}") as ctx:
        logger.info(f"Processing batch {batch_id}")
        # Async task info automatically captured
        await fetch_data()
        await transform_data()
        await save_results()
        logger.info(f"Batch {batch_id} completed")
```

### Cross-Platform Debugging
```python
# Works on both Windows and Unix
if debug_monitor.enabled:
    if platform.system() == 'Windows':
        debug_monitor.manual_check()
    else:
        # Use signals: kill -USR1 <pid>
        pass
```

## 📊 Performance Considerations

- **Batch Database Writes**: Reduces database load
- **Configurable Thresholds**: Prevents log spam
- **Efficient Unicode Processing**: Minimal performance impact
- **Lazy Correlation Tracking**: Only active when needed
- **Memory-Efficient Partitioning**: Automatic cleanup of old partitions

## 🔒 Security and Reliability

- **Safe Unicode Handling**: Prevents encoding crashes
- **Database Connection Pooling**: Reliable database logging
- **Error Recovery**: Graceful handling of logging failures
- **Thread-Safe Operations**: Safe for concurrent use
- **Resource Cleanup**: Proper cleanup on application shutdown

## 🎛️ Configuration Options

All enhancements are configurable through environment variables and YAML configuration, maintaining backward compatibility while providing powerful new features.

The enhanced logging system provides comprehensive observability for both synchronous and asynchronous operations while maintaining cross-platform compatibility and robust error handling.
