# coding=utf-8
import asyncio
import atexit
import io
import logging
import os
import platform
import queue
import signal
import sys
import threading
import time
import traceback
import uuid
from contextvars import ContextVar
from logging.handlers import TimedR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from zoneinfo import ZoneInfo
from rich.logging import RichHandler
from typing import Dict, Any, Optional, Set, Union
import weakref

os.environ['PYTHONIOENCODING'] = 'utf-8'

# Context variables for async correlation tracking
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
task_id_var: ContextVar[Optional[str]] = ContextVar('task_id', default=None)
coroutine_name_var: ContextVar[Optional[str]] = ContextVar('coroutine_name', default=None)


class CorrelationContext:
    """Context manager for tracking correlation IDs across async operations."""

    def __init__(self, correlation_id: Optional[str] = None, operation_name: Optional[str] = None):
        self.correlation_id = correlation_id or str(uuid.uuid4())[:8]
        self.operation_name = operation_name
        self.task_id = None
        self.coroutine_name = None

    def __enter__(self):
        # Set correlation ID
        correlation_id_var.set(self.correlation_id)

        # Try to get async task information
        try:
            loop = asyncio.get_running_loop()
            task = asyncio.current_task(loop=loop)
            if task:
                self.task_id = str(id(task))
                self.coroutine_name = task.get_name()
                task_id_var.set(self.task_id)
                coroutine_name_var.set(self.coroutine_name)
        except RuntimeError:
            pass

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Reset context variables
        correlation_id_var.set(None)
        task_id_var.set(None)
        coroutine_name_var.set(None)

    async def __aenter__(self):
        return self.__enter__()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.__exit__(exc_type, exc_val, exc_tb)


def setup_utf8_output():
    """Setup UTF-8 encoding for stdout and stderr on Windows"""
    try:
        # Check if we're on Windows and need to wrap streams
        if sys.platform.startswith('win') and hasattr(sys.stdout, 'detach'):
            # Only wrap if not already wrapped and if encoding is not utf-8
            if not hasattr(sys.stdout, 'buffer') or sys.stdout.encoding.lower() != 'utf-8':
                sys.stdout = io.TextIOWrapper(
                    sys.stdout.detach(),
                    encoding='utf-8',
                    errors='replace'  # This prevents crashes on encoding errors
                )
            if not hasattr(sys.stderr, 'buffer') or sys.stderr.encoding.lower() != 'utf-8':
                sys.stderr = io.TextIOWrapper(
                    sys.stderr.detach(),
                    encoding='utf-8',
                    errors='replace'
                )
    except (AttributeError, OSError):
        # Fallback: streams might already be wrapped or unavailable
        pass

# Call this BEFORE any logging setup
setup_utf8_output()


class UnifiedUnicodeHandler(logging.Filter):
    """
    Unified Unicode handler that combines filtering, formatting, and safe processing.
    Replaces UnicodeAwareFilter, UniversalUnicodeFilter, and SafeUnicodeFormatter.
    """

    def __init__(self, fallback_encoding='ascii'):
        super().__init__()
        self.fallback_encoding = fallback_encoding
        self.unicode_replacements = {
            # Success/Failure symbols
            '✅': '[OK]',
            '❌': '[FAIL]',
            '⚠️': '[WARN]',
            'ℹ️': '[INFO]',

            # Common emojis
            '🔥': '[HOT]',
            '🚀': '[ROCKET]',
            '📝': '[NOTE]',
            '💡': '[IDEA]',
            '🔍': '[SEARCH]',
            '📊': '[CHART]',
            '🎯': '[TARGET]',
            '⭐': '[STAR]',
            '🔒': '[LOCKED]',
            '🔓': '[UNLOCKED]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '✨': '[SPARKLE]',
            '🎉': '[PARTY]',
            '👍': '[THUMBS_UP]',
            '👎': '[THUMBS_DOWN]',
            '💯': '[100]',
            '🔴': '[RED]',
            '🟢': '[GREEN]',
            '🟡': '[YELLOW]',
            '🔵': '[BLUE]',
            '🟠': '[ORANGE]',
            '🟣': '[PURPLE]',

            # Arrows and symbols
            '→': '->',
            '←': '<-',
            '↑': '^',
            '↓': 'v',
            '⬆️': '[UP_ARROW]',
            '⬇️': '[DOWN_ARROW]',
            '➡️': '[RIGHT_ARROW]',
            '⬅️': '[LEFT_ARROW]',

            # Mathematical symbols
            '✓': '[CHECK]',
            '✗': '[X]',
            '±': '+/-',
            '×': 'x',
            '÷': '/',
            '∞': '[INFINITY]',

            # Currency and special chars
            '€': 'EUR',
            '£': 'GBP',
            '¥': 'YEN',
            '©': '(c)',
            '®': '(R)',
            '™': '(TM)',
        }

    def filter(self, record):
        """
        Process the log record to handle Unicode characters safely and add correlation info.
        """
        try:
            # Add correlation tracking information
            self._add_correlation_info(record)

            # Handle the main message
            if hasattr(record, 'msg'):
                record.msg = self._process_unicode_safely(record.msg)

            # Handle message arguments
            if hasattr(record, 'args') and record.args:
                record.args = tuple(self._process_unicode_safely(arg) for arg in record.args)

            return True
        except Exception as e:
            # If something goes wrong, create a safe fallback message
            record.msg = f"[Unicode processing error: {str(e)}] Original level: {record.levelname}"
            record.args = ()
            return True

    def _add_correlation_info(self, record):
        """Add correlation tracking information to log record if not already present."""
        # Only add correlation info if not already set by the log record factory
        if not hasattr(record, 'correlation_id'):
            correlation_id = correlation_id_var.get()
            record.correlation_id = correlation_id or 'N/A'

        if not hasattr(record, 'task_id'):
            task_id = task_id_var.get()
            record.task_id = task_id or 'N/A'

        if not hasattr(record, 'coroutine_name'):
            coroutine_name = coroutine_name_var.get()
            record.coroutine_name = coroutine_name or 'N/A'

        # Try to get current task info if not in context and not already set
        if (not hasattr(record, 'task_id') or record.task_id == 'N/A' or
            not hasattr(record, 'coroutine_name') or record.coroutine_name == 'N/A'):
            try:
                loop = asyncio.get_running_loop()
                task = asyncio.current_task(loop=loop)
                if task:
                    if not hasattr(record, 'task_id') or record.task_id == 'N/A':
                        record.task_id = str(id(task))
                    if not hasattr(record, 'coroutine_name') or record.coroutine_name == 'N/A':
                        record.coroutine_name = task.get_name()
            except RuntimeError:
                pass

    def _process_unicode_safely(self, obj):
        """Process any object to handle Unicode characters safely"""
        if not isinstance(obj, str):
            try:
                obj = str(obj)
            except Exception:
                return "[Unprintable object]"

        # Replace known problematic Unicode characters
        processed_text = self._replace_unicode_characters(obj)

        # Test if the result can be encoded safely
        try:
            processed_text.encode('utf-8').decode('utf-8')

            # On Windows, also test console encoding
            if sys.platform.startswith('win'):
                console_encoding = getattr(sys.stdout, 'encoding', 'cp1252') or 'cp1252'
                try:
                    processed_text.encode(console_encoding)
                except UnicodeEncodeError:
                    processed_text = self._make_ascii_safe(processed_text)

            return processed_text
        except (UnicodeEncodeError, UnicodeDecodeError):
            return self._make_ascii_safe(obj)

    def _replace_unicode_characters(self, text):
        """Replace known Unicode characters with ASCII alternatives"""
        for unicode_char, replacement in self.unicode_replacements.items():
            text = text.replace(unicode_char, replacement)
        return text

    def _make_ascii_safe(self, text):
        """Convert text to ASCII-safe format as a last resort"""
        try:
            return text.encode('ascii', errors='replace').decode('ascii')
        except Exception:
            return "[Text encoding error]"

    def format_record(self, record):
        """Format a log record with safe Unicode handling (replaces SafeUnicodeFormatter)"""
        try:
            # Create a basic format if none exists
            if not hasattr(self, '_formatter'):
                self._formatter = logging.Formatter(
                    '%(asctime)s.%(msecs)03d %(levelname)-8s [%(correlation_id)s] '
                    '[%(task_id)s:%(coroutine_name)s] %(funcName)-25s %(lineno)04d | %(message)s',
                    datefmt='%Y-%m-%dT%H:%M:%S'
                )

            formatted = self._formatter.format(record)

            # Test if the formatted string can be encoded safely
            if sys.platform.startswith('win'):
                console_encoding = sys.stdout.encoding or 'cp1252'
                try:
                    formatted.encode(console_encoding)
                except UnicodeEncodeError:
                    # Re-process with additional sanitization
                    record.msg = self._make_ascii_safe(str(record.msg))
                    if hasattr(record, 'args') and record.args:
                        record.args = tuple(self._make_ascii_safe(str(arg)) for arg in record.args)
                    formatted = self._formatter.format(record)

            return formatted
        except Exception as e:
            # Fallback formatting
            return f"[Formatting Error: {str(e)}] - {record.levelname}: {getattr(record, 'msg', 'Unknown message')}"


# Old Unicode classes removed - replaced by UnifiedUnicodeHandler


class MaskAuthorizationFilter(logging.Filter):
    def filter(self, record):
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            try:
                msg_dict = eval(record.msg)
                if isinstance(msg_dict, dict) and 'headers' in msg_dict and 'Authorization' in msg_dict['headers']:
                    msg_dict['headers']['Authorization'] = '***MASKED***'
                    record.msg = str(msg_dict)
            except (SyntaxError, NameError) as e:
                pass  # If it's not a valid dictionary string, do nothing
        return True

class AutoStartQueueListener(QueueListener):

    def __init__(self, queue, *handlers, respect_handler_level=False):
        super().__init__(queue, *handlers, respect_handler_level=respect_handler_level)
        # Start the listener immediately.
        self.start()

    def stop_listener(self):
        self.stop()

class EnhancedTaskNameFilter(logging.Filter):
    """Enhanced filter that adds task name and correlation information."""

    def filter(self, record):
        # Add task name if not already set
        if not hasattr(record, "task_name"):
            try:
                loop = asyncio.get_running_loop()
                task = asyncio.current_task(loop=loop)
                record.task_name = task.get_name() if task else 'NoTask'
            except RuntimeError:
                record.task_name = "NoEventLoop"

        # Add correlation information if not already set by log record factory
        if not hasattr(record, 'correlation_id'):
            correlation_id = correlation_id_var.get()
            record.correlation_id = correlation_id or 'N/A'

        if not hasattr(record, 'task_id'):
            task_id = task_id_var.get()
            record.task_id = task_id or 'N/A'

        if not hasattr(record, 'coroutine_name'):
            coroutine_name = coroutine_name_var.get()
            record.coroutine_name = coroutine_name or 'N/A'

        return True

# Backward compatibility alias
TaskNameFilter = EnhancedTaskNameFilter

class CustomFormatter(logging.Formatter):
    """Enhanced custom formatter with timezone-aware timestamps and correlation tracking."""

    TZ_MAP = {"India Standard Time": "Asia/Kolkata"}

    def formatTime(self, record, datefmt=None):
        local_time = datetime.now().astimezone()
        timezone_str = local_time.strftime('%Z')
        local_timezone = ZoneInfo(self.TZ_MAP.get(timezone_str, timezone_str))
        dt = datetime.fromtimestamp(record.created, local_timezone)
        return dt.strftime(datefmt or "%Y-%m-%d %H:%M:%S %Z")

    def format(self, record):
        # Ensure correlation fields exist (should already be set by log record factory)
        if not hasattr(record, 'correlation_id'):
            record.correlation_id = correlation_id_var.get() or 'N/A'
        if not hasattr(record, 'task_id'):
            record.task_id = task_id_var.get() or 'N/A'
        if not hasattr(record, 'coroutine_name'):
            record.coroutine_name = coroutine_name_var.get() or 'N/A'

        # Truncate fields for better formatting
        # record.funcName = (record.funcName or "")[:25]
        # record.task_name = (getattr(record, 'task_name', '') or "")[:30]
        # record.correlation_id = (record.correlation_id or "")[:8]
        # record.task_id = (record.task_id or "")[:12]
        # record.coroutine_name = (record.coroutine_name or "")[:20]

        record.funcName = (record.funcName or "")
        record.task_name = (getattr(record, 'task_name', '') or "")
        record.correlation_id = (record.correlation_id or "")
        record.task_id = (record.task_id or "")
        record.coroutine_name = (record.coroutine_name or "")

        # Format lineno with leading zeros (4 digits)
        record.lineno_str = f"{record.lineno:04d}"

        return super().format(record)


class CustomConsoleHandler(RichHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add the unified Unicode handler
        self.addFilter(UnifiedUnicodeHandler())

    def emit(self, record):
        # Only emit logs if the level is WARNING or higher
        if record.levelno >= logging.WARNING:
            try:
                formatted_record = self.format(record)
                self.console.print(formatted_record)
            except Exception as e:
                self.handleError(record)
                raise e


class SafeTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Custom file handler to handle file permission errors during log rotation."""

    def __init__(self, *args, **kwargs):
        # Ensure UTF-8 encoding for file handler
        kwargs.setdefault('encoding', 'utf-8')
        super().__init__(*args, **kwargs)
        # Add the unified Unicode handler
        self.addFilter(UnifiedUnicodeHandler())

    def doRollover(self):
        retries = 5
        while retries > 0:
            try:
                super().doRollover()
                break
            except PermissionError:
                retries -= 1
                if retries == 0:
                    raise


class DatabaseLogHandler(logging.Handler):
    """
    Custom logging handler that writes logs to PostgreSQL database.
    Supports batch writing and automatic partition management.
    Designed to work with dependency injection containers.
    """

    def __init__(self, session_manager=None, batch_size=100, flush_interval=30,
                 schema='public', enabled=True):
        super().__init__()
        self.session_manager = session_manager
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.schema = schema
        self.enabled = enabled
        self.log_buffer = []
        self.last_flush = time.time()
        self.buffer_lock = threading.Lock()
        self._shutdown = False

        # Add the unified Unicode handler
        self.addFilter(UnifiedUnicodeHandler())

        # Only start background thread if enabled and session manager is available
        self.flush_thread = None
        if self.enabled and self.session_manager:
            self._start_flush_thread()

        # Register cleanup on exit
        atexit.register(self.close)

    # def set_session_manager(self, session_manager):
    #     """Set the session manager after initialization (for dependency injection)."""
    #     self.session_manager = session_manager
    #     if self.enabled and not self.flush_thread:
    #         self._start_flush_thread()

    def _start_flush_thread(self):
        """Start the background flush thread."""
        if not self.flush_thread or not self.flush_thread.is_alive():
            self.flush_thread = threading.Thread(target=self._flush_loop, daemon=True)
            self.flush_thread.start()
        

    def emit(self, record):
        """Emit a log record to the database buffer."""
        if not self.enabled or not self.session_manager or self._shutdown:
            return

        try:
            # Create log entry data
            log_data = self._create_log_entry(record)

            with self.buffer_lock:
                self.log_buffer.append(log_data)

                # Flush if buffer is full
                if len(self.log_buffer) >= self.batch_size:
                    self._flush_buffer()

        except Exception as e:
            self.handleError(record)

    def _create_log_entry(self, record):
        """Create a log entry dictionary from a log record."""
        import socket
        import os

        # Get stack trace for errors
        stack_trace = None
        if record.exc_info:
            stack_trace = self.format(record)

        # Extract performance data if available
        performance_data = {}
        if hasattr(record, 'duration'):
            performance_data['duration'] = record.duration
        if hasattr(record, 'memory_usage'):
            performance_data['memory_usage'] = record.memory_usage

        # Collect extra data
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'timestamp', 'correlation_id',
                          'task_id', 'coroutine_name']:
                try:
                    # Only include JSON-serializable values
                    import json
                    json.dumps(value)
                    extra_data[key] = value
                except (TypeError, ValueError):
                    extra_data[key] = str(value)

        return {
            'timestamp': datetime.fromtimestamp(record.created),
            'level': record.levelname,
            'logger_name': record.name,
            'module': getattr(record, 'module', record.filename),
            'function_name': record.funcName,
            'line_number': record.lineno,
            'message': record.getMessage(),
            'correlation_id': getattr(record, 'correlation_id', None),
            'task_id': getattr(record, 'task_id', None),
            'coroutine_name': getattr(record, 'coroutine_name', None),
            'thread_id': record.thread,
            'process_id': record.process,
            'hostname': socket.gethostname(),
            'user_id': os.getenv('USER') or os.getenv('USERNAME'),
            'session_id': None,  # Can be set by application
            'request_id': getattr(record, 'request_id', None),
            'extra_data': extra_data if extra_data else None,
            'stack_trace': stack_trace,
            'performance_data': performance_data if performance_data else None
        }

    def _flush_buffer(self):
        """Flush the log buffer to database."""
        if not self.log_buffer or not self.session_manager:
            return

        try:
            from dags.data_pipeline.dbmodels.log_entry import LogEntry

            with self.session_manager.session() as session:
                # Create LogEntry objects
                log_entries = [LogEntry(**log_data) for log_data in self.log_buffer]
                # Bulk insert
                session.add_all(log_entries)
                session.commit()

            # Clear buffer after successful write
            self.log_buffer.clear()
            self.last_flush = time.time()

        except Exception as e:
            # Log the error but don't raise to avoid logging loops
            print(f"Error flushing logs to database: {e}", file=sys.stderr)
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            print(f"Line {line_num} Error encountered")
            print(f"{''.join(tb.format_exception_only())}")


    def _flush_loop(self):
        """Background thread to periodically flush logs."""
        while not self._shutdown:
            try:
                time.sleep(self.flush_interval)
                current_time = time.time()

                with self.buffer_lock:
                    if (self.log_buffer and
                        current_time - self.last_flush >= self.flush_interval):
                        self._flush_buffer()

            except Exception as e:
                print(f"Error in flush loop: {e}", file=sys.stderr)

    def flush(self):
        """Manually flush the log buffer."""
        with self.buffer_lock:
            self._flush_buffer()

    def close(self):
        """Close the handler and flush remaining logs."""
        self._shutdown = True
        self.flush()
        if self.flush_thread and self.flush_thread.is_alive():
            self.flush_thread.join(timeout=5)
        super().close()


class DatabaseLogHandlerFactory:
    """
    Factory for creating DatabaseLogHandler instances with proper dependency injection.
    This avoids circular dependencies between logging and database containers.
    """

    @staticmethod
    def create_handler(session_manager=None, batch_size=100, flush_interval=30,
                      schema='public', enabled=True, level=logging.INFO):
        """Create a DatabaseLogHandler with the given parameters."""
        handler = DatabaseLogHandler(
            session_manager=session_manager,
            batch_size=batch_size,
            flush_interval=flush_interval,
            schema=schema,
            enabled=enabled
        )
        handler.setLevel(level)
        return handler

    @staticmethod
    def create_deferred_handler(batch_size=100, flush_interval=30,
                               schema='public', enabled=True, level=logging.INFO):
        """
        Create a DatabaseLogHandler without session manager (for later injection).
        This is useful when the session manager is not available at handler creation time.
        """
        handler = DatabaseLogHandler(
            session_manager=None,  # Will be set later
            batch_size=batch_size,
            flush_interval=flush_interval,
            schema=schema,
            enabled=enabled
        )
        handler.setLevel(level)
        return handler


def configure_database_logging(logger_name, session_manager, schema='public',
                              batch_size=50, flush_interval=30, level=logging.INFO):
    """
    Configure database logging for a specific logger.
    This function can be called after container initialization to add database logging.
    """
    logger = logging.getLogger(logger_name)

    # Check if database handler already exists
    for handler in logger.handlers:
        if isinstance(handler, DatabaseLogHandler):
            # Update existing handler
            handler.set_session_manager(session_manager)
            return handler

    # Create new database handler
    db_handler = DatabaseLogHandlerFactory.create_handler(
        session_manager=session_manager,
        batch_size=batch_size,
        flush_interval=flush_interval,
        schema=schema,
        enabled=True,
        level=level
    )

    logger.addHandler(db_handler)
    return db_handler




class CustomLogger:
    """Custom logger with RichHandler for console and file-based logging."""

    def __init__(self, name, log_file, use_formatter=True):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        self.log_queue = queue.Queue(-1)  # Unlimited size

        # Setup handlers
        console_handler = self._get_console_handler(use_formatter)
        file_handler = self._get_file_handler(log_file, use_formatter)

        # Queue handler for thread-safe logging
        queue_handler = QueueHandler(self.log_queue)

        # Add the unified Unicode handler to the queue handler
        # This ensures ALL messages are processed before they go to the queue
        queue_handler.addFilter(UnifiedUnicodeHandler())

        self.logger.addHandler(queue_handler)

        # Listener to process logs from the queue
        self.listener = AutoStartQueueListener(
            self.log_queue,
            console_handler,
            file_handler,
            respect_handler_level=True
        )

        # Stop listener on application exit
        atexit.register(self.listener.stop)
        self.logger.propagate = False

    def _get_console_handler(self, use_formatter):
        """Configure RichHandler for console logs."""
        console_handler = CustomConsoleHandler(rich_tracebacks=True, markup=True)
        console_handler.setLevel(logging.WARNING)  # Only show warnings and above on console

        if use_formatter:
            formatter = CustomFormatter(
                "%(asctime)s — %(name)s — %(levelname)s — %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S %Z"
            )
            console_handler.setFormatter(formatter)
        return console_handler

    def _get_file_handler(self, log_file, use_formatter):
        """Configure TimedRotatingFileHandler for file-based logs."""
        file_handler = SafeTimedRotatingFileHandler(
            log_file,
            when="midnight",
            backupCount=3,
            delay=True,
            encoding='utf-8'  # Explicitly set UTF-8 encoding
        )
        file_handler.setLevel(logging.DEBUG)

        if use_formatter:
            formatter = CustomFormatter(
                "%(asctime)s.%(msecs)03d %(levelname)-8s %(funcName)-40s %(lineno)-4d~%(message)s",
                datefmt="%Y-%m-%dT%H:%M:%S"
            )
            file_handler.setFormatter(formatter)
        return file_handler

    def get_logger(self):
        """Return the configured logger."""
        return self.logger


# Enhanced usage examples with new features
if __name__ == "__main__":
    import asyncio

    # Setup your logger as usual
    log_file_name = os.getenv("AIRFLOW_HOME", "/tmp") + "/main.log"
    custom_logger = CustomLogger(__name__, log_file_name, use_formatter=True)
    my_logger = custom_logger.get_logger()

    # Example 1: Basic logging with automatic Unicode handling
    func_name = "test_function"
    success = True

    # These calls will now automatically handle Unicode characters safely
    if success:
        my_logger.info(f"✅ {func_name} test completed successfully")
    else:
        my_logger.error(f"❌ {func_name} test failed")

    # Example 2: Using correlation context for tracking related operations
    with CorrelationContext(operation_name="data_processing") as ctx:
        my_logger.info("Starting data processing operation")
        my_logger.info("Processing batch 1")
        my_logger.info("Processing batch 2")
        my_logger.info("Data processing completed")

    # Example 3: Async operation tracking
    async def async_example():
        async with CorrelationContext(operation_name="async_processing") as ctx:
            my_logger.info("Starting async operation")
            await asyncio.sleep(0.1)
            my_logger.info("Async operation step 1")
            await asyncio.sleep(0.1)
            my_logger.info("Async operation completed")


    # Example 5: Database logging (if configured)
    # The database handler will automatically capture all log entries
    # with correlation IDs, task IDs, and performance data

    # Run async example
    asyncio.run(async_example())

    print("Enhanced logging examples completed!")
    print("Features demonstrated:")
    print("- ✅ Unified Unicode handling")
    print("- ✅ Correlation ID tracking")
    print("- ✅ Async task correlation")
    print("- ✅ Cross-platform debugging")
    print("- ✅ Database logging support")
    print("- ✅ Configurable monitoring thresholds")
