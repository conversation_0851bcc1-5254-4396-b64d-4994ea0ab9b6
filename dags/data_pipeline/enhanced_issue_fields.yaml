# yaml-language-server: $schema=./schema/enhanced_fields.schema.json
fields:
  - id: aggregateprogress
    datatype: progress
    custom: false
    name: "Σ Progress"
    mapping:
      aggregateprogress.percent: aggregateprogress_percent
      aggregateprogress.progress: aggregateprogress_progress
      aggregateprogress.total: aggregateprogress_total
    target_type: Float64

  - id: aggregatetimeestimate
    datatype: number
    custom: false
    name: "Σ Remaining Estimate"
    target_type: Float64

  - id: aggregatetimeoriginalestimate
    datatype: number
    custom: false
    name: "Σ Original Estimate"
    target_type: Float64

  - id: aggregatetimespent
    datatype: number
    custom: false
    name: "Σ Time Spent"
    target_type: Float64

  - id: assignee
    datatype: user
    custom: false
    name: Assignee
    mapping:
      assignee.accountId: assignee
    target_type: string

  - id: comment
    datatype: comments-page
    custom: false
    name: Comment
    # No direct mapping - handled separately

  - id: components
    datatype: array
    custom: false
    name: Components
    target_type: array_string

  - id: created
    datatype: datetime
    custom: false
    name: Created
    target_type: datetime

  - id: customfield_10001
    datatype: team
    custom: true
    name: Team
    mapping:
      customfield_10001.name: Team
    target_type: string

  - id: customfield_10006
    datatype: option
    custom: true
    name: "Review Status"
    mapping:
      customfield_10006.value: change_risk
    target_type: string

  - id: customfield_10015
    datatype: date
    custom: true
    name: "Start date"
    mapping:
      customfield_10015: startdate
    target_type: date

  - id: customfield_10019
    datatype: any
    custom: true
    name: Rank
    mapping:
      customfield_10019: Rank
    target_type: string

  - id: customfield_10020
    datatype: array
    custom: true
    name: Sprint
    mapping:
      customfield_10020: sprint
    target_type: json

  - id: customfield_10024
    datatype: string
    custom: true
    name: "Story Points"
    mapping:
      customfield_10024: storypoints
    target_type: Float32

  - id: customfield_10049
    datatype: option
    custom: true
    name: Severity
    mapping:
      customfield_10049.value: severity
    target_type: string

  - id: customfield_10056
    datatype: option
    custom: true
    name: "Issue Classification"
    mapping:
      customfield_10056.value: category_type
    target_type: string

  - id: customfield_10059
    datatype: string
    custom: true
    name: "Test Case Number"
    mapping:
      customfield_10059: testcaseno
    target_type: string

  - id: customfield_10060
    datatype: string
    custom: true
    name: "Test Case Suite"
    mapping:
      customfield_10060: testcasesuite
    target_type: string

  - id: customfield_10061
    datatype: string
    custom: true
    name: "Test Step Number"
    mapping:
      customfield_10061: teststepno
    target_type: string

  - id: customfield_10062
    datatype: string
    custom: true
    name: "Scenario Number"
    mapping:
      customfield_10062: scenariono
    target_type: string

  - id: customfield_10067
    datatype: array
    custom: true
    name: "Client Jira #"
    mapping:
      customfield_10067: ClientJira
    target_type: array_string

  - id: customfield_10071
    datatype: option
    custom: true
    name: "Initiated By"
    mapping:
      customfield_10071.value: initiated_by
    target_type: string

  - id: customfield_10078
    datatype: option
    custom: true
    name: "Lenox Approval Status"
    mapping:
      customfield_10078.value: approvalstatus
    target_type: string

  - id: customfield_10092
    datatype: option
    custom: true
    name: Urgency
    mapping:
      customfield_10092.value: urgency
    target_type: string

  - id: customfield_10120
    datatype: number
    custom: true
    name: "Total Effort"
    mapping:
      customfield_10120: totaleffort
    target_type: float64

  - id: customfield_10121
    datatype: number
    custom: true
    name: "Total Dev Effort"
    mapping:
      customfield_10121: totaldeveffort
    target_type: Float64

  - id: customfield_10122
    datatype: number
    custom: true
    name: "BA Effort"
    mapping:
      customfield_10122: baeffort
    target_type: Float64

  - id: customfield_10123
    datatype: number
    custom: true
    name: "AD Effort"
    mapping:
      customfield_10123: adeffort
    target_type: Float64

  - id: customfield_10124
    datatype: number
    custom: true
    name: "RD Effort"
    mapping:
      customfield_10124: rdeffort
    target_type: Float64

  - id: customfield_10125
    datatype: number
    custom: true
    name: "QA Effort"
    mapping:
      customfield_10125: qaeffort
    target_type: Float64

  - id: customfield_10126
    datatype: number
    custom: true
    name: Contingency
    mapping:
      customfield_10126: contingency
    target_type: Float64

  - id: customfield_10146
    datatype: option
    custom: true
    name: "Requirement Finalized"
    mapping:
      customfield_10146.value: reqfinalized
    target_type: string

  - id: customfield_10147
    datatype: string
    custom: true
    name: "Reopened Count"
    mapping:
      customfield_10147: reopen_count
    target_type: Int8

  - id: customfield_10179
    datatype: option
    custom: true
    name: "Assignee Team"
    mapping:
      customfield_10179.value: qc_check
    target_type: string

  - id: customfield_10182
    datatype: string
    custom: true
    name: "WD Project"
    mapping:
      customfield_10182: project
    target_type: string

  - id: customfield_10183
    datatype: string
    custom: true
    name: "WD Release"
    mapping:
      customfield_10183: release
    target_type: string

  - id: customfield_10184
    datatype: string
    custom: true
    name: "WD Feature"
    mapping:
      customfield_10184: feature
    target_type: string

  - id: customfield_10199
    datatype: number
    custom: true
    name: "CVSS Score"
    mapping:
      customfield_10199: cvss_score
    target_type: Float32

  - id: customfield_10256
    datatype: string
    custom: true
    name: "Initiative Details"
    mapping:
      customfield_10256: initiative_detail
    target_type: string

  # Special handling for initiative attributes
  - id: initiative_attributes
    datatype: special
    custom: false
    name: "Initiative Attributes"
    mapping:
#      id: initiative_id
#      key: initiative_key
      customfield_10182: project
      customfield_10183: release
      customfield_10184: feature
    field_types:
#      id: int64
#      key: string
      customfield_10182: string
      customfield_10183: string
      customfield_10184: string

  - id: description
    datatype: string
    custom: false
    name: Description
    target_type: json

  - id: duedate
    datatype: date
    custom: false
    name: "Due date"
    target_type: date

  - id: fixVersions
    datatype: array
    custom: false
    name: "Fix versions"
    target_type: array_string

  - id: issuelinks
    datatype: array
    custom: false
    name: "Linked Issues"
    # Handled separately

  - id: issuetype
    datatype: issuetype
    custom: false
    name: "Issue Type"
    mapping:
      issuetype.name: issuetype
      issuetype.subtask: isSubTask
      issuetype.hierarchyLevel: issue_hierarchy_level
    field_types:
      issuetype.name: category
      issuetype.subtask: bool
      issuetype.hierarchyLevel: Int8

  - id: parent
    datatype: array
    custom: false
    name: Parent
    mapping:
      parent.id: parent_id
      parent.key: parent_key
    field_types:
      parent.id: Int64
      parent.key: string

  - id: priority
    datatype: priority
    custom: false
    name: Priority
    mapping:
      priority.name: priority
    target_type: string

  - id: progress
    datatype: progress
    custom: false
    name: Progress
    mapping:
      progress.percent: progress_percent
      progress.progress: progress_progress
      progress.total: progress_total
    target_type: Float64

  - id: reporter
    datatype: user
    custom: false
    name: Reporter
    mapping:
      reporter.accountId: reporter
    target_type: string

  - id: resolution
    datatype: resolution
    custom: false
    name: Resolution
    mapping:
      resolution.name: resolution
    target_type: string

  - id: resolutiondate
    datatype: datetime
    custom: false
    name: Resolved
    target_type: datetime

  - id: status
    datatype: status
    custom: false
    name: Status
    mapping:
      status.name: status
      status.statusCategory.name: statusCategory
    target_type: category

  - id: statuscategorychangedate
    datatype: datetime
    custom: false
    name: "Status Category Changed"
    target_type: datetime

  - id: summary
    datatype: string
    custom: false
    name: Summary
    target_type: string[pyarrow]

  - id: timeestimate
    datatype: number
    custom: false
    name: "Remaining Estimate"
    target_type: Float64

  - id: timeoriginalestimate
    datatype: number
    custom: false
    name: "Original estimate"
    target_type: Float64

  - id: timespent
    datatype: number
    custom: false
    name: "Time Spent"
    target_type: Float64

  - id: timetracking
    datatype: timetracking
    custom: false
    name: "Time tracking"
    target_type: json

  - id: updated
    datatype: datetime
    custom: false
    name: Updated
    target_type: datetime

  - id: versions
    datatype: array
    custom: false
    name: "Affects versions"
    target_type: array_string

  - id: worklog
    datatype: array
    custom: false
    name: "Log Work"
    target_type: json

drop-column-prefixes:
  - parent.
  - customfield_10001.
  - worklog.
  - comment.comments
  - description.
  - assignee.
  - customfield_10006.
  - customfield_10049.
  - reporter.
  - customfield_10056.
  - customfield_10071.
  - customfield_10078.
  - customfield_10146.
  - customfield_10179.
  - issuetype.
  - priority.
  - resolution.
  - status.
  - customfield_10092.

drop-column-exceptions:
  - parent.id
  - parent.key
  - assignee.accountId
  - customfield_10006.value
  - customfield_10001.name
  - customfield_10049.value
  - reporter.accountId
  - customfield_10056.value
  - customfield_10071.value
  - customfield_10078.value
  - customfield_10146.value
  - customfield_10179.value
  - issuetype.name
  - issuetype.subtask
  - issuetype.hierarchyLevel
  - priority.name
  - resolution.name
  - status.name
  - status.statusCategory.name
  - customfield_10092.value


database_config:
  model: Issue
  no_update_cols: ("tscv_summary_description")
