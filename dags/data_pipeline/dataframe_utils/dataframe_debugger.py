"""
Refactored DataFrame Debugger with improved architecture using mixins, protocols, and reduced code duplication.

Key improvements:
1. Protocol-based interfaces for type safety and clear contracts
2. Mixins for shared functionality (logging, file operations, preprocessing)
3. Strategy pattern for different execution models (sync/async)
4. Factory pattern for easy instantiation
5. Eliminated code duplication while maintaining backward compatibility
"""

import os
import time
import asyncio
import logging
import threading
import weakref
import atexit
from abc import ABC, abstractmethod
from contextlib import contextmanager, asynccontextmanager
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from queue import Queue, Empty
from threading import Thread, Event, Lock
from typing import (
    Protocol, runtime_checkable, Optional, Dict, Any, Union, List, 
    Callable, Awaitable, TypeVar, Generic
)

import pandas as pd

# Safe handler import with fallback
try:
    from dags.data_pipeline.custom_logger import SafeTimedRotatingFileHandler
    from dags.data_pipeline.containers import LoggerContainer
except ImportError:
    from logging.handlers import TimedRotatingFileHandler
    SafeTimedRotatingFileHandler = TimedRotatingFileHandler
    LoggerContainer = None


# Type variables and enums
T = TypeVar('T')


class SaveFormat(Enum):
    """Supported file formats."""
    CSV = "csv"
    EXCEL = "xlsx"
    PARQUET = "parquet"
    FEATHER = "feather"
    JSON = "json"


class Priority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class ExecutionMode(Enum):
    """Execution modes for the debugger."""
    SYNC = "sync"
    ASYNC = "async"


# Protocols (Interfaces)
@runtime_checkable
class DataFrameProcessor(Protocol):
    """Protocol for DataFrame processing operations."""
    
    def preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess DataFrame before saving."""
        ...
    
    def estimate_dataframe_size(self, df: pd.DataFrame) -> int:
        """Estimate DataFrame size in bytes."""
        ...


@runtime_checkable
class FileHandler(Protocol):
    """Protocol for file handling operations."""
    
    def save_csv(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to CSV."""
        ...
    
    def save_excel(self, df: pd.DataFrame, file_path: Path, column_formats: Dict[str, Any]) -> None:
        """Save DataFrame to Excel with formatting."""
        ...
    
    def save_parquet(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to Parquet."""
        ...
    
    def save_feather(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to Feather."""
        ...
    
    def save_json(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to JSON."""
        ...


@runtime_checkable
class TaskProcessor(Protocol):
    """Protocol for task processing."""
    
    def process_task(self, task: 'SaveTask') -> bool:
        """Process a save task."""
        ...


@runtime_checkable
class ProgressNotifier(Protocol):
    """Protocol for progress notifications."""
    
    def notify_progress(self, status: str, task_id: str, filename: str) -> None:
        """Notify progress to callbacks."""
        ...


# Data classes
@dataclass
class SaveTask:
    """Represents a save task with metadata."""
    df: pd.DataFrame
    file_path: Path
    file_format: SaveFormat
    column_formats: Dict[str, Any] = field(default_factory=dict)
    priority: Priority = Priority.NORMAL
    timestamp: datetime = field(default_factory=datetime.now)
    callback: Optional[Union[
        Callable[[bool, Optional[str]], None],
        Callable[[bool, Optional[str]], Awaitable[None]]
    ]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        self.df = self.df.copy()  # Ensure we have a copy


@dataclass
class ProcessingStats:
    """Statistics for processing operations."""
    files_saved: int = 0
    files_failed: int = 0
    total_retries: int = 0
    bytes_processed: int = 0
    avg_processing_time: float = 0.0
    queue_high_water_mark: int = 0


# Mixins for shared functionality
class LoggerMixin:
    """Mixin for shared logging functionality."""
    
    _logger_cache = {}
    _logger_cache_lock = Lock()
    _active_handlers = weakref.WeakSet()
    
    def _setup_logger(self, log_level: int) -> logging.Logger:
        """Setup logger with proper file descriptor management."""
        logger_name = f"{__name__}.dataframe_debugger"
        
        with self._logger_cache_lock:
            if logger_name in self._logger_cache:
                cached_logger, cached_level = self._logger_cache[logger_name]
                if cached_logger.level != log_level:
                    cached_logger.setLevel(log_level)
                return cached_logger
            
            logger = logging.getLogger(logger_name)
            logger.setLevel(log_level)
            
            if not logger.handlers:
                # Try to use LoggerContainer first
                if LoggerContainer:
                    try:
                        logger_container = LoggerContainer()
                        shared_logger = logger_container.logger()
                        for handler in shared_logger.handlers:
                            if handler not in logger.handlers:
                                logger.addHandler(handler)
                        logger.setLevel(shared_logger.level)
                        self._logger_cache[logger_name] = (logger, log_level)
                        return logger
                    except Exception:
                        pass  # Fall through to file handler
                
                # Fallback to file handler
                log_dir = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
                os.makedirs(log_dir, exist_ok=True)
                log_file_path = log_dir / "dataframe_debugger_shared.log"
                
                handler = SafeTimedRotatingFileHandler(
                    filename=str(log_file_path),
                    when="midnight",
                    interval=1,
                    backupCount=7,
                    encoding="utf-8",
                    delay=True
                )
                
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - [PID:%(process)d] %(message)s'
                )
                handler.setFormatter(formatter)
                logger.addHandler(handler)
                self._active_handlers.add(handler)
            
            self._logger_cache[logger_name] = (logger, log_level)
            return logger
    
    @classmethod
    def cleanup_logger_cache(cls):
        """Clean up cached loggers and handlers."""
        with cls._logger_cache_lock:
            handlers_to_close = list(cls._active_handlers)
            for handler in handlers_to_close:
                try:
                    handler.close()
                except Exception:
                    pass
            cls._logger_cache.clear()
            cls._active_handlers.clear()


class PathMixin:
    """Mixin for path handling functionality."""
    
    def _setup_path(self, path: Optional[Union[str, Path]], create_directory: bool) -> Path:
        """Setup and validate the output path."""
        if path is None:
            path = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
        else:
            path = Path(path)
        
        if create_directory:
            try:
                path.mkdir(parents=True, exist_ok=True)
            except OSError as e:
                if hasattr(self, 'logger'):
                    self.logger.warning(f"Could not create directory {path}: {e}")
        
        if not path.exists():
            raise FileNotFoundError(f"Output directory does not exist: {path}")
        
        return path


class DataFrameProcessorMixin:
    """Mixin for DataFrame processing operations."""
    
    def preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess DataFrame before saving."""
        df = df.copy()
        
        # Handle timezone-aware datetimes
        datetime_cols = df.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64[ns]']).columns
        for col in datetime_cols:
            if hasattr(df[col].dtype, 'tz') and df[col].dtype.tz is not None:
                df[col] = df[col].dt.tz_localize(None)
        
        # Handle complex objects
        object_cols = df.select_dtypes(include=['object']).columns
        for col in object_cols:
            if df[col].apply(lambda x: isinstance(x, (list, dict))).any():
                df[col] = df[col].astype(str)
        
        return df
    
    def estimate_dataframe_size(self, df: pd.DataFrame) -> int:
        """Estimate DataFrame size in bytes."""
        return df.memory_usage(deep=True).sum()


class FileHandlerMixin:
    """Mixin for file handling operations."""
    
    def save_csv(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to CSV."""
        df.to_csv(file_path, index=False, encoding='utf-8')
    
    def save_excel(self, df: pd.DataFrame, file_path: Path, column_formats: Dict[str, Any]) -> None:
        """Save DataFrame to Excel with formatting."""
        with pd.ExcelWriter(file_path, engine="xlsxwriter") as writer:
            df.to_excel(writer, index=False, sheet_name="Data")
            
            worksheet = writer.sheets['Data']
            workbook = writer.book
            
            # Apply formatting
            wrap_format = workbook.add_format({'text_wrap': True})
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4F81BD',
                'font_color': 'white'
            })
            
            # Set column widths and formats
            for i, col in enumerate(df.columns):
                width = column_formats.get(col, 15)
                worksheet.set_column(i, i, width)
                worksheet.write(0, i, col, header_format)
            
            if not df.empty:
                worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
    
    def save_parquet(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to Parquet."""
        df.to_parquet(file_path, index=False)
    
    def save_feather(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to Feather."""
        df.to_feather(file_path)
    
    def save_json(self, df: pd.DataFrame, file_path: Path) -> None:
        """Save DataFrame to JSON."""
        df.to_json(file_path, orient='records', date_format='iso')
    
    def _compress_file(self, file_path: Path) -> None:
        """Compress file using gzip."""
        import gzip
        import shutil
        
        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        file_path.unlink()
        compressed_path.rename(file_path.with_suffix(file_path.suffix + '.gz'))


class ProgressNotifierMixin:
    """Mixin for progress notification functionality."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.progress_callbacks = []
    
    def add_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a progress callback function."""
        self.progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a progress callback function."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)


# Base classes with strategy pattern
class BaseDataFrameDebugger(
    LoggerMixin, 
    PathMixin, 
    DataFrameProcessorMixin, 
    FileHandlerMixin,
    ProgressNotifierMixin,
    ABC
):
    """Base class for DataFrame debuggers with shared functionality."""
    
    def __init__(
        self,
        path: Optional[Union[str, Path]] = None,
        max_retries: int = 5,
        retry_delay: float = 1.0,
        create_directory: bool = True,
        log_level: int = logging.INFO,
        auto_compress: bool = True,
        enable_progress_callback: bool = True
    ):
        super().__init__()
        
        # Setup logging and path
        self.logger = self._setup_logger(log_level)
        self.path = self._setup_path(path, create_directory)
        
        # Configuration
        self.max_retries = max(1, max_retries)
        self.retry_delay = max(0.1, retry_delay)
        self.auto_compress = auto_compress
        self.enable_progress_callback = enable_progress_callback
        
        # Statistics
        self.stats = ProcessingStats()
        
        # Task counter for unique IDs
        self._task_counter = 0
    
    def _generate_filename(self, filename: Optional[str], file_format: Optional[Union[str, SaveFormat]] = None) -> tuple[str, SaveFormat]:
        """Generate filename and determine format."""
        # Handle SaveFormat enum input
        if isinstance(file_format, SaveFormat):
            file_extension = file_format.value
            save_format = file_format
        elif isinstance(file_format, str):
            file_extension = file_format.lower()
            try:
                save_format = SaveFormat(file_extension)
            except ValueError:
                raise ValueError(f"Unsupported file format: {file_extension}")
        else:
            file_extension = None
            save_format = None

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            ext = file_extension if file_extension else 'xlsx'
            filename = f"dataframe_{timestamp}.{ext}"

        if file_format is None:
            file_extension = Path(filename).suffix.lower().lstrip('.')
            if not file_extension:
                file_extension = 'xlsx'
                filename += f'.{file_extension}'
            try:
                save_format = SaveFormat(file_extension)
            except ValueError:
                raise ValueError(f"Unsupported file format: {file_extension}")
        else:
            if not filename.endswith(f'.{file_extension}'):
                filename = Path(filename).stem + f'.{file_extension}'

        return filename, save_format
    
    def _save_with_format(self, df: pd.DataFrame, file_path: Path, save_format: SaveFormat, column_formats: Dict[str, Any]) -> None:
        """Save DataFrame based on format."""
        df = self.preprocess_dataframe(df)
        
        if save_format == SaveFormat.CSV:
            self.save_csv(df, file_path)
        elif save_format == SaveFormat.EXCEL:
            self.save_excel(df, file_path, column_formats)
        elif save_format == SaveFormat.PARQUET:
            self.save_parquet(df, file_path)
        elif save_format == SaveFormat.FEATHER:
            self.save_feather(df, file_path)
        elif save_format == SaveFormat.JSON:
            self.save_json(df, file_path)
        else:
            raise ValueError(f"Unsupported format: {save_format}")
        
        # Apply compression if enabled
        if self.auto_compress and file_path.stat().st_size > 1024 * 1024:  # 1MB
            self._compress_file(file_path)
    
    def _save_with_retries(self, task: SaveTask) -> bool:
        """Save with retry logic."""
        for attempt in range(self.max_retries):
            try:
                self._save_with_format(
                    task.df, 
                    task.file_path, 
                    task.file_format, 
                    task.column_formats
                )
                
                self.stats.files_saved += 1
                self.stats.bytes_processed += self.estimate_dataframe_size(task.df)
                return True
                
            except Exception as e:
                self.stats.total_retries += 1
                
                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt)
                    time.sleep(delay)
                else:
                    self.logger.error(f"Failed to save {task.file_path} after {self.max_retries} attempts: {e}")
                    self.stats.files_failed += 1
        
        return False
    
    @abstractmethod
    def save_dataframe(self, df: pd.DataFrame, filename: Optional[str] = None, **kwargs) -> Union[bool, str]:
        """Save DataFrame (implementation depends on sync/async)."""
        pass
    
    @abstractmethod
    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for completion (implementation depends on sync/async)."""
        pass


# Synchronous implementation
class SyncDataFrameDebugger(BaseDataFrameDebugger):
    """Synchronous DataFrame debugger with thread-based processing."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Threading components
        self.queue = Queue()
        self.stop_event = Event()
        self.worker_thread = Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        
        # Register cleanup
        atexit.register(self.stop)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_value, traceback_value):
        self.stop()
    
    def save_dataframe(
        self,
        df: pd.DataFrame,
        filename: Optional[str] = None,
        file_format: Optional[Union[str, SaveFormat]] = None,
        column_formats: Optional[Dict[str, Any]] = None,
        priority: Priority = Priority.NORMAL,
        callback: Optional[Callable[[bool, Optional[str]], None]] = None,
        **metadata
    ) -> bool:
        """Queue a DataFrame for saving."""
        if df is None or df.empty:
            self.logger.warning("Cannot save empty or None DataFrame")
            return False
        
        try:
            filename, save_format = self._generate_filename(filename, file_format)
            file_path = self.path / filename
            
            task_id = f"task_{self._task_counter}_{int(time.time() * 1000)}"
            self._task_counter += 1
            
            task = SaveTask(
                df=df,
                file_path=file_path,
                file_format=save_format,
                column_formats=column_formats or {},
                priority=priority,
                callback=callback,
                metadata={'task_id': task_id, **metadata}
            )
            
            self.queue.put(task)
            self._notify_progress("queued", task_id, filename)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to queue DataFrame save task: {e}")
            return False
    
    def _worker(self):
        """Background worker thread."""
        while not self.stop_event.is_set():
            try:
                task = self.queue.get(timeout=1.0)
                if task is None:  # Sentinel value
                    break
                
                task_id = task.metadata.get('task_id', 'unknown')
                self._notify_progress("processing", task_id, str(task.file_path.name))
                
                success = self._save_with_retries(task)
                
                if success:
                    self._notify_progress("completed", task_id, str(task.file_path.name))
                else:
                    self._notify_progress("failed", task_id, str(task.file_path.name))
                
                # Call callback if provided
                if task.callback:
                    try:
                        task.callback(success, None if success else "Save failed")
                    except Exception as e:
                        self.logger.warning(f"Callback error for task {task_id}: {e}")
                
                self.queue.task_done()
                
            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"Worker thread error: {e}")
    
    def _notify_progress(self, status: str, task_id: str, filename: str):
        """Notify progress callbacks."""
        if not self.enable_progress_callback or not self.progress_callbacks:
            return
        
        progress_data = {
            'status': status,
            'task_id': task_id,
            'filename': filename,
            'timestamp': datetime.now(),
            'stats': self.stats.__dict__.copy()
        }
        
        for callback in self.progress_callbacks:
            try:
                callback(progress_data)
            except Exception as e:
                self.logger.warning(f"Progress callback error: {e}")
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for all queued tasks to complete."""
        try:
            if timeout is None:
                self.queue.join()
                return True
            else:
                start_time = time.time()
                while not self.queue.empty():
                    if time.time() - start_time > timeout:
                        return False
                    time.sleep(0.1)
                return True
        except Exception as e:
            self.logger.error(f"Error waiting for completion: {e}")
            return False
    
    def get_queue_size(self) -> int:
        """Get current queue size."""
        return self.queue.qsize()
    
    def stop(self):
        """Stop the worker thread."""
        if hasattr(self, 'stop_event') and not self.stop_event.is_set():
            self.stop_event.set()
            self.queue.put(None)
            
            if self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5.0)
    
    def debug_dataframe(
        self,
        df: pd.DataFrame,
        filename: Optional[str] = None,
        column_formats: Optional[Dict[str, Any]] = None,
        file_format: Optional[str] = None
    ) -> bool:
        """
        Backward compatibility method for debug_dataframe.

        Args:
            df: The DataFrame to save.
            filename: Name of the output file. Auto-generated if None.
            column_formats: Column formatting options for Excel files.
            file_format: File format ('csv' or 'xlsx'). Inferred from filename if None.

        Returns:
            bool: True if successfully queued for saving.
        """
        return self.save_dataframe(df, filename, file_format, column_formats=column_formats)

    @contextmanager
    def batch_save(self):
        """Context manager for batch operations."""
        try:
            yield self
        finally:
            self.wait_for_completion()


# Asynchronous implementation
class AsyncDataFrameDebugger(BaseDataFrameDebugger):
    """Asynchronous DataFrame debugger with asyncio-based processing."""
    
    def __init__(self, max_workers: int = 4, use_process_pool: bool = False, max_queue_size: int = 1000, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Async configuration
        self.max_workers = max(1, max_workers)
        self.use_process_pool = use_process_pool
        self.max_queue_size = max_queue_size
        
        # Async components
        self.task_queue = None
        self.active_tasks = set()
        self.executor = None
        self.is_running = False
        self._shutdown_event = None
        self._worker_tasks = []
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_value, traceback_value):
        await self.stop()
    
    async def start(self):
        """Start the async debugger."""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Initialize async components
        if self.task_queue is None:
            self.task_queue = asyncio.PriorityQueue(maxsize=self.max_queue_size)
        if self._shutdown_event is None:
            self._shutdown_event = asyncio.Event()
        
        self._shutdown_event.clear()
        
        # Create executor
        if self.use_process_pool:
            self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        else:
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Start worker tasks
        for i in range(self.max_workers):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self._worker_tasks.append(task)
    
    async def stop(self, timeout: float = 30.0):
        """Stop the async debugger."""
        if not self.is_running:
            return
        
        self._shutdown_event.set()
        
        # Wait for current tasks
        if self.active_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.active_tasks, return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                self.logger.warning("Some tasks did not complete within timeout")
        
        # Cancel worker tasks
        for task in self._worker_tasks:
            if not task.done():
                task.cancel()
        
        if self._worker_tasks:
            await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        # Shutdown executor
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = None
        
        self.is_running = False
        self._worker_tasks.clear()
        self.active_tasks.clear()
    
    async def save_dataframe(
        self,
        df: pd.DataFrame,
        filename: Optional[str] = None,
        file_format: Optional[Union[str, SaveFormat]] = None,
        column_formats: Optional[Dict[str, Any]] = None,
        priority: Priority = Priority.NORMAL,
        callback: Optional[Callable[[bool, Optional[str]], Awaitable[None]]] = None,
        **metadata
    ) -> str:
        """Queue a DataFrame for async saving."""
        if not self.is_running:
            await self.start()
        
        if df is None or df.empty:
            raise ValueError("Cannot save empty or None DataFrame")
        
        filename, save_format = self._generate_filename(filename, file_format)
        file_path = self.path / filename
        
        task_id = f"task_{self._task_counter}_{int(time.time() * 1000)}"
        self._task_counter += 1
        
        task = SaveTask(
            df=df,
            file_path=file_path,
            file_format=save_format,
            column_formats=column_formats or {},
            priority=priority,
            callback=callback,
            metadata={'task_id': task_id, **metadata}
        )
        
        # Queue task (priority queue uses negative values for higher priority)
        priority_value = -priority.value
        await self.task_queue.put((priority_value, task_id, task))
        
        # Update stats
        self.stats.queue_high_water_mark = max(
            self.stats.queue_high_water_mark,
            self.task_queue.qsize()
        )
        
        await self._notify_progress("queued", task_id, filename)
        return task_id
    
    async def _worker(self, worker_name: str):
        """Async worker to process save tasks."""
        while not self._shutdown_event.is_set():
            try:
                try:
                    priority, task_id, task = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                process_task = asyncio.create_task(self._process_task(task, worker_name))
                self.active_tasks.add(process_task)
                
                try:
                    await process_task
                finally:
                    self.active_tasks.discard(process_task)
                    self.task_queue.task_done()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")
    
    async def _process_task(self, task: SaveTask, worker_name: str):
        """Process a single save task."""
        task_id = task.metadata.get('task_id', 'unknown')
        start_time = time.time()
        
        await self._notify_progress("processing", task_id, str(task.file_path.name))
        
        try:
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self.executor,
                self._save_with_retries,
                task
            )
            
            processing_time = time.time() - start_time
            
            if success:
                await self._notify_progress("completed", task_id, str(task.file_path.name))
            else:
                await self._notify_progress("failed", task_id, str(task.file_path.name))
            
            # Update average processing time
            total_files = self.stats.files_saved + self.stats.files_failed
            if total_files > 0:
                self.stats.avg_processing_time = (
                    (self.stats.avg_processing_time * (total_files - 1) + processing_time) / total_files
                )
            
            # Call callback if provided
            if task.callback:
                try:
                    await task.callback(success, None if success else "Save failed")
                except Exception as e:
                    self.logger.warning(f"Callback error for task {task_id}: {e}")
                    
        except Exception as e:
            self.stats.files_failed += 1
            await self._notify_progress("error", task_id, str(task.file_path.name))
            self.logger.error(f"Task {task_id} failed with error: {e}")
            
            if task.callback:
                try:
                    await task.callback(False, str(e))
                except Exception as callback_error:
                    self.logger.warning(f"Callback error for task {task_id}: {callback_error}")
    
    async def _notify_progress(self, status: str, task_id: str, filename: str):
        """Notify progress callbacks."""
        if not self.enable_progress_callback or not self.progress_callbacks:
            return
        
        progress_data = {
            'status': status,
            'task_id': task_id,
            'filename': filename,
            'timestamp': datetime.now(),
            'stats': self.stats.__dict__.copy()
        }
        
        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress_data)
                else:
                    callback(progress_data)
            except Exception as e:
                self.logger.warning(f"Progress callback error: {e}")
    
    async def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for all queued tasks to complete."""
        try:
            if timeout is None:
                await self.task_queue.join()
                return True
            else:
                await asyncio.wait_for(self.task_queue.join(), timeout=timeout)
                return True
        except asyncio.TimeoutError:
            return False
    
    async def get_queue_size(self) -> int:
        """Get current queue size."""
        return self.task_queue.qsize()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        stats = self.stats.__dict__.copy()
        stats['queue_size'] = await self.get_queue_size()
        stats['active_tasks'] = len(self.active_tasks)
        stats['is_running'] = self.is_running
        return stats
    
    @asynccontextmanager
    async def batch_save(self):
        """Async context manager for batch operations."""
        try:
            yield self
        finally:
            await self.wait_for_completion()
    
    async def save_multiple(
        self,
        dataframes: List[tuple],  # [(df, filename, options), ...]
        priority: Priority = Priority.NORMAL
    ) -> List[str]:
        """Save multiple DataFrames concurrently."""
        tasks = []
        for item in dataframes:
            if len(item) == 2:
                df, filename = item
                options = {}
            else:
                df, filename, options = item
            
            task_id = await self.save_dataframe(
                df, filename, priority=priority, **options
            )
            tasks.append(task_id)
        
        return tasks


# Factory pattern for easy instantiation
class DataFrameDebuggerFactory:
    """Factory for creating DataFrame debuggers."""
    
    @staticmethod
    def create_debugger(
        mode: ExecutionMode,
        path: Optional[Union[str, Path]] = None,
        **kwargs
    ) -> Union[SyncDataFrameDebugger, AsyncDataFrameDebugger]:
        """Create a DataFrame debugger based on execution mode."""
        if mode == ExecutionMode.SYNC:
            return SyncDataFrameDebugger(path=path, **kwargs)
        elif mode == ExecutionMode.ASYNC:
            return AsyncDataFrameDebugger(path=path, **kwargs)
        else:
            raise ValueError(f"Unsupported execution mode: {mode}")
    
    @staticmethod
    def create_sync_debugger(path: Optional[Union[str, Path]] = None, **kwargs) -> SyncDataFrameDebugger:
        """Create a synchronous DataFrame debugger."""
        return SyncDataFrameDebugger(path=path, **kwargs)
    
    @staticmethod
    def create_async_debugger(path: Optional[Union[str, Path]] = None, **kwargs) -> AsyncDataFrameDebugger:
        """Create an asynchronous DataFrame debugger."""
        return AsyncDataFrameDebugger(path=path, **kwargs)


# Convenience functions
def quick_save_dataframe(
    df: pd.DataFrame,
    filename: Optional[str] = None,
    path: Optional[str] = None,
    file_format: Optional[str] = None,
    **kwargs
) -> bool:
    """Quick utility to save a DataFrame synchronously."""
    try:
        kwargs.setdefault('log_level', logging.WARNING)
        
        with SyncDataFrameDebugger(path=path, **kwargs) as debugger:
            success = debugger.save_dataframe(df, filename, file_format)
            if success:
                debugger.wait_for_completion()
            return success
    except Exception as e:
        print(f"Quick save failed: {e}")
        return False


async def quick_save_dataframe_async(
    df: pd.DataFrame,
    filename: Optional[str] = None,
    path: Optional[str] = None,
    file_format: Optional[str] = None,
    **kwargs
) -> bool:
    """Quick utility to save a DataFrame asynchronously."""
    try:
        kwargs.setdefault('log_level', logging.WARNING)
        
        async with AsyncDataFrameDebugger(path=path, **kwargs) as debugger:
            await debugger.save_dataframe(df, filename, file_format)
            return await debugger.wait_for_completion(timeout=60.0)
    except Exception as e:
        print(f"Quick async save failed: {e}")
        return False


def quick_save_dataframe_minimal(
    df: pd.DataFrame,
    filename: Optional[str] = None,
    path: Optional[str] = None,
    file_format: str = "xlsx"
) -> bool:
    """Minimal DataFrame save without logging (for high-frequency operations)."""
    try:
        if df is None or df.empty:
            return False
        
        # Setup path
        if path is None:
            path = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp')
        else:
            path = Path(path)
        
        path.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"dataframe_{timestamp}.{file_format}"
        
        file_path = path / filename
        
        # Save based on format
        if file_format.lower() in ['xlsx', 'excel']:
            df.to_excel(file_path, index=False, engine="openpyxl")
        elif file_format.lower() == 'csv':
            df.to_csv(file_path, index=False, encoding='utf-8')
        elif file_format.lower() == 'parquet':
            df.to_parquet(file_path, index=False)
        elif file_format.lower() == 'feather':
            df.to_feather(file_path)
        elif file_format.lower() == 'json':
            df.to_json(file_path, orient='records', date_format='iso')
        else:
            raise ValueError(f"Unsupported format: {file_format}")
        
        return True
        
    except Exception as e:
        print(f"Minimal quick save failed: {e}")
        return False


async def save_dataframes_parallel(
    dataframes: Dict[str, pd.DataFrame],
    path: Optional[str] = None,
    file_format: str = "xlsx",
    max_workers: int = 4
) -> Dict[str, bool]:
    """Save multiple DataFrames in parallel."""
    results = {}
    
    async def save_callback(success: bool, error: Optional[str], filename: str):
        results[filename] = success
        if not success:
            print(f"Failed to save {filename}: {error}")
    
    async with AsyncDataFrameDebugger(path=path, max_workers=max_workers) as debugger:
        tasks = []
        for filename, df in dataframes.items():
            callback = lambda s, e, f=filename: save_callback(s, e, f)
            task_id = await debugger.save_dataframe(
                df, filename, file_format, callback=callback
            )
            tasks.append(task_id)
        
        await debugger.wait_for_completion()
    
    return results


# Cleanup function
def cleanup_logger_cache():
    """Clean up cached loggers and handlers."""
    LoggerMixin.cleanup_logger_cache()


# Register cleanup
atexit.register(cleanup_logger_cache)


# Backward compatibility aliases and functions
DataframeDebugger = SyncDataFrameDebugger  # For backward compatibility
AsyncDataframeDebugger = AsyncDataFrameDebugger  # Keep the same name

# Add backward compatibility for quick_save_async (used in codebase)
quick_save_async = quick_save_dataframe_async


# Example usage
async def example_usage():
    """Example usage of the refactored DataFrame debugger."""
    import numpy as np
    
    # Create sample data
    sample_df = pd.DataFrame({
        'id': range(1, 1001),
        'name': [f'Item_{i}' for i in range(1, 1001)],
        'value': np.random.randn(1000),
        'timestamp': pd.date_range('2024-01-01', periods=1000, freq='h'),
        'category': np.random.choice(['A', 'B', 'C', 'D'], 1000)
    })
    
    # Progress callback
    def progress_callback(data):
        print(f"Task {data['task_id']}: {data['status']} - {data['filename']}")
    
    # Example 1: Using factory pattern
    sync_debugger = DataFrameDebuggerFactory.create_sync_debugger(max_workers=4)
    async_debugger = DataFrameDebuggerFactory.create_async_debugger(max_workers=4)
    
    # Example 2: Synchronous usage
    with sync_debugger as debugger:
        debugger.add_progress_callback(progress_callback)
        
        success1 = debugger.save_dataframe(sample_df, "sync_sample.xlsx")
        success2 = debugger.save_dataframe(sample_df, "sync_sample.csv", file_format="csv")
        
        debugger.wait_for_completion()
        print(f"Sync stats: {debugger.stats.__dict__}")
    
    # Example 3: Asynchronous usage
    async with async_debugger as debugger:
        debugger.add_progress_callback(progress_callback)
        
        task1 = await debugger.save_dataframe(sample_df, "async_sample.xlsx")
        task2 = await debugger.save_dataframe(sample_df, "async_sample.csv", file_format="csv")
        task3 = await debugger.save_dataframe(sample_df, "async_sample.parquet", file_format="parquet")
        
        await debugger.wait_for_completion()
        stats = await debugger.get_stats()
        print(f"Async stats: {stats}")
    
    # Example 4: Batch operations with different priorities
    async with AsyncDataFrameDebugger() as debugger:
        async with debugger.batch_save():
            for i in range(5):
                subset_df = sample_df.iloc[i * 200:(i + 1) * 200]
                await debugger.save_dataframe(
                    subset_df,
                    f"batch_high_{i}.xlsx",
                    priority=Priority.HIGH
                )
                await debugger.save_dataframe(
                    subset_df,
                    f"batch_normal_{i}.xlsx",
                    priority=Priority.NORMAL
                )
    
    # Example 5: Quick save utilities
    success = quick_save_dataframe(sample_df, "quick_sync.xlsx")
    print(f"Quick sync save: {success}")
    
    success = await quick_save_dataframe_async(sample_df, "quick_async.xlsx")
    print(f"Quick async save: {success}")
    
    # Example 6: Parallel save utility
    dataframes = {
        f"parallel_{i}.xlsx": sample_df.iloc[i * 100:(i + 1) * 100]
        for i in range(10)
    }
    
    results = await save_dataframes_parallel(dataframes, max_workers=6)
    print(f"Parallel save results: {results}")
    
    # Example 7: Minimal save for high-frequency operations
    for i in range(100):
        small_df = sample_df.iloc[i * 10:(i + 1) * 10]
        success = quick_save_dataframe_minimal(
            small_df, 
            f"minimal_{i}.csv", 
            file_format="csv"
        )


if __name__ == "__main__":
    # Run synchronous examples
    print("Running synchronous examples...")
    
    import numpy as np
    sample_df = pd.DataFrame({
        'id': range(1, 101),
        'name': [f'Item_{i}' for i in range(1, 101)],
        'value': np.random.randn(100),
        'timestamp': pd.date_range('2024-01-01', periods=100, freq='D'),
        'category': np.random.choice(['A', 'B', 'C'], 100)
    })
    
    # Test synchronous debugger
    with SyncDataFrameDebugger() as debugger:
        debugger.save_dataframe(sample_df, "test_sync.xlsx")
        debugger.save_dataframe(sample_df, "test_sync.csv")
        debugger.wait_for_completion()
        print(f"Sync completion stats: {debugger.stats.__dict__}")
    
    # Test quick save
    success = quick_save_dataframe(sample_df, "test_quick.xlsx")
    print(f"Quick save success: {success}")
    
    # Run async examples
    print("\nRunning asynchronous examples...")
    asyncio.run(example_usage())