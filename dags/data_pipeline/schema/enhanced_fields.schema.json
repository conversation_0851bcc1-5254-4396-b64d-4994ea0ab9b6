{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Enhanced Issue Fields Schema", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "items": {"type": "object", "required": ["id", "datatype", "custom", "name"], "properties": {"id": {"type": "string"}, "datatype": {"type": "string"}, "custom": {"type": "boolean"}, "name": {"type": "string"}, "mapping": {"type": "object", "additionalProperties": {"type": "string"}}, "target_type": {"type": "string"}}}}}, "additionalProperties": false}