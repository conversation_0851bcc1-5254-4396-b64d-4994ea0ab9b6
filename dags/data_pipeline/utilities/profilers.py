# coding=utf-8
import cProfile
import io
import pstats
import contextlib
from logging import Logger

from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import Logger<PERSON>ontainer


@contextlib.contextmanager
@inject
def profiled(
        profiled_logger: Logger = Provide[LoggerContainer.profiled_logger]
):
    pr = cProfile.Profile()
    pr.enable()
    yield
    pr.disable()
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats()
    # uncomment this to see who's calling what
    ps.print_callers()
    profiled_logger.debug(s.getvalue())

logger_container = LoggerContainer()
logger_container.init_resources()