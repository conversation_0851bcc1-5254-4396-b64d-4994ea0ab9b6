import hashlib
import random
import string

from passlib.context import CryptContext


def generate_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def verify_password(input_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(input_password, hashed_password)


# Set up Passlib context with Argon2
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")


def generate_password(length: int = 12):
    characters = string.ascii_letters + string.digits + string.punctuation
    return ''.join(random.choice(characters) for _ in range(length))


def md5_hash(password, role_name):
    raw = (password + role_name).encode()
    return "md5" + hashlib.md5(raw).hexdigest()
