# coding=utf-8
"""
Priority Queue System for JIRA Data Pipeline.

This module provides a priority queue system with composite keys for managing
message priorities across different queue types and processors.
"""

import asyncio
import threading
from dataclasses import dataclass, field
from typing import Any, Optional, Union
from enum import IntEnum


class MessageType(IntEnum):
    """Message type priorities - lower numbers = higher priority"""
    ISSUE_DIRECT = 1      # Messages from consume_issues/IssueProcessor to queue_upsert_issue
    SPECIALIZED = 2       # Messages from specialized processors to queue_upsert_issue
    REGULAR_DATA = 3      # Regular data messages between queues
    TERMINATION = 999     # None/termination signals


@dataclass(order=True)
class PriorityMessage:
    """
    Priority message wrapper with composite key for priority queues.
    
    Priority is determined by:
    1. message_type_priority (lower = higher priority)
    2. task_id (for ordering within same priority)
    3. counter (unique sequence number)
    """
    priority: int = field(init=False)
    message_type_priority: int = field(compare=False)
    task_id: int = field(compare=False)
    counter: int = field(compare=False)
    data: Any = field(compare=False)
    
    def __post_init__(self):
        """Calculate composite priority key"""
        # Composite priority: (message_type_priority, task_id, counter)
        # Each component gets allocated specific bit ranges to avoid conflicts
        self.priority = (
            (self.message_type_priority << 32) +  # High bits for message type
            (self.task_id << 16) +                # Middle bits for task ID
            self.counter                          # Low bits for counter
        )


class PriorityQueueManager:
    """
    Manages priority queue operations with automatic counter generation.
    
    Provides thread-safe counter generation and helper methods for creating
    priority messages with proper composite keys.
    """
    
    def __init__(self):
        self._counter_lock = threading.Lock()
        self._global_counter = 0
        self._task_counters = {}  # Per-task counters for uniqueness
    
    def _get_next_counter(self, task_id: int) -> int:
        """Get next unique counter for a specific task"""
        with self._counter_lock:
            if task_id not in self._task_counters:
                self._task_counters[task_id] = 0
            self._task_counters[task_id] += 1
            return self._task_counters[task_id]
    
    def create_priority_message(
        self,
        data: Any,
        message_type: MessageType,
        task_id: int
    ) -> PriorityMessage:
        """
        Create a priority message with automatic counter generation.
        
        Args:
            data: The actual message data (can be None for termination)
            message_type: Type of message determining priority
            task_id: Task/producer/consumer ID for ordering
            
        Returns:
            PriorityMessage with proper composite key
        """
        counter = self._get_next_counter(task_id)
        
        return PriorityMessage(
            message_type_priority=message_type.value,
            task_id=task_id,
            counter=counter,
            data=data
        )
    
    async def put_priority_message(
        self,
        queue: asyncio.PriorityQueue,
        data: Any,
        message_type: MessageType,
        task_id: int
    ) -> None:
        """
        Put a message into priority queue with automatic priority assignment.
        
        Args:
            queue: The priority queue to put message into
            data: The message data
            message_type: Type of message for priority determination
            task_id: Task/producer/consumer ID
        """
        priority_msg = self.create_priority_message(data, message_type, task_id)
        await queue.put(priority_msg)
    
    async def get_priority_message(
        self,
        queue: asyncio.PriorityQueue
    ) -> Any:
        """
        Get a message from priority queue, returning just the data.
        
        Args:
            queue: The priority queue to get message from
            
        Returns:
            The original message data (unwrapped from PriorityMessage)
        """
        priority_msg: PriorityMessage = await queue.get()
        return priority_msg.data


# Global instance for use across the application
priority_queue_manager = PriorityQueueManager()
