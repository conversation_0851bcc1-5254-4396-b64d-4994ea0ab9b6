# yaml-language-server: $schema=./schema/enhanced_fields.schema.json
fields:
  - id: id
    name: "Issue ID"
    custom: false
    datatype: string
    source_column: id
    target_column: id
    target_type: int64

  - id: author
    name: "Author"
    custom: false
    datatype: string
    source_column: author.accountId
    target_column: author
    target_type: string
    
  - id: body
    name: "Body"
    custom: false
    datatype: json
    source_column: body
    target_column: body
    target_type: json

  - id: renderedBody
    name: "renderedBody"
    custom: false
    datatype: string
    target_type: string
    
  - id: updateAuthor
    name: "Update Author"
    custom: false
    datatype: string
    source_column: updateAuthor.accountId
    target_column: updateAuthor
    target_type: string
    
  - id: created
    name: "Created"
    custom: false
    datatype: string
    source_column: created
    target_column: created
    target_type: datetime
    
  - id: updated
    name: "Updated"
    custom: false
    datatype: string
    source_column: updated
    target_column: updated
    target_type: datetime
    
  - id: jsdPublic
    name: "JSD Public"
    custom: false
    datatype: string
    target_type: bool

  - id: issue_key
    name: "Issue Key Final"
    custom: false
    datatype: string
    target_type: string

  - id: issue_id
    name: "Issue ID Final"
    custom: false
    source_column: issue_id
    target_column: issue_id
    datatype: string
    target_type: int64

database_config:
  model: IssueComments
  no_update_cols: ["body"]

drop-column-prefixes:
  - self
  - maxResults
  - total
  - startAt

