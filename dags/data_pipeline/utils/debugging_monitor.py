import logging
import platform
import sys
import threading
import time
import traceback
import weakref
from collections import deque, defaultdict
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass, field
from typing import Any, Dict, Optional, Union
from enum import Enum



class LogLevel(Enum):
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


@dataclass
class MonitorConfig:
    """Configuration for the debugging monitor."""
    enabled: bool = False
    log_interval: int = 30
    max_queue_size_warning: int = 100
    slow_operation_threshold: float = 5.0
    memory_warning_threshold: float = 80.0
    log_suppression_interval: int = 300
    max_log_entries: int = 1000
    batch_size: int = 100
    high_volume_threshold: int = 1000  # requests per minute
    adaptive_sampling: bool = True
    stack_trace_on_deadlock: bool = True


@dataclass
class ActivityMetrics:
    """Lightweight metrics container."""
    count: int = 0
    last_activity: float = field(default_factory=time.time)
    errors: int = 0
    rate_limits: int = 0

    def increment(self, error: bool = False, rate_limit: bool = False):
        self.count += 1
        self.last_activity = time.time()
        if error:
            self.errors += 1
        if rate_limit:
            self.rate_limits += 1


class AdaptiveSampler:
    """Adaptive sampling to reduce overhead during high-volume periods."""

    def __init__(self, high_volume_threshold: int = 1000):
        self.high_volume_threshold = high_volume_threshold
        self.window_size = 60  # 1-minute window
        self.activity_window = deque(maxlen=self.high_volume_threshold)
        self.sample_rate = 1.0  # Start with 100% sampling

    def should_sample(self) -> bool:
        """Determine if this event should be sampled."""
        current_time = time.time()

        # Remove old entries from window
        while self.activity_window and current_time - self.activity_window[0] > self.window_size:
            self.activity_window.popleft()

        # Add current activity
        self.activity_window.append(current_time)

        # Adjust sampling rate based on volume
        current_rate = len(self.activity_window)
        if current_rate > self.high_volume_threshold:
            # High volume: reduce sampling
            self.sample_rate = max(0.1, self.high_volume_threshold / current_rate)
        else:
            # Normal volume: increase sampling back to normal
            self.sample_rate = min(1.0, self.sample_rate * 1.1)

        return hash(current_time) % 100 < (self.sample_rate * 100)


def _get_memory_info() -> Union[Dict[str, Any], Dict[str, str]]:
    """Get memory usage information with graceful fallback."""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "percent": process.memory_percent()
        }
    except ImportError:
        return {"error": "psutil not available"}
    except Exception as e:
        return {"error": f"Memory check failed: {e}"}


class DebuggingMonitor:
    """
    Platform-agnostic debugging monitor with context manager support,
    adaptive sampling, and intelligent resource management.
    """

    def __init__(self, config: Optional[MonitorConfig] = None):
        self.config = config or MonitorConfig()
        self.logger = logging.getLogger("debugging_monitor")
        self.start_time = time.time()

        # Lightweight metrics using defaultdict for automatic initialization
        self.metrics = defaultdict(ActivityMetrics)

        # Use deque for efficient log rotation
        self.recent_events = deque(maxlen=self.config.max_log_entries)

        # Thread-safe monitoring
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        self._lock = threading.RLock()

        # Weak references to prevent memory leaks
        self._active_tasks = weakref.WeakSet()
        self._active_queues = weakref.WeakSet()

        # Adaptive sampling for high-volume scenarios
        self.sampler = AdaptiveSampler(self.config.high_volume_threshold) if self.config.adaptive_sampling else None

        # Warning suppression
        self._last_warnings = {}

        # Platform detection
        self.platform_info = {
            'system': platform.system(),
            'release': platform.release(),
            'architecture': platform.architecture()[0]
        }

    def __enter__(self):
        """Context manager entry."""
        if self.config.enabled:
            self.start_monitoring()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.stop()
        if exc_type:
            self.logger.error(f"Exception in monitoring context: {exc_type.__name__}: {exc_val}")
            if self.config.stack_trace_on_deadlock:
                self.dump_stack_traces()

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for asyncio applications."""
        try:
            if self.config.enabled:
                self.start_monitoring()
            yield self
        finally:
            self.stop()

    @contextmanager
    def operation_context(self, operation_name: str):
        """Context manager for tracking individual operations."""
        start_time = time.time()
        self.log_task_start(operation_name)
        success = False

        try:
            yield
            success = True
        except Exception:
            success = False
            self.metrics[operation_name].increment(error=True)
            raise
        finally:
            duration = time.time() - start_time
            self.log_task_end(operation_name, success)

            # Log slow operations
            if duration > self.config.slow_operation_threshold:
                self._log_if_sampled(
                    logging.WARNING,
                    f"Slow operation: {operation_name} took {duration:.2f}s",
                )

    def start_monitoring(self):
        """Start the monitoring thread."""
        if not self.config.enabled:
            return

        with self._lock:
            if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
                self.stop_monitoring.clear()
                self.monitoring_thread = threading.Thread(
                    target=self._monitor_loop,
                    daemon=True,
                    name="DebugMonitor"
                )
                self.monitoring_thread.start()
                self.logger.info(f"Debugging monitor started on {self.platform_info['system']}")

    def stop(self):
        """Stop the monitoring thread with proper cleanup."""
        self.stop_monitoring.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)

        # Final report
        if self.config.enabled:
            self._log_final_report()

        self.logger.info("Debugging monitor stopped")

    def _monitor_loop(self):
        """Main monitoring loop with exception handling."""
        while not self.stop_monitoring.wait(self.config.log_interval):
            try:
                self._log_system_state()
            except Exception as e:
                # Use basic logging to avoid recursion
                print(f"Monitor loop error: {e}", file=sys.stderr)

    def _should_sample(self) -> bool:
        """Determine if this event should be logged based on sampling."""
        if not self.sampler:
            return True
        return self.sampler.should_sample()

    def _log_if_sampled(self, level: int, message: str):
        """Log message only if sampling allows it."""
        if self._should_sample():
            self.logger.log(level, message)

    def _log_system_state(self):
        """Log system state with intelligent batching and thresholds."""
        if not self._should_log_state():
            return

        current_time = time.time()
        uptime = current_time - self.start_time

        # Gather metrics efficiently
        total_requests = sum(m.count for m in self.metrics.values())
        total_errors = sum(m.errors for m in self.metrics.values())
        total_rate_limits = sum(m.rate_limits for m in self.metrics.values())

        # Determine criticality
        is_critical = (
                total_rate_limits > 20 or
                len(self._active_tasks) > 50 or
                (total_requests > 0 and total_errors / total_requests > 0.1)
        )

        level = logging.ERROR if is_critical else logging.INFO

        # Compact state report
        state_summary = {
            'uptime': f"{uptime:.1f}s",
            'platform': f"{self.platform_info['system']} {self.platform_info['release']}",
            'requests': total_requests,
            'errors': total_errors,
            'rate_limits': total_rate_limits,
            'active_tasks': len(self._active_tasks),
            'active_queues': len(self._active_queues),
            'threads': threading.active_count(),
            'sampling_rate': f"{self.sampler.sample_rate:.2f}" if self.sampler else "1.00"
        }

        # Add memory info if available
        memory_info = _get_memory_info()
        if isinstance(memory_info, dict) and 'percent' in memory_info:
            state_summary['memory_pct'] = f"{memory_info['percent']:.1f}%"

        self.logger.log(level, f"Monitor Report: {state_summary}")
        self._last_warnings['system_state'] = current_time

    def _should_log_state(self) -> bool:
        """Determine if state should be logged based on various factors."""
        current_time = time.time()
        last_log = self._last_warnings.get('system_state', 0)

        # Force log if critical conditions
        if self._has_critical_conditions():
            return True

        # Normal interval check
        return current_time - last_log >= self.config.log_suppression_interval

    def _has_critical_conditions(self) -> bool:
        """Check for critical conditions that require immediate logging."""
        total_rate_limits = sum(m.rate_limits for m in self.metrics.values())
        return (
                total_rate_limits > 20 or
                len(self._active_tasks) > 100 or
                threading.active_count() > 50
        )

    def _log_final_report(self):
        """Log final summary report."""
        total_uptime = time.time() - self.start_time
        total_requests = sum(m.count for m in self.metrics.values())
        total_errors = sum(m.errors for m in self.metrics.values())

        self.logger.info(f"""
=== FINAL DEBUGGING REPORT ===
Total Uptime: {total_uptime:.1f}s
Total Requests: {total_requests}
Total Errors: {total_errors}
Error Rate: {(total_errors / total_requests * 100) if total_requests > 0 else 0:.2f}%
Peak Active Tasks: {len(self._active_tasks)}
Platform: {self.platform_info['system']} {self.platform_info['release']}
=== END FINAL REPORT ===
        """)

    # Simplified logging methods with sampling
    def log_http_request(self, method: str, url: str, status: Optional[int] = None):
        """Log HTTP request with adaptive sampling."""
        if not self.config.enabled:
            return

        is_error = status and status >= 400
        is_rate_limit = status == 429

        self.metrics['http'].increment(error=is_error, rate_limit=is_rate_limit)

        if is_rate_limit and self._should_sample():
            self.logger.warning(f"Rate limit: {method} {url}")

    def log_queue_activity(self, queue_name: str, operation: str, queue_size: int):
        """Log queue activity with threshold checking."""
        if not self.config.enabled:
            return

        self.metrics[f'queue_{queue_name}_{operation}'].increment()

        if queue_size > self.config.max_queue_size_warning and self._should_sample():
            level = logging.ERROR if queue_size > self.config.max_queue_size_warning * 2 else logging.WARNING
            self.logger.log(level, f"Large queue: {queue_name} has {queue_size} items")

    def log_db_operation(self, operation: str, duration: Optional[float] = None):
        """Log database operation."""
        if not self.config.enabled:
            return

        is_slow = duration and duration > self.config.slow_operation_threshold
        self.metrics['db'].increment(error=is_slow)

        if is_slow and self._should_sample():
            self.logger.warning(f"Slow DB operation: {operation} took {duration:.2f}s")

    def log_task_start(self, task_name: str):
        """Log task start."""
        if not self.config.enabled:
            return
        self.metrics[f'task_{task_name}'].last_activity = time.time()

    def log_task_end(self, task_name: str, success: bool = True):
        """Log task completion."""
        if not self.config.enabled:
            return
        self.metrics[f'task_{task_name}'].increment(error=not success)

    def register_queue(self, queue):
        """Register a queue for monitoring."""
        if self.config.enabled:
            self._active_queues.add(queue)

    def register_task(self, task):
        """Register a task for monitoring."""
        if self.config.enabled:
            self._active_tasks.add(task)

    def dump_stack_traces(self):
        """Dump stack traces with filtering."""
        if not self.config.enabled:
            return

        self.logger.error("=== STACK TRACE DUMP ===")

        # Filter out system threads
        skip_patterns = {'logging', 'queue', '_monitor', 'dummy', 'thread'}

        for thread_id, frame in sys._current_frames().items():
            try:
                thread = None
                for t in threading.enumerate():
                    if t.ident == thread_id:
                        thread = t
                        break

                if thread and not any(pattern in thread.name.lower() for pattern in skip_patterns):
                    self.logger.error(f"Thread {thread.name} ({thread_id}):")
                    # Limit stack trace depth
                    stack = traceback.format_stack(frame)[-10:]  # Last 10 frames
                    self.logger.error(''.join(stack))

            except Exception as e:
                self.logger.error(f"Error dumping stack for thread {thread_id}: {e}")

        self.logger.error("=== END STACK TRACE DUMP ===")

    def manual_check(self):
        """Manual check for on-demand debugging."""
        if self.config.enabled:
            self._log_system_state()

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get current metrics summary."""
        return {
            name: {
                'count': metrics.count,
                'errors': metrics.errors,
                'rate_limits': metrics.rate_limits,
                'last_activity': metrics.last_activity
            }
            for name, metrics in self.metrics.items()
        }


# Convenience functions for common usage patterns
def create_monitor(enabled: bool = True, **kwargs) -> DebuggingMonitor:
    """Create a debugging monitor with common configuration."""
    config = MonitorConfig(enabled=enabled, **kwargs)
    return DebuggingMonitor(config)


@contextmanager
def debug_monitor(enabled: bool = True, **kwargs):
    """Context manager for quick debugging sessions."""
    with create_monitor(enabled=enabled, **kwargs) as monitor:
        yield monitor


# Example usage patterns
if __name__ == "__main__":
    # Example 1: Basic context manager usage
    with debug_monitor(enabled=True, log_interval=10) as monitor_main:
        monitor_main.log_http_request("GET", "/api/test", 200)

        # Track an operation
        with monitor_main.operation_context("data_processing"):
            time.sleep(0.1)  # Simulate work

    # Example 2: High-volume scenario with adaptive sampling
    monitor_config = MonitorConfig(
        enabled=True,
        adaptive_sampling=True,
        high_volume_threshold=500,
        log_suppression_interval=60
    )

    with DebuggingMonitor(monitor_config) as monitor_main:
        # Simulate high-volume requests
        for i in range(1000):
            monitor_main.log_http_request("POST", f"/api/item/{i}", 200)
            if i % 100 == 0:
                print(f"Processed {i} requests, sampling rate: {monitor_main.sampler.sample_rate:.2f}")
