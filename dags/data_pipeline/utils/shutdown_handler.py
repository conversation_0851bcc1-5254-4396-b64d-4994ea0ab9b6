import asyncio
import logging

class ApplicationShutdownHandler:
    """
    Enhanced application shutdown handler with critical error detection.

    This class improves upon the basic ApplicationShutdownHandler by using asyncio.Condition
    for better coordination between multiple producers/consumers and includes critical error
    monitoring that can trigger automatic shutdown when unrecoverable errors occur.
    """

    # Critical error patterns that should trigger shutdown
    CRITICAL_PATTERNS = {
        "Cannot recover session state": "CRIT<PERSON><PERSON>",
        "closed transaction inside context manager": "CR<PERSON><PERSON><PERSON>",
        "another operation is in progress": "RECOVERABLE",
        "generator didn't stop": "CRITIC<PERSON>",
        "'Provide' object has no attribute": "CRITIC<PERSON>",
        "RuntimeError: Cannot recover session state": "<PERSON><PERSON><PERSON><PERSON>",
        "Can't operate on closed transaction": "CRITIC<PERSON>",
    }

    def __init__(self):
        """Initialize the enhanced shutdown handler."""
        self._shutdown_condition = asyncio.Condition()
        self._shutdown_requested = False
        self._active_components: set = set()
        self._setup_signal_handlers()
        self.logger = logging.getLogger(__name__)

        # Critical error tracking
        self._critical_errors = []
        self._error_counts = {}
        self._last_error_time = {}
        self.max_critical_errors = 3
        self.max_recoverable_errors = 10
        self.error_window_minutes = 5

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        import signal

        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating enhanced shutdown...")
            asyncio.create_task(self.shutdown())

        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

    async def register_component(self, component_name: str):
        """
        Register a component for shutdown coordination.

        Args:
            component_name: Name of the component to register
        """
        async with self._shutdown_condition:
            self._active_components.add(component_name)
            self.logger.debug(f"Registered component: {component_name}")

    async def unregister_component(self, component_name: str):
        """
        Unregister a component and notify waiting shutdown processes.

        Args:
            component_name: Name of the component to unregister
        """
        async with self._shutdown_condition:
            if component_name in self._active_components:
                self._active_components.remove(component_name)
                self.logger.debug(f"Unregistered component: {component_name}")

                # Notify waiting shutdown processes
                self._shutdown_condition.notify_all()

    async def shutdown(self, timeout: float = 30.0):
        """
        Perform enhanced graceful shutdown with better coordination.

        Args:
            timeout: Maximum time to wait for component shutdown
        """
        async with self._shutdown_condition:
            if self._shutdown_requested:
                return

            self._shutdown_requested = True
            self.logger.info("Starting enhanced graceful shutdown...")

            # Wait for all components to unregister themselves
            if self._active_components:
                try:
                    await asyncio.wait_for(
                        self._shutdown_condition.wait_for(lambda: not self._active_components),
                        timeout=timeout
                    )
                    self.logger.info("All components shut down successfully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"Timeout waiting for component shutdown. "
                                      f"Active components: {self._active_components}")

        try:
            # Cleanup all session manager instances
            await self._cleanup_session_managers()
            self.logger.info("All database connections cleaned up successfully")
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

        self.logger.info("Enhanced graceful shutdown completed")


    def is_shutting_down(self) -> bool:
        """Check if shutdown is in progress."""
        return self._shutdown_requested

    async def wait_for_shutdown(self):
        """Wait for shutdown to be requested."""
        async with self._shutdown_condition:
            await self._shutdown_condition.wait_for(lambda: self._shutdown_requested)

    def analyze_error_severity(self, error_message: str) -> str:
        """
        Analyze an error message and determine its severity.

        Args:
            error_message: The error message to analyze

        Returns:
            str: Severity level ('WARNING', 'RECOVERABLE', 'CRITICAL', 'FATAL')
        """
        error_message_lower = error_message.lower()

        for pattern, severity in self.CRITICAL_PATTERNS.items():
            if pattern.lower() in error_message_lower:
                return severity

        # Check for specific error types
        if "runtimeerror" in error_message_lower:
            return "CRITICAL"
        if "typeerror" in error_message_lower and "provide" in error_message_lower:
            return "CRITICAL"
        if "sqlalchemy" in error_message_lower and ("closed" in error_message_lower or
                                                   "transaction" in error_message_lower):
            return "CRITICAL"

        return "WARNING"

    async def record_critical_error(self, error_message: str, module: str = "unknown",
                                  function: str = "unknown", line_number: int = None) -> bool:
        """
        Record a critical error and determine if shutdown should be triggered.

        Args:
            error_message: The error message
            module: Module where the error occurred
            function: Function where the error occurred
            line_number: Line number where the error occurred

        Returns:
            bool: True if shutdown should be triggered, False otherwise
        """
        if self._shutdown_requested:
            return True

        from datetime import datetime, timedelta

        severity = self.analyze_error_severity(error_message)
        now = datetime.now()

        # Create error record
        error_record = {
            'timestamp': now,
            'error_message': error_message,
            'severity': severity,
            'module': module,
            'function': function,
            'line_number': line_number
        }

        self._critical_errors.append(error_record)

        # Update error counts
        error_key = f"{severity}:{module}:{function}"
        self._error_counts[error_key] = self._error_counts.get(error_key, 0) + 1
        self._last_error_time[error_key] = now

        # Log the error
        if severity == "FATAL":
            self.logger.critical(f"FATAL ERROR in {module}.{function}: {error_message}")
        elif severity == "CRITICAL":
            self.logger.error(f"CRITICAL ERROR in {module}.{function}: {error_message}")
        elif severity == "RECOVERABLE":
            self.logger.warning(f"RECOVERABLE ERROR in {module}.{function}: {error_message}")
        else:
            self.logger.info(f"ERROR in {module}.{function}: {error_message}")

        # Check if shutdown should be triggered
        should_shutdown = await self._should_trigger_shutdown(severity, error_key, now)

        if should_shutdown:
            await self.shutdown_due_to_critical_errors(f"Critical error pattern detected: {error_message}")

        return should_shutdown

    async def _should_trigger_shutdown(self, severity: str, error_key: str, now) -> bool:
        """
        Determine if shutdown should be triggered based on error patterns.

        Args:
            severity: Severity of the current error
            error_key: Key identifying the error type and location
            now: Current timestamp

        Returns:
            bool: True if shutdown should be triggered
        """
        from datetime import timedelta

        window_start = now - timedelta(minutes=self.error_window_minutes)

        # Count recent errors
        recent_critical = sum(1 for e in self._critical_errors
                            if e['timestamp'] >= window_start and
                            e['severity'] in ['CRITICAL', 'FATAL'])

        recent_recoverable = sum(1 for e in self._critical_errors
                               if e['timestamp'] >= window_start and
                               e['severity'] == 'RECOVERABLE')

        # Immediate shutdown conditions
        if severity == "FATAL":
            self.logger.critical("FATAL ERROR detected - triggering immediate shutdown")
            return True

        # Critical error threshold
        if recent_critical >= self.max_critical_errors:
            self.logger.critical(f"Too many critical errors ({recent_critical}) in {self.error_window_minutes} minutes - triggering shutdown")
            return True

        # Recoverable error threshold
        if recent_recoverable >= self.max_recoverable_errors:
            self.logger.error(f"Too many recoverable errors ({recent_recoverable}) in {self.error_window_minutes} minutes - triggering shutdown")
            return True

        # Specific critical patterns that should trigger immediate shutdown
        critical_immediate_patterns = [
            "Cannot recover session state",
            "generator didn't stop",
            "'Provide' object has no attribute"
        ]

        for pattern in critical_immediate_patterns:
            if pattern in error_key:
                self.logger.critical(f"Critical pattern '{pattern}' detected - triggering shutdown")
                return True

        return False

    async def shutdown_due_to_critical_errors(self, reason: str):
        """
        Trigger shutdown due to critical errors.

        Args:
            reason: Reason for the shutdown
        """
        self.logger.critical(f"🚨 CRITICAL ERROR SHUTDOWN: {reason}")
        self.logger.critical(f"📊 Error Summary: {len(self._critical_errors)} total errors recorded")

        # Log recent critical errors
        recent_errors = self._critical_errors[-5:]
        for i, error in enumerate(recent_errors, 1):
            self.logger.critical(f"  {i}. [{error['severity']}] {error['module']}.{error['function']}: {error['error_message']}")

        # Trigger normal shutdown process
        await self.shutdown(timeout=10.0)

    def get_error_summary(self) -> dict:
        """Get a summary of recorded errors."""
        from datetime import datetime, timedelta

        now = datetime.now()
        window_start = now - timedelta(minutes=self.error_window_minutes)

        recent_errors = [e for e in self._critical_errors if e['timestamp'] >= window_start]

        return {
            "total_errors": len(self._critical_errors),
            "recent_errors": len(recent_errors),
            "critical_errors": len([e for e in recent_errors if e['severity'] == 'CRITICAL']),
            "fatal_errors": len([e for e in recent_errors if e['severity'] == 'FATAL']),
            "recoverable_errors": len([e for e in recent_errors if e['severity'] == 'RECOVERABLE']),
            "shutdown_requested": self._shutdown_requested,
            "error_window_minutes": self.error_window_minutes
        }
