"""
Async utility functions for better cancellation handling and task management.
"""
import asyncio
import logging
from typing import Optional


async def cancellable_sleep(duration: float, shutdown_event: Optional[asyncio.Event] = None) -> bool:
    """
    Cancellable sleep that can be interrupted by either task cancellation or a shutdown event.
    
    Args:
        duration: Sleep duration in seconds
        shutdown_event: Optional event that can interrupt the sleep
        
    Returns:
        bool: True if sleep completed normally, False if interrupted by shutdown_event
        
    Raises:
        asyncio.CancelledError: If the task is cancelled
    """
    if shutdown_event is None:
        # Simple cancellable sleep using wait_for with timeout
        try:
            # Create a never-completing event to wait on
            never_set_event = asyncio.Event()
            await asyncio.wait_for(never_set_event.wait(), timeout=duration)
            return True  # This should never happen since event is never set
        except asyncio.TimeoutError:
            return True  # Normal completion
        except asyncio.CancelledError:
            # Re-raise cancellation to properly handle task cancellation
            raise
    else:
        # Sleep that can be interrupted by shutdown event
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=duration)
            return False  # Interrupted by shutdown event
        except asyncio.TimeoutError:
            return True  # Normal completion
        except asyncio.CancelledError:
            # Re-raise cancellation to properly handle task cancellation
            raise


async def cancellable_wait_for_event(event: asyncio.Event, timeout: Optional[float] = None) -> bool:
    """
    Wait for an event with proper cancellation handling.
    
    Args:
        event: Event to wait for
        timeout: Optional timeout in seconds
        
    Returns:
        bool: True if event was set, False if timeout occurred
        
    Raises:
        asyncio.CancelledError: If the task is cancelled
    """
    try:
        if timeout is None:
            await event.wait()
            return True
        else:
            await asyncio.wait_for(event.wait(), timeout=timeout)
            return True
    except asyncio.TimeoutError:
        return False
    except asyncio.CancelledError:
        # Re-raise cancellation to properly handle task cancellation
        raise


class CancellableTimer:
    """
    A timer that can be cancelled and provides proper cleanup.
    Useful for periodic tasks that need to be cleanly cancelled.
    """
    
    def __init__(self, interval: float, callback, *args, **kwargs):
        """
        Initialize the cancellable timer.
        
        Args:
            interval: Time interval in seconds between callback executions
            callback: Async function to call periodically
            *args, **kwargs: Arguments to pass to the callback
        """
        self.interval = interval
        self.callback = callback
        self.args = args
        self.kwargs = kwargs
        self._task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        self._logger = logging.getLogger(__name__)
    
    async def _timer_loop(self):
        """Internal timer loop."""
        try:
            while not self._shutdown_event.is_set():
                # Use cancellable sleep
                sleep_completed = await cancellable_sleep(self.interval, self._shutdown_event)
                
                if not sleep_completed:
                    # Shutdown event was set
                    break
                
                if not self._shutdown_event.is_set():
                    try:
                        await self.callback(*self.args, **self.kwargs)
                    except Exception as e:
                        self._logger.error(f"Error in timer callback: {e}", exc_info=True)
                        
        except asyncio.CancelledError:
            self._logger.debug("Timer loop cancelled")
            raise
        except Exception as e:
            self._logger.error(f"Unexpected error in timer loop: {e}", exc_info=True)
    
    def start(self):
        """Start the timer."""
        if self._task is None or self._task.done():
            self._shutdown_event.clear()
            self._task = asyncio.create_task(self._timer_loop())
    
    def stop(self):
        """Stop the timer gracefully."""
        self._shutdown_event.set()
    
    async def cancel_and_wait(self):
        """Cancel the timer and wait for it to complete."""
        self._shutdown_event.set()
        
        if self._task and not self._task.done():
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
    
    @property
    def is_running(self) -> bool:
        """Check if the timer is currently running."""
        return self._task is not None and not self._task.done()


async def graceful_task_cancellation(task: asyncio.Task, timeout: float = 5.0) -> bool:
    """
    Gracefully cancel a task with a timeout.
    
    Args:
        task: Task to cancel
        timeout: Maximum time to wait for graceful cancellation
        
    Returns:
        bool: True if task was cancelled gracefully, False if forced
    """
    if task.done():
        return True
    
    # Request cancellation
    task.cancel()
    
    try:
        # Wait for graceful cancellation
        await asyncio.wait_for(task, timeout=timeout)
        return True
    except asyncio.TimeoutError:
        # Task didn't cancel gracefully within timeout
        logging.warning(f"Task {task.get_name()} did not cancel gracefully within {timeout}s")
        return False
    except asyncio.CancelledError:
        # Task was cancelled successfully
        return True


async def safe_gather(*tasks, return_exceptions: bool = True, timeout: Optional[float] = None):
    """
    Safely gather tasks with proper cancellation handling.
    
    Args:
        *tasks: Tasks to gather
        return_exceptions: Whether to return exceptions instead of raising them
        timeout: Optional timeout for the gather operation
        
    Returns:
        List of results or exceptions
    """
    try:
        if timeout is None:
            return await asyncio.gather(*tasks, return_exceptions=return_exceptions)
        else:
            return await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=return_exceptions),
                timeout=timeout
            )
    except asyncio.CancelledError:
        # Cancel all tasks if gather is cancelled
        for task in tasks:
            if isinstance(task, asyncio.Task) and not task.done():
                task.cancel()
        
        # Wait for all tasks to complete cancellation
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        raise
