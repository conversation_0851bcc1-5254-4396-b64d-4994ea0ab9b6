import asyncio
import time
import traceback
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
import inspect
import weakref

@dataclass
class GranularTaskStats:
    name: str
    start_time: float
    state: str
    await_location: Optional[str] = None
    await_target: Optional[str] = None
    stack_info: List[str] = None
    duration: float = 0.0
    await_duration: float = 0.0
    last_await_start: Optional[float] = None
    exception_info: Optional[str] = None
    result: Any = None

class GranularTaskMonitor:
    def __init__(self):
        self.tasks: Dict[str, GranularTaskStats] = {}
        self.monitoring = False
        self.monitor_task = None
        self.previous_states: Dict[str, str] = {}
        
    async def start_monitoring(self, interval=0.1):
        """Start granular monitoring with high frequency"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        
    async def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
    
    def _get_task_state(self, task):
        """Get detailed task state information"""
        if hasattr(task, '_state'):
            # Task states: PENDING, RUNNING, DONE, CANCELLED
            return task._state
        elif task.done():
            if task.cancelled():
                return 'CANCELLED'
            elif task.exception():
                return 'EXCEPTION'
            else:
                return 'DONE'
        else:
            return 'PENDING'
    
    def _get_await_info(self, task):
        """Extract information about what the task is awaiting"""
        await_location = None
        await_target = None
        
        try:
            # Get the stack frames
            stack = task.get_stack()
            
            if stack:
                # Look for the frame where await is happening
                for frame in stack:
                    filename = frame.f_code.co_filename
                    lineno = frame.f_lineno
                    func_name = frame.f_code.co_name
                    
                    # Skip asyncio internals
                    if 'asyncio' in filename:
                        continue
                    
                    # Get the line of code
                    try:
                        with open(filename, 'r') as f:
                            lines = f.readlines()
                            if lineno <= len(lines):
                                line = lines[lineno - 1].strip()
                                if 'await' in line:
                                    await_location = f"{filename}:{lineno} ({func_name})"
                                    await_target = line
                                    break
                    except:
                        pass
        except Exception as e:
            await_location = f"Error getting stack: {e}"
        
        return await_location, await_target
    
    def _get_stack_trace(self, task):
        """Get readable stack trace"""
        try:
            stack = task.get_stack()
            if not stack:
                return []
            
            stack_info = []
            for frame in stack:
                filename = frame.f_code.co_filename
                lineno = frame.f_lineno
                func_name = frame.f_code.co_name
                
                # Skip asyncio internals for cleaner output
                if 'asyncio' not in filename:
                    stack_info.append(f"{filename}:{lineno} in {func_name}")
            
            return stack_info
        except Exception as e:
            return [f"Error getting stack: {e}"]
    
    async def _monitor_loop(self, interval):
        """Main monitoring loop with granular inspection"""
        while self.monitoring:
            try:
                self._update_granular_stats()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
    
    def _update_granular_stats(self):
        """Update detailed statistics for all tasks"""
        current_time = time.time()
        
        # Get all current tasks
        current_tasks = {task.get_name(): task for task in asyncio.all_tasks() 
                        if not task.done() or task.get_name() in self.tasks}
        
        for task_name, task in current_tasks.items():
            current_state = self._get_task_state(task)
            
            if task_name in self.tasks:
                stats = self.tasks[task_name]
                stats.duration = current_time - stats.start_time
                
                # Track state changes
                previous_state = self.previous_states.get(task_name, 'UNKNOWN')
                if previous_state != current_state:
                    # State changed
                    if current_state == 'PENDING' and previous_state != 'PENDING':
                        # Task went into await
                        stats.last_await_start = current_time
                    elif previous_state == 'PENDING' and stats.last_await_start:
                        # Task came out of await
                        stats.await_duration += current_time - stats.last_await_start
                        stats.last_await_start = None
                
                stats.state = current_state
                
                # Get await information if task is pending
                if current_state == 'PENDING':
                    stats.await_location, stats.await_target = self._get_await_info(task)
                    stats.stack_info = self._get_stack_trace(task)
                else:
                    stats.await_location = None
                    stats.await_target = None
                    stats.stack_info = []
                
                # Handle completed tasks
                if task.done():
                    if task.cancelled():
                        stats.state = 'CANCELLED'
                    elif task.exception():
                        stats.state = 'EXCEPTION'
                        stats.exception_info = str(task.exception())
                    else:
                        stats.state = 'DONE'
                        try:
                            stats.result = task.result()
                        except:
                            pass
                
                self.previous_states[task_name] = current_state
                
            else:
                # New task
                self.tasks[task_name] = GranularTaskStats(
                    name=task_name,
                    start_time=current_time,
                    state=current_state,
                    stack_info=self._get_stack_trace(task)
                )
                self.previous_states[task_name] = current_state
    
    def get_stats(self) -> List[GranularTaskStats]:
        """Get current detailed statistics"""
        return list(self.tasks.values())
    
    def print_detailed_stats(self):
        """Print comprehensive task statistics"""
        print("\n" + "="*100)
        print("GRANULAR TASK MONITORING")
        print("="*100)
        
        for stats in self.get_stats():
            print(f"\nTask: {stats.name}")
            print(f"  State: {stats.state}")
            print(f"  Duration: {stats.duration:.3f}s")
            print(f"  Await Time: {stats.await_duration:.3f}s")
            
            if stats.await_location:
                print(f"  Currently Awaiting: {stats.await_location}")
                print(f"  Await Target: {stats.await_target}")
            
            if stats.exception_info:
                print(f"  Exception: {stats.exception_info}")
            
            if stats.result is not None:
                print(f"  Result: {stats.result}")
            
            if stats.stack_info:
                print("  Stack Trace:")
                for frame in stats.stack_info[-3:]:  # Show last 3 frames
                    print(f"    {frame}")
    
    def print_compact_stats(self):
        """Print compact task statistics"""
        print("\n" + "="*120)
        print(f"{'Task':<20} {'State':<12} {'Duration':<10} {'Await':<10} {'Location':<30} {'Awaiting':<30}")
        print("="*120)
        
        for stats in self.get_stats():
            location = stats.await_location.split('/')[-1] if stats.await_location else ""
            awaiting = stats.await_target[:30] if stats.await_target else ""
            
            print(f"{stats.name:<20} {stats.state:<12} {stats.duration:<10.3f} "
                  f"{stats.await_duration:<10.3f} {location:<30} {awaiting:<30}")

# Example tasks for testing
async def network_task(task_id):
    """Simulate network operations"""
    print(f"Network task {task_id} starting")
    
    # Simulate connection establishment
    await asyncio.sleep(0.5)
    
    # Simulate data transfer
    await asyncio.sleep(1.0)
    
    # Simulate response processing
    await asyncio.sleep(0.3)
    
    return f"Network result {task_id}"

async def database_task(task_id):
    """Simulate database operations"""
    print(f"Database task {task_id} starting")
    
    # Simulate connection
    await asyncio.sleep(0.2)
    
    # Simulate query execution
    await asyncio.sleep(0.8)
    
    # Simulate result processing
    await asyncio.sleep(0.1)
    
    return f"DB result {task_id}"

async def file_io_task(task_id):
    """Simulate file I/O operations"""
    print(f"File I/O task {task_id} starting")
    
    # Simulate file operations in thread
    await asyncio.to_thread(time.sleep, 1.5)
    
    return f"File result {task_id}"

async def cpu_bound_task(task_id):
    """Simulate CPU-bound work"""
    print(f"CPU task {task_id} starting")
    
    # Simulate CPU work
    start = time.time()
    while time.time() - start < 0.5:
        pass
    
    await asyncio.sleep(0.1)  # Brief pause
    
    # More CPU work
    start = time.time()
    while time.time() - start < 0.3:
        pass
    
    return f"CPU result {task_id}"

async def main():
    monitor = GranularTaskMonitor()
    
    # Start monitoring with high frequency
    await monitor.start_monitoring(interval=0.05)
    
    # Create a task to print stats periodically
    async def stats_printer():
        while True:
            await asyncio.sleep(1)
            monitor.print_compact_stats()
    
    stats_task = asyncio.create_task(stats_printer())
    
    try:
        # Run various types of tasks
        async with asyncio.TaskGroup() as tg:
            tasks = []
            
            # Different task types
            tasks.append(tg.create_task(network_task(1), name="network_1"))
            tasks.append(tg.create_task(database_task(1), name="database_1"))
            tasks.append(tg.create_task(file_io_task(1), name="file_io_1"))
            tasks.append(tg.create_task(cpu_bound_task(1), name="cpu_bound_1"))
            
            # More network tasks
            for i in range(2, 4):
                tasks.append(tg.create_task(network_task(i), name=f"network_{i}"))
        
        # Final detailed stats
        print("\nFINAL DETAILED STATISTICS:")
        monitor.print_detailed_stats()
        
    finally:
        stats_task.cancel()
        await monitor.stop_monitoring()

if __name__ == "__main__":
    asyncio.run(main())