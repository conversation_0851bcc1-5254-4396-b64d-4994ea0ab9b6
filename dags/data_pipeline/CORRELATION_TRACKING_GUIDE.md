# Correlation ID Tracking Fix Guide

This guide explains how to properly set up correlation ID tracking to avoid getting "N/A" values.

## 🔍 Problem Analysis

The correlation IDs were showing as "N/A" because:

1. **Multiple Injection Points**: Correlation information was being added in multiple places (filters, formatters, handlers)
2. **Timing Issues**: The log record factory wasn't being installed early enough
3. **Context Variable Access**: Context variables weren't being properly accessed during log record creation
4. **Initialization Order**: The correlation factory needed to be installed before any logging occurred

## ✅ Solution Implementation

### 1. Enhanced Log Record Factory

The `logging_utils.py` now contains an improved log record factory:

```python
def install_correlation_log_record_factory():
    """Install a global log record factory that injects correlation metadata."""
    global _factory_installed
    
    with _factory_lock:
        if _factory_installed:
            return
            
        original_factory = logging.getLogRecordFactory()

        def correlation_record_factory(*args, **kwargs):
            record = original_factory(*args, **kwargs)
            
            # Get correlation information from context variables
            correlation_id = correlation_id_var.get()
            task_id = task_id_var.get()
            coroutine_name = coroutine_name_var.get()
            
            # If not in context, try to get current task info
            if not task_id or not coroutine_name:
                try:
                    loop = asyncio.get_running_loop()
                    task = asyncio.current_task(loop=loop)
                    if task:
                        if not task_id:
                            task_id = str(id(task))
                        if not coroutine_name:
                            coroutine_name = task.get_name()
                except RuntimeError:
                    pass
            
            # Set the correlation information on the record
            record.correlation_id = correlation_id or "N/A"
            record.task_id = task_id or "N/A"
            record.coroutine_name = coroutine_name or "N/A"
            
            return record

        logging.setLogRecordFactory(correlation_record_factory)
        _factory_installed = True
```

### 2. Container Integration

The `LoggerContainer` now automatically installs the correlation factory:

```python
class LoggerContainer(containers.DeclarativeContainer):
    # Resource to install correlation factory
    correlation_factory_resource = providers.Resource(
        lambda: __import__('dags.data_pipeline.logging_utils', fromlist=['install_correlation_log_record_factory']).install_correlation_log_record_factory()
    )
    
    @classmethod
    def init_resources(cls):
        """Initialize logging resources including correlation factory."""
        # Initialize the correlation factory first
        try:
            cls.correlation_factory_resource()
        except Exception as e:
            logging.getLogger(__name__).warning(f"Failed to initialize correlation factory: {e}")
        
        # Then initialize the main logging configuration
        try:
            cls.log_resource()
        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to initialize logging configuration: {e}")
            raise
```

### 3. Proper Initialization Order

**Correct initialization sequence:**

```python
# Step 1: Initialize container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Step 2: Initialize logging resources (this installs correlation factory)
container.logger_container.init_resources()

# Step 3: Configure database logging (optional)
db_handlers = initialize_database_logging(container=container, enabled=True)

# Step 4: Use logging with correlation tracking
logger = container.logger_container.logger()

with CorrelationContext("my_operation") as ctx:
    logger.info("This will have proper correlation ID")
```

## 🚀 Usage Examples

### Basic Correlation Tracking

```python
from containers import ApplicationContainer
from custom_logger import CorrelationContext

# Initialize container with proper order
container = ApplicationContainer()
container.wire(modules=[__name__])
container.logger_container.init_resources()  # Critical: installs correlation factory

logger = container.logger_container.logger()

# Now correlation tracking works
with CorrelationContext("user_registration") as ctx:
    logger.info("Starting user registration")  # Will have correlation ID
    logger.info("Validating input")           # Same correlation ID
    logger.info("Creating account")           # Same correlation ID
```

### Async Correlation Tracking

```python
async def process_data(batch_id):
    async with CorrelationContext(f"batch_{batch_id}") as ctx:
        logger.info(f"Processing batch {batch_id}")  # Will have correlation ID + task info
        await some_async_operation()
        logger.info(f"Batch {batch_id} completed")   # Same correlation ID + task info

# Each task gets its own correlation ID
tasks = [process_data(i) for i in range(3)]
await asyncio.gather(*tasks)
```

### Manual Factory Installation (if needed)

```python
from logging_utils import install_correlation_log_record_factory

# Install manually if not using containers
install_correlation_log_record_factory()

# Now all loggers will have correlation tracking
logger = logging.getLogger("my_logger")
with CorrelationContext("manual_test") as ctx:
    logger.info("This will have correlation ID")
```

## 🔧 Troubleshooting

### Issue: Still getting "N/A" values

**Solution:**
1. Ensure `container.logger_container.init_resources()` is called
2. Verify the correlation factory is installed before any logging
3. Check that you're using `CorrelationContext` properly

```python
# Debug: Check if factory is installed
from logging_utils import _factory_installed
print(f"Factory installed: {_factory_installed}")

# Debug: Check context variables
from custom_logger import correlation_id_var
print(f"Current correlation ID: {correlation_id_var.get()}")
```

### Issue: Different correlation IDs in same context

**Solution:**
Make sure you're not creating multiple contexts:

```python
# ❌ Wrong - creates new context each time
def process_item(item):
    with CorrelationContext("process") as ctx:  # New context each call
        logger.info(f"Processing {item}")

# ✅ Correct - single context for related operations
with CorrelationContext("batch_process") as ctx:
    for item in items:
        process_item(item)  # All share same correlation ID
```

### Issue: Async tasks not getting task info

**Solution:**
Ensure you're using `async with` for async contexts:

```python
# ❌ Wrong - sync context in async function
async def async_task():
    with CorrelationContext("async_op") as ctx:  # Won't capture task info
        logger.info("Processing")

# ✅ Correct - async context
async def async_task():
    async with CorrelationContext("async_op") as ctx:  # Captures task info
        logger.info("Processing")
```

## 🧪 Testing

Run the correlation fix test to verify everything works:

```bash
python test_correlation_fix.py
```

Expected output:
```
🚀 Correlation ID Fix Tests
========================================

📋 Running Basic Correlation Factory test...
Record 1 - correlation_id: abc12345
Record 1 - task_id: N/A
Record 1 - coroutine_name: N/A
✅ Correlation tracking with factory test passed

📋 Running Async Correlation Factory test...
Task 1 Record 1 - correlation_id: def67890
Task 1 Record 1 - task_id: 140234567890
Task 1 Record 1 - coroutine_name: async_task_0
✅ Async correlation tracking with factory test passed

📋 Running Container Integration test...
Container Record 1 - correlation_id: ghi12345
✅ Container integration test passed

========================================
🎯 Test Results: 3 passed, 0 failed
🎉 All correlation tracking tests passed!
```

## 📊 Log Format Examples

With proper correlation tracking, your logs will look like:

```
2024-01-15T10:30:45.123 INFO [abc12345] [task_789:process_data] function_name 0042 | Starting data processing
2024-01-15T10:30:45.124 INFO [abc12345] [task_789:process_data] function_name 0043 | Processing batch 1
2024-01-15T10:30:45.125 INFO [abc12345] [task_789:process_data] function_name 0044 | Processing completed
```

Where:
- `abc12345` = Correlation ID (groups related operations)
- `task_789` = Asyncio task ID (for async operations)
- `process_data` = Coroutine name (for async operations)

## 🎯 Key Points

1. **Install Early**: The correlation factory must be installed before any logging occurs
2. **Use Containers**: The `LoggerContainer.init_resources()` method handles proper initialization
3. **Context Managers**: Always use `CorrelationContext` for grouping related operations
4. **Async Support**: Use `async with` for async operations to capture task information
5. **Thread Safe**: The factory installation is thread-safe and can only happen once

Following this guide should resolve all "N/A" correlation ID issues and provide proper tracking across your application.
