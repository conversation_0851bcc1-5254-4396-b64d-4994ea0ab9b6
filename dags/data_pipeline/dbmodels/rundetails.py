import datetime

from .base import Base, TableName
from sqlalchemy import Column, MetaData, Table, String, Integer, DateTime
from sqlalchemy.dialects.postgresql import TEXT, TIMESTAMP


class RunDetailsJira(Base):
    use_snake_case = True
    topic = Column(TEXT, primary_key=True)
    last_run = Column(TIMESTAMP(timezone=True), nullable=False)


metadata_obj = MetaData(schema=None)
PGStatActivity = Table(
    "pg_stat_activity",
    metadata_obj,
    Column('datname', String, primary_key=True),
    Column('usename', String, primary_key=True),
    Column('pid', Integer, primary_key=True),
    Column('state', String),
)