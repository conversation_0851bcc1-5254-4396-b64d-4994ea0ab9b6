# coding=utf-8
"""
Database model for application logs with monthly partitioning support.
"""
from datetime import datetime
from sqlalchemy import String, Text, Integer, DateTime, Index, Column
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped
from typing import Optional, Dict, Any
from dags.data_pipeline.dbmodels.base import Base


class LogEntry(Base):
    """
    Database model for storing application logs with comprehensive tracking information.
    
    This table supports monthly partitioning for efficient log storage and retrieval.
    Partitioning is handled by the create_schema_tables_ddl function in utility_code.py.
    
    Attributes:
        id: Primary key
        timestamp: When the log entry was created
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        logger_name: Name of the logger that created this entry
        module: Python module where the log originated
        function_name: Function name where the log originated
        line_number: Line number where the log originated
        message: The actual log message
        correlation_id: Correlation ID for tracking related operations
        task_id: Asyncio task ID for async operations
        coroutine_name: Name of the coroutine for async operations
        thread_id: Thread ID where the log was generated
        process_id: Process ID where the log was generated
        hostname: Hostname of the machine generating the log
        user_id: User ID if available
        session_id: Session ID if available
        request_id: Request ID for web requests
        extra_data: Additional structured data as JSON
        stack_trace: Stack trace for errors
        performance_data: Performance metrics as JSON
    """
    
    __tablename__ = 'log_entry'
    __table_args__ = (
        # Indexes for efficient querying
        Index('idx_log_entry_timestamp', 'timestamp'),
        Index('idx_log_entry_level', 'level'),
        Index('idx_log_entry_correlation_id', 'correlation_id'),
        Index('idx_log_entry_logger_name', 'logger_name'),
        Index('idx_log_entry_module_function', 'module', 'function_name'),
        Index('idx_log_entry_task_id', 'task_id'),
        # Composite indexes for common queries
        Index('idx_log_entry_timestamp_level', 'timestamp', 'level'),
        Index('idx_log_entry_correlation_timestamp', 'correlation_id', 'timestamp'),
        {'schema': None}  # Will be created in multiple schemas
    )
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Timestamp information
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True,
                       comment="Timestamp when the log entry was created")
    
    # Log level and source information
    level = Column(String(20), nullable=False, index=True,
                   comment="Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
    logger_name = Column(String(100), nullable=False, index=True,
                         comment="Name of the logger that created this entry")
    
    # Source code location
    module = Column(String(200), nullable=True,
                    comment="Python module where the log originated")
    function_name = Column(String(100), nullable=True,
                           comment="Function name where the log originated")
    line_number = Column(Integer, nullable=True,
                         comment="Line number where the log originated")
    
    # Message content
    message = Column(Text, nullable=False,
                     comment="The actual log message")
    
    # Correlation and tracking information
    correlation_id = Column(String(50), nullable=True, index=True,
                            comment="Correlation ID for tracking related operations")
    task_id = Column(String(50), nullable=True, index=True,
                     comment="Asyncio task ID for async operations")
    coroutine_name = Column(String(100), nullable=True,
                            comment="Name of the coroutine for async operations")
    
    # System information
    thread_id = Column(Integer, nullable=True,
                       comment="Thread ID where the log was generated")
    process_id = Column(Integer, nullable=True,
                        comment="Process ID where the log was generated")
    hostname = Column(String(100), nullable=True,
                      comment="Hostname of the machine generating the log")
    
    # User and session information
    user_id = Column(String(100), nullable=True,
                     comment="User ID if available")
    session_id = Column(String(100), nullable=True,
                        comment="Session ID if available")
    request_id = Column(String(100), nullable=True,
                        comment="Request ID for web requests")
    
    # Structured data
    extra_data = Column(JSONB, nullable=True,
                        comment="Additional structured data as JSON")
    stack_trace = Column(Text, nullable=True,
                         comment="Stack trace for errors")
    performance_data = Column(JSONB, nullable=True,
                              comment="Performance metrics as JSON")
    
    def __repr__(self):
        return (f"LogEntry(id={self.id}, timestamp={self.timestamp}, "
                f"level={self.level}, logger_name={self.logger_name}, "
                f"correlation_id={self.correlation_id}, message={self.message[:50]}...)")
    
    @classmethod
    def get_partition_name(cls, timestamp: datetime) -> str:
        """Get the partition table name for a given timestamp."""
        return f"{cls.__tablename__}_{timestamp.strftime('%Y_%m')}"
    
    @classmethod
    def get_partition_ddl(cls, schema: str, year: int, month: int) -> str:
        """Generate DDL for creating a monthly partition."""
        partition_name = f"{cls.__tablename__}_{year:04d}_{month:02d}"
        start_date = f"{year:04d}-{month:02d}-01"
        
        # Calculate next month for end date
        if month == 12:
            next_year, next_month = year + 1, 1
        else:
            next_year, next_month = year, month + 1
        end_date = f"{next_year:04d}-{next_month:02d}-01"
        
        return f"""
        CREATE TABLE IF NOT EXISTS {schema}.{partition_name} 
        PARTITION OF {schema}.{cls.__tablename__}
        FOR VALUES FROM ('{start_date}') TO ('{end_date}');
        
        -- Create indexes on partition
        CREATE INDEX IF NOT EXISTS idx_{partition_name}_timestamp 
        ON {schema}.{partition_name} (timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_{partition_name}_level 
        ON {schema}.{partition_name} (level);
        
        CREATE INDEX IF NOT EXISTS idx_{partition_name}_correlation_id 
        ON {schema}.{partition_name} (correlation_id);
        """
