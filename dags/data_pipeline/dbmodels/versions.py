# coding=utf-8
from sqlalchemy_utils import URLType

from .base import Base, TableName
from sqlalchemy import Column, Integer, String, Boolean, Date, Index


class Versions(Base, TableName):
    # __tablename__ = "versions"
    id = Column(Integer, primary_key=True, autoincrement=False)
    description = Column(String)
    name = Column(String, index=True)
    archived = Column(Boolean)
    released = Column(Boolean)
    startDate = Column(Date)
    userStartDate = Column(Date)
    releaseDate = Column(Date)
    userReleaseDate = Column(Date)
    overdue = Column(Boolean)
    projectId = Column(Integer, index=True)
    issuesUnresolvedCount = Column(Integer)
    issuesCount = Column(Integer)
    issuesFixedCount = Column(Integer)
    issuesAffectedCount = Column(Integer)
    issueCountWithCustomFieldsShowingVersion = Column(Integer)
    category = Column(String)
    issueId = Column(Integer)
    relatedWorkId = Column(String)
    title = Column(String)
    url = Column(URLType)