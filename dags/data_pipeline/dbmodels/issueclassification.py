# coding=utf-8
from enum import Enum, unique

from sqlalchemy import Column, ForeignKey, Index
from sqlalchemy.dialects.postgresql import TEXT, BIGINT, ENUM, INTEGER
from sqlalchemy_utils import LtreeType

from .base import Base, TableName
from .issue import Issue


@unique
class IssueClassificationEnum(Enum):
    initiative = 2
    epic = 1
    standard = 0
    subtask = -1


class IssueClassification(Base, TableName):
    use_snake_case = True
    id = Column(BIGINT, ForeignKey(Issue.id), primary_key=True, autoincrement=False)
    key = Column(TEXT, ForeignKey(Issue.key), index=True, nullable=False, unique=True)
    # issueclass = Column(ENUM(IssueClassificationEnum), unique=False, nullable=False)
    # issue_hierarchy_level = Column(INTEGER, nullable=True)
    path_id = Column(LtreeType, nullable=True)
    path_key = Column(LtreeType, nullable=True)
    initiative_key = Column(TEXT, nullable=True, index=True)
    epic_key = Column(TEXT, nullable=True, index=True)
    standard_key = Column(TEXT, nullable=True, index=True)
    subtask_key = Column(TEXT, nullable=True, index=True)
    initiative_id = Column(BIGINT, nullable=True, index=True)
    epic_id = Column(BIGINT, nullable=True, index=True)
    standard_id = Column(BIGINT, nullable=True, index=True)
    subtask_id = Column(BIGINT, nullable=True, index=True)


    __table_args__ = (
        Index("ix_issue_classification_path_id", 'path_id', postgresql_using='gist'),
        Index("ix_issue_classification_path_key", 'path_key', postgresql_using='gist'),
    )

