# coding=utf-8
from sqlalchemy import (
    Column, Integer, String, DateTime, ForeignKey, Text, ARRAY
)
from sqlalchemy.dialects.postgresql import BIGINT, JSON, TEXT

from .base import Base, TableName
from .allboards import AllBoards


class SprintOld(Base, TableName):
    sprint_id = Column(Integer, primary_key=True, autoincrement=False)
    origin_board_id = Column(Integer, nullable=False, primary_key=True)
    name = Column(String, nullable=True)
    state = Column(String, nullable=True)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    complete_date = Column(DateTime(timezone=True), nullable=True)
    goal = Column(Text, nullable=True)
    estimated_velocity = Column(Integer, nullable=True)
    completed_velocity = Column(Integer, nullable=True)
    all_considered_issue_keys = Column(ARRAY(String), nullable=True)
    estimated_entries = Column(ARRAY(String), nullable=True)
    completed_entries = Column(ARRAY(String), nullable=True)
    created_date = Column(DateTime(timezone=True))
    __table_args__ = (
        {
            'extend_existing': True,
            'schema': None
        }
    )

class Sprint(Base):
    use_snake_case = True

    id = Column(BIGINT, primary_key=True, autoincrement=False)
    # If board moves, it retains the originBoardId breaking the integrity constraint.
    originBoardId = Column(BIGINT, nullable=False)
    name = Column(String, nullable=True)
    state = Column(String, nullable=True)
    startDate = Column(DateTime(timezone=True))
    endDate = Column(DateTime(timezone=True))
    completeDate = Column(DateTime(timezone=True))
    createdDate = Column(DateTime(timezone=True))
    goal = Column(Text, nullable=True)
    estimated = Column(JSON)
    completed = Column(JSON)
    allConsideredIssueKeys = Column(ARRAY(TEXT))
    estimatedEntries = Column(JSON)
    completedEntries = Column(JSON)
    changes = Column(JSON)
    startTime = Column(BIGINT)
    endTime = Column(BIGINT)
    completeTime = Column(BIGINT)
    now = Column(BIGINT)
    issueToParentKeys = Column(JSON)
    issueToSummary = Column(JSON)
    workRateData = Column(JSON)
    openCloseChanges = Column(JSON)
    lastUserWhoClosedHtml = Column(TEXT)
    warningMessage = Column(TEXT)


