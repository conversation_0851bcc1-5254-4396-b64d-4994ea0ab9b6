# coding=utf-8
import re

from citext import CIText
from sqlalchemy import Integer, String
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import object_mapper, has_inherited_table, declarative_base
from sqlalchemy.sql.schema import MetaData, Table, Column


class TableNameCamelCase(object):
    """
    This class is passed to declarative_base to auto generate __tablename__ for each model class
    """

    @declared_attr
    def __tablename__(cls):
        if has_inherited_table(cls):
            return None
        split_camel_case = "_".join(re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split())
        # print(f"Table Name in base.py {split_camel_case.lower()}")
        return split_camel_case.lower()


class TableName(object):

    @declared_attr
    def __tablename__(cls):
        if has_inherited_table(cls):
            return None

        split_camel_case = "_".join(re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split())
        # print(f"Table Name in base.py {split_camel_case.lower()}")
        return cls.__name__.lower()


class RepresentableBase(object):
    """
    This class can be used by ``declarative_base``, to add an automatic
    ``__repr__`` method to *all* subclasses of ``Base``. This ``__repr__`` will
    represent values as::
        ClassName(pkey_1=value_1, pkey_2=value_2, ..., pkey_n=value_n)
    where ``pkey_1..pkey_n`` are the primary key columns of the mapped table
    with the corresponding values.
    """

    # Since the tables defined here are only 'skeleton' tables used as
    # the base for our schema-specific tables, we set abstract to True.
    __abstract__ = True
    use_snake_case = False

    @declared_attr
    def __tablename__(cls) -> object:
        if '__tablename__' in cls.__dict__:

            return cls.__dict__['__tablename__']

        # Convert CamelCase to snake_case
        if cls.use_snake_case:
            split_camel_case = "_".join(
                re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split()
            ).lower()
            return split_camel_case
        else:

            return cls.__name__.lower()

    @classmethod
    def __declare_last__(cls):
        """
        This method is called after all class-level declarations have been processed.
        You can perform additional actions here related to table definitions.
        """
        # Example: Print out all table names defined with this base
        for schema in ['plat']:
            table_name = cls.__name__.lower()  # Example: issue -> issue
            table = Table(
                table_name,
                MetaData(),
                Column('id', Integer, primary_key=True),
                Column('name', String),
                schema=schema,
            )
            setattr(cls, f"{table_name}_{schema}", table)


    def __repr__(self):
        mapper = object_mapper(self)
        items = [(p.key, getattr(self, p.key))
                 for p in [
                     mapper.get_property_by_column(c) for c in mapper.primary_key]]
        return "{0}({1})".format(
            self.__class__.__name__,
            ', '.join(['{0}={1!r}'.format(*_) for _ in items]))


convention = {
    "ix": 'ix_%(column_0_label)s',
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata_obj = MetaData(
    naming_convention=convention, schema=None,

)
Base = declarative_base(cls=RepresentableBase, metadata=metadata_obj)


class CacheyCIText(CIText):
    # Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    # Can remove when that issue is fixed
    cache_ok = True
