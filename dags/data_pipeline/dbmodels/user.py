from sqlalchemy.orm import Mapped
from sqlalchemy_utils import TimezoneType, ChoiceType, EmailType, LocaleType

from .base import Base
from sqlalchemy import Column, Boolean, INTEGER
from sqlalchemy.dialects.postgresql import TEXT


class User(Base):
    ACCOUNT_TYPES = [
        ('atlassian', 'Atlassian'),
        ('app', 'App'),
        ('customer', 'Customer'),
    ]

    accountId: Mapped[str] = Column(
        TEXT, primary_key=True, autoincrement=False
    )
    accountType: Mapped[ChoiceType] = Column(
        ChoiceType(ACCOUNT_TYPES), nullable=False,
    )

    emailAddress = Column(
         EmailType, unique=True, nullable=True
    )
    displayName = Column(
        TEXT, nullable=False
    )
    active: Mapped[Boolean] = Column(
        Boolean, nullable=False
    )
    timeZone: Mapped[TimezoneType] = Column(
        TimezoneType(backend='zoneinfo'), nullable=True
    )
    locale: Mapped[LocaleType] = Column(
        LocaleType, nullable=True
    )

    __table_args__ = (
        # CheckConstraint("now() AT TIME ZONE timeZone IS NOT NULL", name='user_timezone'),
        {'schema': 'public'}
    )

    def __repr__(self):
        return str([getattr(self, c.name, None) for c in self.__table__.c])


class Role(Base):
    id = Column(
        INTEGER, primary_key=True, autoincrement=True
    )
    name: Mapped[str] = Column(TEXT, unique=True, nullable=False)
    description = Column(TEXT, nullable=True)
    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f'<Role {self.name}>'


class Permission(Base):
    id: Mapped[int] = Column(
        INTEGER, primary_key=True, autoincrement=True
    )
    name: Mapped[str] = Column(TEXT, unique=True, nullable=False)
    description = Column(TEXT, nullable=True)
    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f'<Permission {self.name}>'
