import datetime

from sqlalchemy import Column, BIGIN<PERSON>, ForeignKey, Date, Boolean, Index, Sequence, UniqueConstraint, TEXT, DateTime, \
    Integer, NUMERIC, text
from sqlalchemy.sql import expression
from sqlalchemy.sql.functions import func
from sqlalchemy_utils import ChoiceType, EmailType

from .base import Base, TableName, TableNameCamelCase
from .user import User
from sqlalchemy.schema import FetchedValue
from sqlalchemy.dialects.postgresql import ExcludeConstraint, TEXT

TABLE_ID_SEQ = Sequence('table_id_seq')


# id = Column(BIGINT, TABLE_ID_SEQ, primary_key=True, server_default=func.public.next_id())

class Teams(Base):
    id = Column(BIGINT, TABLE_ID_SEQ, primary_key=True, server_default=FetchedValue())
    team_name = Column(TEXT, nullable=False)
    emailAddress = Column(
        EmailType,
        doc="Email address of the user. Using CIText ensures case in-sensitive comparison",
        nullable=False
    )
    accountId = Column(
        TEXT, ForeignKey(User.accountId), nullable=False
    )
    startDate = Column(
        Date, default=datetime.date(1970, 1, 1), server_default='1970-01-01',
        nullable=False
    )
    endDate = Column(
        Date, default=datetime.date(2050, 12, 31), server_default='2050-12-31',
        nullable=False
    )
    active = Column(Boolean, server_default=expression.true(), default=True)
    designation = Column(TEXT)
    __table_args__ = (
        ExcludeConstraint(
            (team_name, "="),
            (accountId, "="),
            (func.daterange(startDate, endDate, bounds="[]"), "&&"),
            name="uq_teams_emailAddress_teamName_daterange",
            using="gist",
        ),
    )


# TABLE_TRAINING_ID_SEQ = Sequence('training_data_id_seq', schema='public')


class NLPTrainingData(TableNameCamelCase, Base):
    # id = Column(BIGINT, primary_key=True, server_default=TABLE_TRAINING_ID_SEQ.next_value())
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    summary = Column(TEXT)
    label = Column(TEXT)
    rank = Column(TEXT)

    __table_args__ = (
        {'schema': 'public'}
    )


class RequestTracker(Base, TableNameCamelCase):
    RECURRING_TASK_CHOICES = [
        ("Yes", "Yes"),
        ("No", "No"),
        ("Blank", "")
    ]

    id = Column(BIGINT, primary_key=True, autoincrement=False)
    desc = Column(TEXT, nullable=False)
    reported_by = Column(TEXT, nullable=False)
    reported_on = Column(DateTime, nullable=False)
    last_updated_by = Column(TEXT)
    last_updated_on = Column(DateTime)
    project = Column(TEXT, nullable=False)
    organization = Column(TEXT, nullable=False)
    category = Column(TEXT)
    priority = Column(TEXT)
    assigned_to = Column(TEXT)
    status = Column(TEXT)
    task = Column(TEXT)
    reference_no = Column(TEXT)
    help_type = Column(TEXT)
    help_from = Column(TEXT)
    recurring_task = Column(ChoiceType(RECURRING_TASK_CHOICES))
    add_rt_to_irr = Column(TEXT)
    irr_id = Column(TEXT)
    functionality = Column(TEXT)
    estimated_time_hours = Column(Integer)
    complexity = Column(TEXT)
    label = Column(TEXT)
    confidence_percent = Column(NUMERIC)
    label_max_percent = Column(TEXT)
    max_confidence_percent = Column(NUMERIC)

    __table_args__ = (
        {'schema': 'public'}
    )

    def __repr__(self):
        return f"<Task(id={self.id}, desc='{self.desc}', ...)>"
