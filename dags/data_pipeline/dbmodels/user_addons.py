from sqlalchemy_utils import EmailType
from sqlalchemy import Column, INTEGER, ForeignKey
from sqlalchemy.orm import Mapped
from sqlalchemy.dialects.postgresql import TEXT, DATE
from .base import Base, CacheyCIText
from .user import Role, User, Permission


class UserRole(Base):
    use_snake_case = True
    emailAddress = Column(
        CacheyCIText(),
        ForeignKey(User.emailAddress),
        primary_key=True
    )
    role_id = Column(
        INTEGER, ForeignKey(Role.id), primary_key=True, nullable=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )


class RolePermissions(Base):
    use_snake_case = True
    role_id: Mapped[int] = Column(
        INTEGER, ForeignKey(Role.id), primary_key=True, autoincrement=False
    )
    permission_id = Column(
        INTEGER, ForeignKey(Permission.id), primary_key=True, autoincrement=False
    )
    __table_args__ = (
        {'schema': 'public'}
    )

class UserToken(Base):
    use_snake_case = True
    emailAddress = Column(
        EmailType, ForeignKey(User.emailAddress), primary_key=True, nullable=True,
    )
    otp_secret: Mapped[TEXT] = Column(
        TEXT, nullable=True
    )
    otp_secret_added_on: Mapped[DATE] = Column(
        DATE, nullable=True
    )
