from .base import Base, TableName
from sqlalchemy import Column, BIGIN<PERSON>, ForeignKey, String, DateTime, Index
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.dialects.postgresql import HSTORE, TEXT
from sqlalchemy.sql.functions import func


class InitiativeAttribute(Base, TableName):
    initiative_id = Column(BIGINT, ForeignKey('issue.id'), primary_key=True, autoincrement=False)
    initiative_key = Column(String, nullable=False, index=True)
    project = Column(TEXT, nullable=True, index=True)
    release = Column(TEXT, nullable=True, index=True)
    feature = Column(TEXT, nullable=True, index=True)
    created = Column(DateTime(timezone=True), nullable=False)
    updated = Column(DateTime(timezone=True), nullable=False)
    # created_at = Column(DateTime(timezone=True), server_default=func.now())
    # updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        {'extend_existing': True, 'schema': None},
    )
