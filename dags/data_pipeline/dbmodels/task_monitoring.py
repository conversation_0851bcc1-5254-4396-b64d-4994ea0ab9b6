from .base import Base

from sqlalchemy import (
    Column, String, Float, Integer, Text,
    Index, ForeignKey, BigInteger, SmallInteger, UniqueConstraint, PrimaryKeyConstraint, ForeignKeyConstraint, Identity
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB, TIMESTAMP
from sqlalchemy.sql import func
import uuid
# from dags.data_pipeline.dbmodels.base import Base


class TaskExecution(Base):
    """Main table for task execution tracking"""
    use_snake_case = True
    # Primary key and identification
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    task_name = Column(String(255), nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # Groups related tasks
    process_id = Column(String(50), nullable=False, index=True)  # OS process ID
    
    # Timing information
    start_time = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now(), primary_key=True)
    end_time = Column(TIMESTAMP(timezone=True), nullable=True)
    duration = Column(Float, nullable=True)  # Total duration in seconds
    
    # Status tracking
    initial_state = Column(String(20), nullable=False, default='PENDING')
    final_state = Column(String(20), nullable=True, index=True)
    
    # Resource usage
    peak_memory_mb = Column(Float, nullable=True)
    avg_cpu_percent = Column(Float, nullable=True)
    
    # Result and error information
    result_type = Column(String(100), nullable=True)
    result_summary = Column(Text, nullable=True)  # Truncated result
    exception_type = Column(String(200), nullable=True)
    exception_message = Column(Text, nullable=True)
    
    # Metadata
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now())
    
    # Relationships
    state_changes = relationship("TaskStateChange", back_populates="task_execution", cascade="all, delete-orphan")
    await_events = relationship("TaskAwaitEvent", back_populates="task_execution", cascade="all, delete-orphan")
    stack_snapshots = relationship("TaskStackSnapshot", back_populates="task_execution", cascade="all, delete-orphan")
    
    # Partitioning - partition by date (start_time)
    __table_args__ = (
        UniqueConstraint('id', 'start_time', name='uq_task_execution_id_start_time'),
        # Indexes for common queries

        # Index('idx_task_executions_final_state', 'final_state'),
        # Index('idx_task_executions_process_id', 'process_id'),

        # Composite indexes for common query patterns
        Index('idx_task_executions_name_start', 'task_name', 'start_time'),
        Index('idx_task_executions_session_start', 'session_id', 'start_time'),
        Index('idx_task_executions_state_time', 'final_state', 'start_time'),

        # Partial indexes for active tasks
        Index('idx_task_executions_active', 'task_name', 'start_time',
              postgresql_where=Column('end_time').is_(None)),
        {
            'postgresql_partition_by': 'RANGE (start_time)',
            'schema': 'public'
        },
    )

    def __repr__(self):
        return str([getattr(self, c.name, None) for c in self.__table__.c])

class TaskStateChange(Base):
    """Track state transitions of tasks"""
    use_snake_case = True
    
    id = Column(BigInteger, Identity(always=True), primary_key=True)
    task_execution_id = Column(UUID(as_uuid=True), nullable=False)
    task_start_time = Column(TIMESTAMP(timezone=True), nullable=False)
    
    # State information
    from_state = Column(String(20), nullable=True)
    to_state = Column(String(20), nullable=False)
    timestamp = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now(), primary_key=True)
    
    # Duration in previous state
    state_duration = Column(Float, nullable=True)  # Time spent in from_state
    
    # System metrics at state change
    cpu_percent = Column(Float, nullable=True)
    memory_mb = Column(Float, nullable=True)
    thread_count = Column(SmallInteger, nullable=True)
    
    # Additional context
    context_info = Column(JSONB, nullable=True)  # Any additional state info
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="state_changes")
    
    __table_args__ = (
        # Indexes for time-series queries
        Index('idx_state_changes_task_timestamp', 'task_execution_id', 'timestamp'),
        Index('idx_state_changes_timestamp', 'timestamp'),
        Index('idx_state_changes_to_state', 'to_state'),
        Index('idx_state_changes_from_to', 'from_state', 'to_state'),
        UniqueConstraint("id", "timestamp", name="uq_task_state_changes_id_timestamp"),
        ForeignKeyConstraint(
            ('task_execution_id', 'task_start_time'), [TaskExecution.id, TaskExecution.start_time], name='fk_task_state_changes_task_execution_id'),
        
        # Partition by timestamp (inherits from parent table partitioning)
        {
            'postgresql_partition_by': 'RANGE (timestamp)',
            'schema': 'public'
        },
    )

class TaskAwaitEvent(Base):
    """Track await events and what tasks are waiting for"""
    use_snake_case = True
    
    id = Column(BigInteger, Identity(always=True), primary_key=True)
    task_execution_id = Column(UUID(as_uuid=True), nullable=False)
    task_start_time = Column(TIMESTAMP(timezone=True), nullable=False)
    
    # Await timing
    await_start = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now(), primary_key=True)
    await_end = Column(TIMESTAMP(timezone=True), nullable=True)
    await_duration = Column(Float, nullable=True)
    
    # Await location information
    await_location = Column(String(500), nullable=True)  # filename:line
    await_function = Column(String(200), nullable=True)  # function name
    await_target = Column(Text, nullable=True)  # what's being awaited
    
    # Await category for analytics
    await_category = Column(String(50), nullable=True)  # 'network', 'database', 'file_io', etc.
    
    # Performance impact
    blocked_tasks_count = Column(SmallInteger, nullable=True)  # How many tasks were blocked
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="await_events")
    
    __table_args__ = (
        ForeignKeyConstraint(
            ('task_execution_id', 'task_start_time'), [TaskExecution.id, TaskExecution.start_time], name='fk_task_await_events_task_execution_id'
        ),
        Index('idx_await_events_task_start', 'task_execution_id', 'await_start'),
        Index('idx_await_events_start_time', 'await_start'),
        Index('idx_await_events_duration', 'await_duration'),
        Index('idx_await_events_category', 'await_category'),
        Index('idx_await_events_location', 'await_location'),
        
        # Partial index for active awaits
        Index('idx_await_events_active', 'task_execution_id', 'await_start',
              postgresql_where=Column('await_end').is_(None)),
        
        # Partition by await_start
        {
            'postgresql_partition_by': 'RANGE (await_start)',
            'schema': 'public'
        },
    )

class TaskStackSnapshot(Base):
    """Store periodic stack snapshots for debugging"""
    use_snake_case = True
    
    id = Column(BigInteger, Identity(always=True), primary_key=True)
    task_execution_id = Column(UUID(as_uuid=True), nullable=False)
    task_start_time = Column(TIMESTAMP(timezone=True), nullable=False)
    
    timestamp = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now(), primary_key=True)
    
    # Stack information
    stack_frames = Column(JSONB, nullable=False)  # Array of frame info
    stack_depth = Column(SmallInteger, nullable=False)
    
    # Current execution context
    current_file = Column(String(500), nullable=True)
    current_line = Column(Integer, nullable=True)
    current_function = Column(String(200), nullable=True)
    
    # State at snapshot time
    task_state = Column(String(20), nullable=False)
    
    # Relationships
    task_execution = relationship("TaskExecution", back_populates="stack_snapshots")
    
    __table_args__ = (
        ForeignKeyConstraint(
            ('task_execution_id', 'task_start_time'), [TaskExecution.id, TaskExecution.start_time],
            name='fk_task_stack_snapshots_task_execution_id'
        ),
        Index('idx_stack_snapshots_task_time', 'task_execution_id', 'timestamp'),
        Index('idx_stack_snapshots_timestamp', 'timestamp'),
        Index('idx_stack_snapshots_state', 'task_state'),
        Index('idx_stack_snapshots_function', 'current_function'),
        
        # Partition by timestamp
        {
            'postgresql_partition_by': 'RANGE (timestamp)',
            'schema': 'public'
        },
    )

class TaskMetrics(Base):
    """Aggregated metrics for analytics"""
    use_snake_case = True
    
    id = Column(BigInteger, Identity(always=True), primary_key=True)
    
    # Aggregation parameters
    task_name = Column(String(255), nullable=False)
    time_bucket = Column(TIMESTAMP(timezone=True), nullable=False, primary_key=True)  # 1-minute buckets
    session_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Aggregated statistics
    execution_count = Column(Integer, nullable=False, default=0)
    avg_duration = Column(Float, nullable=True)
    max_duration = Column(Float, nullable=True)
    min_duration = Column(Float, nullable=True)
    
    avg_await_duration = Column(Float, nullable=True)
    max_await_duration = Column(Float, nullable=True)
    await_event_count = Column(Integer, nullable=False, default=0)
    
    # Success/failure rates
    success_count = Column(Integer, nullable=False, default=0)
    failure_count = Column(Integer, nullable=False, default=0)
    cancelled_count = Column(Integer, nullable=False, default=0)
    
    # Resource usage
    avg_memory_mb = Column(Float, nullable=True)
    peak_memory_mb = Column(Float, nullable=True)
    avg_cpu_percent = Column(Float, nullable=True)
    
    # Popular await categories
    top_await_categories = Column(JSONB, nullable=True)  # {"network": 45, "database": 30, ...}
    
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, default=func.now())
    
    __table_args__ = (
        Index('idx_task_metrics_name_bucket', 'task_name', 'time_bucket'),
        Index('idx_task_metrics_bucket', 'time_bucket'),
        Index('idx_task_metrics_session_bucket', 'session_id', 'time_bucket'),
        
        # Unique constraint for aggregation
        Index('idx_task_metrics_unique', 'task_name', 'time_bucket', 'session_id'),
        
        # Partition by time_bucket
        {
            'postgresql_partition_by': 'RANGE (time_bucket)',
            'schema': 'public'
        },
    )
