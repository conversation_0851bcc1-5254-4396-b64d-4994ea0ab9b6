from .base import Base, TableName
from sqlalchemy import Column, Integer, String, Boolean


class AllBoards(Base, TableName):
    use_snake_case = True
    id = Column(Integer, primary_key=True, autoincrement=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    projectId = Column(Integer, index=True)
    displayName = Column(String, nullable=True)
    projectName = Column(String, nullable=True)
    projectKey = Column(String, nullable=True, index=True)
    projectTypeKey = Column(String)
    userId = Column(Integer)
    userAccountId = Column(String)
    isPrivate = Column(Boolean)
    __table_args__ = (
        {'schema': 'public'}
    )