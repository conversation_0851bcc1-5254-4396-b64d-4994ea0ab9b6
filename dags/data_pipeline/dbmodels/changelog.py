from sqlalchemy.ext.indexable import index_property

from .base import Base, TableName
from .user import User
from .issue import Issue
from sqlalchemy import Column, BIGINT, String, ForeignKey, DateTime, Sequence, func, Index, UniqueConstraint
from sqlalchemy.dialects.postgresql import ARRAY, TEXT, JSONB, JSON, TIMESTAMP
from sqlalchemy.schema import FetchedValue


# Source: https://docs.sqlalchemy.org/en/20/orm/extensions/indexable.html
class pg_json_property(index_property):
    def __init__(self, attr_name, index, cast_type):
        super(pg_json_property, self).__init__(attr_name, index)
        self.cast_type = cast_type

    def expr(self, model):
        expr = super(pg_json_property, self).expr(model)
        return expr.astext.cast(self.cast_type)


class ChangeLog(Base):
    use_snake_case = False

    id = Column(BIGINT, nullable=False, primary_key=True)
    author = Column(String, ForeignKey(User.accountId), nullable=True)
    created = Column(TIMESTAMP(timezone=True), nullable=False)
    field = Column(String, nullable=False, primary_key=True)
    fieldtype = Column(String, nullable=False)
    fieldId = Column(String, nullable=True)
    from_ = Column(String, nullable=False, primary_key=True)
    fromString = Column(String, nullable=True)
    to = Column(String, nullable=False, primary_key=True)
    toString = Column(String, nullable=True)
    tmpFromAccountId = Column(TEXT, nullable=True)
    tmpToAccountId = Column(TEXT, nullable=True)
    issue_key = Column(String, ForeignKey(Issue.key), nullable=False)
    issue_id = Column(BIGINT, ForeignKey(Issue.id), nullable=False)


class ChangelogJSON(Base):
    use_snake_case = True
    id = Column(BIGINT, primary_key=True, autoincrement=False)
    author = Column(String, ForeignKey(User.accountId), nullable=True)
    created = Column(DateTime(timezone=True), nullable=False)
    items = Column(JSON)
    issue_key = Column(
        String,
        ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )
    issue_id = Column(
        BIGINT,
        ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )
    # Index properties for JSONB fields
    # field = index_property(attr_name='items', index='field', default=None)
    # fieldtype = index_property('items', 'fieldtype', default=None)
    # fieldId = index_property('items', 'fieldId', default=None)
    # from_value = pg_json_property('items', 'from', String)
    # fromString = index_property('items', 'fromString', default=None)
    # to = pg_json_property('items', 'to', String)
    # toString = index_property('items', 'toString', default=None)
