from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, JSON

from .base import Base, TableName
from .user import User
import numpy as np
from sqlalchemy import Column, String, DateTime, Integer, ForeignKey, Computed, Numeric
from sqlalchemy.ext.hybrid import hybrid_property

class DeletedWorklog(Base, TableName):
    use_snake_case = True
    worklogId = Column(Integer, primary_key=True, autoincrement=False)
    updatedTime = Column(BIGINT, nullable=False)
    __table_args__ = (
        {
            'schema': 'public'
        }
    )

class WorkLog(Base, TableName):
    # Got a bad example where "started": "0001-01-01T00:00:00.000+0530",
    # therefore, made started as nullable for worklogid 1812131 and key = PLAT-236217
    id = Column(Integer, primary_key=True, autoincrement=False)
    author = Column(String, ForeignKey(User.accountId), nullable=False, index=True)
    updateauthor = Column(String, ForeignKey(User.accountId), nullable=False)
    created = Column(DateTime(timezone=True), nullable=False, index=True)
    updated = Column(DateTime(timezone=True), nullable=False)
    started = Column(DateTime(timezone=True), nullable=True, index=True)    
    timeSpent = Column(String, nullable=False)
    timeSpentSeconds = Column(Integer, nullable=False)
    timeSpentHours = Column(Numeric, Computed(timeSpentSeconds / 3600, persisted=True))
    comment = Column(JSON)
    issue_id = Column(
        Integer,
        ForeignKey("issue.id", deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )
    issue_key = Column(
        String,
        ForeignKey("issue.key", deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )

    __table_args__ = (
        {
            'extend_existing': True,
            'schema': None
        }
    )

    @hybrid_property
    def business_days(self):
        # Ensure the start and end values are valid datetime objects
        if not self.started or not self.updated:
            return None
        try:
            # Convert to numpy.datetime64 to use busday_count
            start = np.datetime64(self.started)  # Convert datetime to numpy datetime64
            end = np.datetime64(self.updated)    # Convert datetime to numpy datetime64
            return np.busday_count(start, end)
        except (ValueError, TypeError) as e:
            # Handle invalid datetime or conversion errors
            return None


