# Database Guidelines

## Core Principles

### Multi-Schema PostgreSQL Setup
- Use separate schemas for different environments (plat, plp, public)
- Implement proper schema translation with Alembic
- Use `get_model_by_name()` function for dynamic model retrieval
- Handle schema-specific migrations properly

### Transaction Management
- Maintain referential integrity with proper transaction handling
- Implement retry mechanisms for lock contention issues
- Use dependency injection for database session management
- Commit parent tables before child tables to maintain integrity

### Performance and Scalability
- Use SQLAlchemy + psycopg3 for both sync/async operations
- Implement partition tables for time-series data (monthly partitions)
- Use connection pooling for better performance
- Implement proper indexing strategies

## Code Examples

### Dynamic Model Retrieval
```python
from dags.data_pipeline.database.get_model_by_name import get_model_by_name

def get_database_config(table_name: str, schema: str = None) -> dict:
    """Get database configuration using dynamic model retrieval."""
    try:
        model_class = get_model_by_name(table_name, schema)
        return {
            'model': model_class,
            'table_name': model_class.__table__.name,
            'schema': getattr(model_class.__table__, 'schema', None),
            'columns': [col.name for col in model_class.__table__.columns]
        }
    except ValueError as e:
        logger.error(f"Model not found: {e}")
        raise
```

### Multi-Schema Session Management
```python
from sqlalchemy.ext.asyncio import AsyncSession
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_schema_session(schema_name: str, app_container):
    """Get database session with proper schema context."""
    try:
        app_container.schema.override(schema_name)
        async with app_container.database_rw().update_schema(schema_name).async_session() as session:
            # Set schema translation map
            session.bind.update_execution_options(
                schema_translate_map={None: schema_name}
            )
            yield session
    finally:
        # Reset schema override
        app_container.schema.reset_override()
```

### Partition Table Creation
```python
from datetime import datetime, timezone, timedelta
from sqlalchemy import DDL, text

def get_partition_ddl_statements():
    """Generate DDL statements for partition table creation."""
    today = datetime.now(timezone.utc).date()
    ddls = []

    # Monthly partitions for task_executions, task_metrics
    for i in range(3):
        start = (today.replace(day=1) + timedelta(days=32 * i)).replace(day=1)
        end = (start.replace(day=28) + timedelta(days=4)).replace(day=1)
        suffix = f"{start.year}_{start.month:02d}"

        for table in ["task_execution", "task_metrics"]:
            ddl = DDL(f"""
                CREATE TABLE IF NOT EXISTS public.{table}_{suffix}
                PARTITION OF public.{table}
                FOR VALUES FROM ('{start.isoformat()}T00:00:00+00:00') 
                TO ('{end.isoformat()}T00:00:00+00:00');
            """)
            ddls.append((table, ddl))

    # Daily partitions for log_entry
    for i in range(7):
        date = today + timedelta(days=i)
        suffix = date.strftime("%Y_%m_%d")
        
        ddl = DDL(f"""
            CREATE TABLE IF NOT EXISTS public.log_entry_{suffix}
            PARTITION OF public.log_entry
            FOR VALUES FROM ('{date.isoformat()}T00:00:00+00:00') 
            TO ('{(date + timedelta(days=1)).isoformat()}T00:00:00+00:00');
        """)
        ddls.append(("log_entry", ddl))

    return ddls

def attach_partition_ddls():
    """Create partition tables using DDL statements."""
    table_dict = {
        'task_execution': 'TaskExecution',
        'task_state_change': 'TaskStateChange',
        'task_await_event': 'TaskAwaitEvent',
        'task_stack_snapshot': 'TaskStackSnapshot',
        'task_metrics': 'TaskMetrics',
        'log_entry': 'LogEntry'
    }
    
    for table_name, ddl in get_partition_ddl_statements():
        if table_name in table_dict:
            model_class = get_model_by_name(table_dict[table_name])
            # Execute DDL for the model's table
            # Implementation would execute the DDL statement
            logger.info(f"Created partition for {table_name}")
```

### Transaction Retry Pattern
```python
import asyncio
from sqlalchemy.exc import OperationalError
from typing import Callable, Any

async def retry_transaction(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    *args,
    **kwargs
) -> Any:
    """Retry database transaction with exponential backoff."""
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except OperationalError as e:
            if "deadlock detected" in str(e).lower() and attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                logger.warning(f"Deadlock detected, retrying in {delay}s (attempt {attempt + 1})")
                await asyncio.sleep(delay)
                continue
            raise
        except Exception as e:
            logger.error(f"Transaction failed: {e}")
            raise
    
    raise Exception(f"Transaction failed after {max_retries} attempts")
```

### Database Monitoring
```python
import asyncio
import psutil
from sqlalchemy import text
from typing import Dict, Any

class DatabaseMonitor:
    def __init__(self, db_session):
        self.db_session = db_session
        self.monitoring = False
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start database monitoring."""
        self.monitoring = True
        while self.monitoring:
            try:
                metrics = await self.collect_db_metrics()
                await self.log_metrics(metrics)
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Database monitoring error: {e}")
                await asyncio.sleep(interval)
    
    async def collect_db_metrics(self) -> Dict[str, Any]:
        """Collect database metrics."""
        try:
            # Connection count
            conn_query = text("""
                SELECT count(*) as active_connections 
                FROM pg_stat_activity 
                WHERE state = 'active'
            """)
            
            # Lock information
            lock_query = text("""
                SELECT mode, count(*) as lock_count
                FROM pg_locks 
                GROUP BY mode
            """)
            
            # Database size
            size_query = text("""
                SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
            """)
            
            async with self.db_session() as session:
                conn_result = await session.execute(conn_query)
                lock_result = await session.execute(lock_query)
                size_result = await session.execute(size_query)
                
                return {
                    'active_connections': conn_result.scalar(),
                    'locks': dict(lock_result.fetchall()),
                    'database_size': size_result.scalar(),
                    'timestamp': datetime.now(timezone.utc)
                }
        except Exception as e:
            logger.error(f"Error collecting DB metrics: {e}")
            return {}
    
    async def log_metrics(self, metrics: Dict[str, Any]):
        """Log database metrics."""
        if metrics:
            logger.info(f"DB Metrics: {metrics}")
```

## Schema Management

### Alembic Multi-Schema Configuration
```python
# alembic/env.py configuration for multi-schema support
from alembic import context
from sqlalchemy import engine_from_config, pool
from dags.data_pipeline.dbmodels.base import Base

def get_x_argument(key: str, default=None):
    """Get argument from alembic command line."""
    return context.get_x_argument(as_dictionary=True).get(key, default)

def run_migrations_online():
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        context.config.get_section(context.config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    # Get target schema from command line
    target_schema = get_x_argument('schema', 'public')
    
    with connectable.connect() as connection:
        # Set schema translation map
        connection.execute(f"SET search_path TO {target_schema}")
        
        context.configure(
            connection=connection,
            target_metadata=Base.metadata,
            version_table_schema=target_schema,
            include_schemas=True,
            compare_type=True,
            compare_server_default=True
        )

        with context.begin_transaction():
            context.run_migrations()
```

### Model Definition Best Practices
```python
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

Base = declarative_base()

class BaseModel(Base):
    """Base model with common fields."""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                       onupdate=lambda: datetime.now(timezone.utc))

class Issue(BaseModel):
    """Issue model with proper schema configuration."""
    __tablename__ = 'issue'
    # Schema can be set dynamically or left as None for multi-schema support
    __table_args__ = {'schema': None}
    
    key = Column(String(50), unique=True, nullable=False, index=True)
    summary = Column(String(500))
    parent_key = Column(String(50), ForeignKey('issue.key'))
    
    # Self-referential relationship
    parent = relationship("Issue", remote_side=[key], backref="children")
    
    @classmethod
    def get_partition_ddl(cls, schema_name: str, year: int, month: int) -> str:
        """Generate partition DDL for time-based partitioning."""
        return f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.{cls.__tablename__}_{year}_{month:02d}
            PARTITION OF {schema_name}.{cls.__tablename__}
            FOR VALUES FROM ('{year}-{month:02d}-01') TO ('{year}-{month+1:02d}-01');
        """
```

## Connection Management

### Async Connection Pool
```python
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

class DatabaseManager:
    def __init__(self, database_url: str):
        self.engine = create_async_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
        
        self.async_session = sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    
    async def get_session(self) -> AsyncSession:
        """Get async database session."""
        return self.async_session()
    
    async def close(self):
        """Close database connections."""
        await self.engine.dispose()
```

## Error Handling

### Database Exception Handling
```python
from sqlalchemy.exc import (
    IntegrityError, 
    OperationalError, 
    SQLAlchemyError,
    DisconnectionError
)

async def handle_database_operation(operation_func, *args, **kwargs):
    """Handle database operations with proper error handling."""
    try:
        return await operation_func(*args, **kwargs)
    except IntegrityError as e:
        logger.error(f"Integrity constraint violation: {e}")
        # Handle constraint violations (duplicate keys, foreign key violations)
        raise
    except OperationalError as e:
        if "deadlock detected" in str(e).lower():
            logger.warning("Deadlock detected, operation should be retried")
            raise
        elif "connection" in str(e).lower():
            logger.error(f"Database connection error: {e}")
            raise
        else:
            logger.error(f"Database operational error: {e}")
            raise
    except DisconnectionError as e:
        logger.error(f"Database disconnection: {e}")
        # Handle connection loss
        raise
    except SQLAlchemyError as e:
        logger.error(f"SQLAlchemy error: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        raise
```
