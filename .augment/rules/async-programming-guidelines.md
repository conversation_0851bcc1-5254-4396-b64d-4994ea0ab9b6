# Async Programming Guidelines

## Core Principles

### Task Management
- Always use `asyncio.current_task().set_name()` for better debugging
- Use `asyncio.all_tasks()` for real-time task monitoring
- Implement proper task cancellation with `asyncio.Event()`
- Handle `asyncio.CancelledError` gracefully in all async functions

### Error Handling
- Use specific exception types for different error conditions
- Implement retry mechanisms with exponential backoff
- Handle deadlocks with automatic detection and retry
- Implement graceful shutdown on max retry failure

### Monitoring and Debugging
- Track task states, await locations, and stack traces
- Use correlation IDs for tracking related operations
- Implement granular task monitoring for debugging
- Monitor system metrics (CPU, memory, asyncio tasks)

### Best Practices
- Use priority queues for task processing with proper ordering
- Implement circuit breakers for external API calls
- Use proper cancellation handling with `asyncio.Event()`
- Handle deadlocks with automatic detection and retry mechanisms

## Code Examples

### Task Naming and Monitoring
```python
import asyncio
from typing import Optional

async def process_data(data_id: str) -> Optional[dict]:
    """Process data with proper task naming and monitoring."""
    task_name = f"process_data_{data_id}"
    asyncio.current_task().set_name(task_name)
    
    # Register for monitoring
    global_async_process_tracker.register_process(task_name, "task", data_id)
    
    try:
        # Processing logic here
        result = await actual_processing(data_id)
        return result
    except asyncio.CancelledError:
        logger.info(f"Task {task_name} was cancelled")
        raise
    except Exception as e:
        logger.error(f"Task {task_name} failed: {e}")
        raise
```

### Proper Cancellation Handling
```python
import asyncio
from dags.data_pipeline.utils.async_utils import cancellable_sleep

async def long_running_task():
    """Example of proper cancellation handling."""
    try:
        while True:
            # Use cancellable sleep instead of asyncio.sleep
            await cancellable_sleep(1)
            
            # Check for cancellation
            if asyncio.current_task().cancelled():
                break
                
            # Do work here
            await process_batch()
            
    except asyncio.CancelledError:
        logger.info("Long running task cancelled gracefully")
        # Cleanup code here
        raise
```

### Circuit Breaker Pattern
```python
import asyncio
from typing import Callable, Any

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: float = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        if self.state == "OPEN":
            if time.time() - self.last_failure_time < self.timeout:
                raise Exception("Circuit breaker is OPEN")
            else:
                self.state = "HALF_OPEN"
        
        try:
            result = await func(*args, **kwargs)
            self.reset()
            return result
        except Exception as e:
            self.record_failure()
            raise
    
    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
    
    def reset(self):
        self.failure_count = 0
        self.state = "CLOSED"
```

### Priority Queue Implementation
```python
import asyncio
import heapq
from typing import Any, Tuple
from dataclasses import dataclass, field

@dataclass
class PriorityMessage:
    priority: int
    message_type: str
    task_id: str
    counter: int
    data: Any = field(compare=False)
    
    def __lt__(self, other):
        # Lower priority number = higher priority
        return (self.priority, self.task_id, self.counter) < (other.priority, other.task_id, other.counter)

class PriorityQueueManager:
    def __init__(self):
        self.counter = 0
        
    async def put_priority_message(self, queue: asyncio.Queue, message_type: str, 
                                 task_id: str, data: Any, priority: int = 5):
        """Put message with priority (lower number = higher priority)."""
        self.counter += 1
        priority_msg = PriorityMessage(
            priority=priority,
            message_type=message_type,
            task_id=task_id,
            counter=self.counter,
            data=data
        )
        await queue.put(priority_msg)
    
    async def get_priority_message(self, queue: asyncio.Queue) -> dict:
        """Get highest priority message from queue."""
        priority_msg = await queue.get()
        return {
            'message_type': priority_msg.message_type,
            'task_id': priority_msg.task_id,
            'data': priority_msg.data,
            'priority': priority_msg.priority
        }
```

## Monitoring Integration

### Real-time Task Monitoring
```python
import asyncio
import time
from typing import Dict, List

class AsyncTaskMonitor:
    def __init__(self):
        self.task_states: Dict[str, dict] = {}
        self.monitoring = False
        
    async def start_monitoring(self, interval: float = 0.1):
        """Start real-time task monitoring."""
        self.monitoring = True
        while self.monitoring:
            try:
                await self._update_task_states()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
    
    async def _update_task_states(self):
        """Update task states using asyncio.all_tasks()."""
        current_tasks = asyncio.all_tasks()
        
        for task in current_tasks:
            task_name = task.get_name()
            
            # Get current state
            if task.done():
                state = "completed"
            elif task.cancelled():
                state = "cancelled"
            else:
                state = self._get_detailed_state(task)
            
            # Update tracking
            if task_name not in self.task_states:
                self.task_states[task_name] = {
                    'start_time': time.time(),
                    'state': state,
                    'await_location': None,
                    'stack_trace': []
                }
            else:
                self.task_states[task_name]['state'] = state
                
            # Get await location and stack trace
            if not task.done():
                await_location, stack_trace = self._get_await_details(task)
                self.task_states[task_name]['await_location'] = await_location
                self.task_states[task_name]['stack_trace'] = stack_trace
    
    def _get_detailed_state(self, task) -> str:
        """Get detailed task state."""
        try:
            if task.done():
                return "completed"
            elif task.cancelled():
                return "cancelled"
            else:
                # Check if task is waiting
                stack = task.get_stack()
                if stack:
                    frame = stack[-1]
                    if 'await' in frame.f_code.co_name:
                        return "awaiting"
                return "running"
        except Exception:
            return "unknown"
    
    def _get_await_details(self, task) -> Tuple[str, List[str]]:
        """Get await location and stack trace."""
        try:
            stack = task.get_stack()
            if not stack:
                return "no_stack", []
            
            # Find await location
            await_location = "unknown"
            stack_trace = []
            
            for frame in stack:
                filename = frame.f_code.co_filename
                lineno = frame.f_lineno
                func_name = frame.f_code.co_name
                
                # Skip asyncio internals
                if 'asyncio' not in filename:
                    stack_trace.append(f"{filename}:{lineno} in {func_name}")
                    
                    # Look for await statements
                    if 'await' in func_name or frame.f_lineno:
                        await_location = f"{filename}:{lineno} in {func_name}"
            
            return await_location, stack_trace
            
        except Exception as e:
            return f"error: {e}", []
```

## Error Recovery Patterns

### Deadlock Detection and Recovery
```python
import asyncio
import time
from typing import List

async def detect_deadlocks() -> List[int]:
    """Detect deadlocked transactions."""
    # Implementation would query database for deadlocks
    # This is a placeholder
    return []

async def kill_transaction(pid: int):
    """Kill a deadlocked transaction."""
    # Implementation would terminate the transaction
    pass

async def monitor_deadlocks(interval: int = 5):
    """Monitor and resolve deadlocks periodically."""
    while True:
        try:
            deadlocked_pids = await detect_deadlocks()
            if deadlocked_pids:
                logger.info(f"Detected deadlocks: {deadlocked_pids}")
                for pid in deadlocked_pids:
                    await kill_transaction(pid)
            
            await asyncio.sleep(interval)
        except asyncio.CancelledError:
            logger.info("Deadlock monitoring cancelled")
            break
        except Exception as e:
            logger.error(f"Error in deadlock monitoring: {e}")
            await asyncio.sleep(interval)
```

### Graceful Shutdown Pattern
```python
import asyncio
import signal
from typing import Set

class GracefulShutdown:
    def __init__(self):
        self.shutdown_event = asyncio.Event()
        self.background_tasks: Set[asyncio.Task] = set()
        
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        for sig in (signal.SIGTERM, signal.SIGINT):
            signal.signal(sig, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        self.shutdown_event.set()
    
    async def wait_for_shutdown(self):
        """Wait for shutdown signal."""
        await self.shutdown_event.wait()
    
    async def cleanup_tasks(self):
        """Cancel and cleanup background tasks."""
        logger.info("Cleaning up background tasks")
        
        # Cancel all background tasks
        for task in self.background_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete cancellation
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        logger.info("All background tasks cleaned up")
```
