# Agent Instructions for Python Airflow Project

## Project Overview

This project is a Python Airflow application developed with Python 3.10+. We use `uv` for virtual environment management and `pyproject.toml` for dependency management and project configuration. Testing is performed using `pytest` with `allure-pytest` for detailed test reporting. The development environment is Windows.

## Key Technologies and Tools

* **Python Version:** 3.10+
* **Virtual Environment:** Managed by `uv` (located in `.venv/`)
* **Dependency Management:** `pyproject.toml` (using uv)
* **Testing Framework:** `pytest`
* **Test Reporting:** `allure-pytest`
* **Operating System:** Windows
* **Code Formatting:** `black`, `isort`
* **Linting:** `flake8`, `mypy`
* **Database:** PostgreSQL with SQLAlchemy 1.4 and psycopg2-binary
* **Async Support:** aiohttp, asyncpg
* **Container Management:** dependency-injector

## Project Structure

```
airflow/
├── dags/
│   ├── __init__.py
│   ├── jira_dag.py
│   └── data_pipeline/
│       ├── __init__.py
│       ├── containers.py
│       ├── custom_logger.py
│       ├── database/
│       │   ├── __init__.py
│       │   ├── get_model_by_name.py
│       │   └── upsert_operations.py
│       ├── dbmodels/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   ├── issue.py
│       │   ├── user.py
│       │   └── ... (other models)
│       ├── jira/
│       │   ├── __init__.py
│       │   └── api_client.py
│       ├── utils/
│       │   ├── __init__.py
│       │   └── async_utils.py
│       └── ... (other modules)
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── unit/
│   ├── integration/
│   ├── functional/
│   ├── performance/
│   ├── smoke/
│   ├── regression/
│   ├── contract/
│   ├── container/
│   ├── smart_retry/
│   ├── features/
│   ├── steps/
│   └── data_pipeline/
├── alembic/
│   ├── env.py
│   ├── script.py.mako
│   └── versions/
├── allure_results/
├── allure-report/
├── pyproject.toml
├── requirements.txt
├── requirements_dev.txt
├── uv.lock
└── .venv/
```

## Virtual Environment Handling

When performing operations that require Python or project dependencies, always ensure the `.venv` is activated.

* **Activation on Windows (CMD):** `.\.venv\Scripts\activate.bat`
* **Activation on Windows (PowerShell):** `.\.venv\Scripts\Activate.ps1`
* **Verification:** Use `which python` or `where python` to confirm virtual environment is active
* **Note:** If `uv` is used for creating the `.venv`, it typically handles pathing well, but explicit activation might be needed for certain tools or scripts.

## Common Development Commands

### Environment Setup
```bash
# Create virtual environment
uv venv

# Activate virtual environment (Windows PowerShell)
.\.venv\Scripts\Activate.ps1

# Install project in development mode
uv pip install -e .

# Install all dependencies from pyproject.toml


# Install development dependencies

```

### Testing
```bash
# Run all tests
pytest

# Run tests with verbose output
pytest -v --tb=short

# Run specific test categories
pytest tests/unit
pytest tests/integration
pytest tests/functional
pytest tests/performance

# Generate Allure results
pytest --alluredir=allure_results --clean-alluredir

# Run tests with coverage
pytest --cov=dags --cov-report=html

# Run async tests
pytest --asyncio-mode=auto

# Run specific test markers
pytest -m "unit"
pytest -m "integration"
pytest -m "not slow"
```

### Code Quality
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8

# Type checking
mypy dags/

# Run all quality checks
black . && isort . && flake8 && mypy dags/
```

### Allure Reporting
```bash
# Generate and serve Allure report
allure serve allure_results

# Generate static HTML report
allure generate allure_results -o allure-report --clean
```

## AI Assistant Guidelines

### Code Generation Preferences
- Always include type hints for function parameters and return values
- Generate comprehensive docstrings following Google style
- Prefer explicit imports over wildcard imports
- Use dataclasses or Pydantic models for structured data
- Include proper error handling with specific exception types
- Add logging statements for debugging and monitoring
- Use `pathlib.Path` for file system operations (Windows compatibility)
- Use async/await patterns for I/O operations
- Follow SQLAlchemy 1.4.50+ patterns for database operations

### Error Handling Standards
```python
import logging
from typing import Optional
from pathlib import Path
import aiohttp
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

async def process_jira_data(data_path: Path) -> Optional[dict]:
    """Process JIRA data and return parsed information.

    Args:
        data_path: Path to the JIRA data file to process

    Returns:
        Parsed data dictionary or None if processing fails

    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If file format is invalid
        SQLAlchemyError: If database operation fails
    """
    try:
        if not data_path.exists():
            raise FileNotFoundError(f"File not found: {data_path}")

        # Processing logic here
        logger.info(f"Successfully processed {data_path}")
        return {"status": "success"}

    except Exception as e:
        logger.error(f"Failed to process {data_path}: {e}")
        raise
```

### Testing Standards
- Write unit tests for all new functions and classes
- Use descriptive test names that explain what is being tested
- Include edge cases and error conditions
- Use pytest fixtures for common test data
- Add appropriate Allure decorators for better reporting
- Use async test patterns for async functions
- Mock external dependencies (JIRA API, database)

### Test Example Template
```python
import pytest
import allure
from pathlib import Path
from unittest.mock import AsyncMock, patch
from dags.data_pipeline.jira.api_client import JiraApiClient

@allure.epic("JIRA Data Processing")
@allure.feature("API Client")
class TestJiraApiClient:

    @allure.story("Valid API calls")
    @allure.title("Should successfully fetch JIRA issues")
    @pytest.mark.asyncio
    async def test_fetch_jira_issues(self, mock_jira_response):
        """Test fetching JIRA issues from API."""
        # Given
        client = JiraApiClient("https://example.atlassian.net")

        # When
        with allure.step("Fetch JIRA issues"):
            result = await client.fetch_issues("PROJECT-123")

        # Then
        with allure.step("Verify API response"):
            assert result is not None
            assert len(result) > 0

    @allure.story("Error handling")
    @allure.title("Should handle API errors gracefully")
    @pytest.mark.asyncio
    async def test_handle_api_error(self):
        """Test error handling for API failures."""
        # Given
        client = JiraApiClient("https://example.atlassian.net")

        # When/Then
        with pytest.raises(Exception):
            await client.fetch_issues("INVALID-PROJECT")
```

## Dependency Management

### Production Dependencies
Core application requirements that are needed for the application to run.

**Key Dependencies:**
- `pandas` - Data manipulation
- `sqlalchemy` - Database ORM
- `psycopg2-binary` - PostgreSQL adapter
- `aiohttp` - Async HTTP client
- `asyncpg` - Async PostgreSQL adapter
- `dependency-injector` - Dependency injection
- `pydantic` - Data validation
- `rich` - Terminal output formatting
- `alembic` - Database migrations

```bash
# Add production dependency
uv pip install package-name

# Add specific version
uv pip install package-name==1.2.3
```

### Development Dependencies
Testing, linting, formatting tools, and other development utilities.

**Required Development Dependencies:**
- `pytest` - Testing framework
- `pytest-asyncio` - Async test support
- `pytest-bdd` - BDD testing
- `allure-pytest` - Test reporting
- `black` - Code formatting
- `isort` - Import sorting
- `flake8` - Linting
- `mypy` - Type checking
- `pytest-cov` - Test coverage

```bash
# Install development dependencies
uv pip install pytest pytest-asyncio pytest-bdd allure-pytest black isort flake8 mypy pytest-cov
```

## Monitoring and Debugging Guidelines

### Task Monitoring Best Practices
- Use granular task monitoring for debugging async operations
- Implement database monitoring with high-frequency logging
- Track task states, await locations, and stack traces
- Monitor system metrics (CPU, memory, asyncio tasks)
- Use correlation IDs for tracking related operations
- Implement circuit breakers for external API calls

### Async Programming Guidelines
- Always use `asyncio.all_tasks()` for real-time task monitoring
- Implement proper cancellation handling with `asyncio.Event()`
- Use task names for better debugging: `asyncio.current_task().set_name()`
- Handle deadlocks with automatic detection and retry mechanisms
- Implement graceful shutdown on max retry failure
- Use priority queues for task processing with proper ordering

### Database Best Practices
- Use multi-schema PostgreSQL setup with proper migrations
- Implement partition tables for time-series data (monthly partitions)
- Use SQLAlchemy + psycopg3 for both sync/async operations
- Maintain referential integrity with proper transaction handling
- Implement retry mechanisms for lock contention issues
- Use dependency injection for database session management

### Error Handling and Logging
- Implement comprehensive error handling with specific exception types
- Use structured logging with correlation IDs
- Create monthly partitioned log tables for scalability
- Implement cross-platform signal handling (Windows/Linux)
- Use LoggerContainer pattern for consistent logging setup
- Monitor and alert on error thresholds and patterns

## Testing with Pytest and Allure

### Running Tests
```bash
# Basic test execution
pytest

# Generate Allure results
pytest --alluredir=allure_results

# Run with coverage
pytest --cov=dags --cov-report=html --cov-report=term-missing

# Run specific test markers
pytest -m "not slow"
pytest -m "integration"
pytest -m "async"

# Run async tests
pytest --asyncio-mode=auto

# Run BDD tests
pytest tests/features/
```

### Test Organization
- Place unit tests in `tests/unit/`
- Place integration tests in `tests/integration/`
- Place database tests in `tests/data_pipeline/`
- Use descriptive test file names: `test_<module_name>.py`
- Group related tests in classes with `Test` prefix

### Allure Report Generation
**Pre-requisite:** Ensure Allure Commandline is installed and accessible in your PATH.

**Installation on Windows:**
```bash
# Using Scoop (recommended)
scoop install allure

# Or download from https://github.com/allure-framework/allure2/releases
```

**Generate Reports:**
```bash
# Serve interactive report
allure serve allure_results

# Generate static HTML report
allure generate allure_results -o allure-report --clean
```

## Common Issues and Solutions

### Virtual Environment Issues
- **Problem:** Commands fail or use wrong Python version
- **Solution:** Ensure `.venv` is activated: `.\.venv\Scripts\Activate.ps1`
- **Verification:** Use `where python` to confirm virtual environment is active

### PowerShell Execution Policy
- **Problem:** Cannot run PowerShell activation script
- **Solution:** Set execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Path Issues
- **Problem:** Cross-platform path compatibility
- **Solution:** Always use `pathlib.Path` instead of string concatenation
- **Example:** `Path("data") / "file.csv"` instead of `"data\\file.csv"`

### Dependency Issues
- **Problem:** Package not found or import errors
- **Solution:** Check installation with `uv pip list` and reinstall if needed
- **Prevention:** Always update pyproject.toml when adding dependencies

### Test Discovery Issues
- **Problem:** Pytest not finding tests
- **Solution:** Ensure test files follow naming convention (`test_*.py` or `*_test.py`)
- **Configuration:** Check `testpaths` in pyproject.toml

### Async/Await Issues
- **Problem:** Async functions not working correctly
- **Solution:** Use `pytest --asyncio-mode=auto` for async tests
- **Verification:** Ensure all async functions are properly awaited

### Database Issues
- **Problem:** Database connection or migration errors
- **Solution:** Check Alembic migrations: `alembic upgrade head`
- **Verification:** Ensure PostgreSQL is running and accessible

## IDE Configuration

### VS Code / Cursor Settings
Create `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": ".venv/Scripts/python.exe",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.testing.unittestEnabled": false,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### Recommended Extensions
- Python (Microsoft)
- Pylance (Microsoft)
- Python Test Explorer
- Allure Test Results
- SQLAlchemy (for database operations)

## Performance Considerations

### For Large Projects
- Use `pytest-xdist` for parallel test execution
- Implement proper logging levels for production
- Consider using `pytest-benchmark` for performance testing
- Use `pytest-mock` for mocking external dependencies
- Use async patterns for I/O operations

### Memory Management
- Close file handles explicitly or use context managers
- Avoid loading large datasets entirely into memory
- Use generators for processing large data sets
- Profile memory usage with `memory_profiler`
- Use async streaming for large data processing

## Security Best Practices

### Code Security
- Never commit sensitive information (API keys, passwords)
- Use environment variables for configuration
- Validate all external inputs
- Use parameterized queries for database operations
- Keep dependencies updated
- Use secure connection strings for databases

### Testing Security
- Mock external API calls in tests
- Don't use real credentials in test data
- Test error handling paths
- Validate input sanitization
- Use test databases for integration tests

## Airflow-Specific Guidelines

### DAG Structure
- Keep DAGs in the `dags/` directory
- Use descriptive DAG IDs and task names
- Implement proper error handling and retries
- Use XCom for task communication
- Follow Airflow best practices for task dependencies

### Database Operations
- Use SQLAlchemy 1.4.50+ patterns
- Implement proper connection pooling
- Use async operations where possible
- Handle database migrations with Alembic
- Implement proper error handling for database operations

### JIRA Integration
- Use async HTTP clients for API calls
- Implement proper rate limiting
- Handle API pagination correctly
- Cache responses when appropriate
- Implement proper error handling for API failures

- Don't use real credentials in test data
- Test error handling paths
- Validate input sanitization

## Monitoring and Debugging Guidelines

### Task Monitoring Best Practices
- Use granular task monitoring for debugging async operations
- Implement database monitoring with high-frequency logging
- Track task states, await locations, and stack traces
- Monitor system metrics (CPU, memory, asyncio tasks)
- Use correlation IDs for tracking related operations
- Implement circuit breakers for external API calls

### Async Programming Guidelines
- Always use `asyncio.all_tasks()` for real-time task monitoring
- Implement proper cancellation handling with `asyncio.Event()`
- Use task names for better debugging: `asyncio.current_task().set_name()`
- Handle deadlocks with automatic detection and retry mechanisms
- Implement graceful shutdown on max retry failure
- Use priority queues for task processing with proper ordering

### Database Best Practices
- Use multi-schema PostgreSQL setup with proper migrations
- Implement partition tables for time-series data (monthly partitions)
- Use SQLAlchemy + psycopg3 for both sync/async operations
- Maintain referential integrity with proper transaction handling
- Implement retry mechanisms for lock contention issues
- Use dependency injection for database session management

### Error Handling and Logging
- Implement comprehensive error handling with specific exception types
- Use structured logging with correlation IDs
- Create monthly partitioned log tables for scalability
- Implement cross-platform signal handling (Windows/Linux)
- Use LoggerContainer pattern for consistent logging setup
- Monitor and alert on error thresholds and patterns

### Monitoring and Partition Issues
- **Problem:** attach_partition_ddls not creating required partitions
- **Solution:** Ensure proper DDL execution order and schema context
- **Verification:** Check partition table creation in database

### Task Monitoring Issues
- **Problem:** Producer tasks not showing in monitoring
- **Solution:** Use proper task naming and registration with global trackers
- **Verification:** Check asyncio.all_tasks() output and task names

## Virtual Environment Handling

When performing operations that require Python or project dependencies, always ensure the `.venv` is activated.

* **Activation on Windows (CMD):** `.\.venv\Scripts\activate.bat`
* **Activation on Windows (PowerShell):** `.\.venv\Scripts\Activate.ps1`
* **Verification:** Use `which python` or `where python` to confirm virtual environment is active
* **Note:** If `uv` is used for creating the `.venv`, it typically handles pathing well, but explicit activation might be needed for certain tools or scripts.

## Common Development Commands

### Environment Setup
```bash
# Create virtual environment
uv venv

# Activate virtual environment (Windows PowerShell)
.\.venv\Scripts\Activate.ps1

# Install project in development mode
uv pip install -e .

# Install all dependencies from pyproject.toml


# Install development dependencies

```

### Testing
```bash
# Run all tests
pytest

# Run tests with verbose output
pytest -v --tb=short

# Run specific test categories
pytest tests/unit
pytest tests/integration
pytest tests/functional
pytest tests/performance

# Generate Allure results
pytest --alluredir=allure_results --clean-alluredir

# Run tests with coverage
pytest --cov=dags --cov-report=html

# Run async tests
pytest --asyncio-mode=auto

# Run specific test markers
pytest -m "unit"
pytest -m "integration"
pytest -m "not slow"
```

### Code Quality
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8

# Type checking
mypy dags/

# Run all quality checks
black . && isort . && flake8 && mypy dags/
```

### Allure Reporting
```bash
# Generate and serve Allure report
allure serve allure_results

# Generate static HTML report
allure generate allure_results -o allure-report --clean
```

## AI Assistant Guidelines

### Code Generation Preferences
- Always include type hints for function parameters and return values
- Generate comprehensive docstrings following Google style
- Prefer explicit imports over wildcard imports
- Use dataclasses or Pydantic models for structured data
- Include proper error handling with specific exception types
- Add logging statements for debugging and monitoring
- Use `pathlib.Path` for file system operations (Windows compatibility)
- Use async/await patterns for I/O operations
- Follow SQLAlchemy 1.4.50+ patterns for database operations

### Error Handling Standards
```python
import logging
from typing import Optional
from pathlib import Path
import aiohttp
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

async def process_jira_data(data_path: Path) -> Optional[dict]:
    """Process JIRA data and return parsed information.
    
    Args:
        data_path: Path to the JIRA data file to process
        
    Returns:
        Parsed data dictionary or None if processing fails
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If file format is invalid
        SQLAlchemyError: If database operation fails
    """
    try:
        if not data_path.exists():
            raise FileNotFoundError(f"File not found: {data_path}")
        
        # Processing logic here
        logger.info(f"Successfully processed {data_path}")
        return {"status": "success"}
        
    except Exception as e:
        logger.error(f"Failed to process {data_path}: {e}")
        raise
```

### Testing Standards
- Write unit tests for all new functions and classes
- Use descriptive test names that explain what is being tested
- Include edge cases and error conditions
- Use pytest fixtures for common test data
- Add appropriate Allure decorators for better reporting
- Use async test patterns for async functions
- Mock external dependencies (JIRA API, database)

### Test Example Template
```python
import pytest
import allure
from pathlib import Path
from unittest.mock import AsyncMock, patch
from dags.data_pipeline.jira.api_client import JiraApiClient

@allure.epic("JIRA Data Processing")
@allure.feature("API Client")
class TestJiraApiClient:
    
    @allure.story("Valid API calls")
    @allure.title("Should successfully fetch JIRA issues")
    @pytest.mark.asyncio
    async def test_fetch_jira_issues(self, mock_jira_response):
        """Test fetching JIRA issues from API."""
        # Given
        client = JiraApiClient("https://example.atlassian.net")
        
        # When
        with allure.step("Fetch JIRA issues"):
            result = await client.fetch_issues("PROJECT-123")
        
        # Then
        with allure.step("Verify API response"):
            assert result is not None
            assert len(result) > 0
    
    @allure.story("Error handling")
    @allure.title("Should handle API errors gracefully")
    @pytest.mark.asyncio
    async def test_handle_api_error(self):
        """Test error handling for API failures."""
        # Given
        client = JiraApiClient("https://example.atlassian.net")
        
        # When/Then
        with pytest.raises(Exception):
            await client.fetch_issues("INVALID-PROJECT")
```

## Dependency Management

### Production Dependencies
Core application requirements that are needed for the application to run.

**Key Dependencies:**
- `pandas` - Data manipulation
- `sqlalchemy` - Database ORM
- `psycopg2-binary` - PostgreSQL adapter
- `aiohttp` - Async HTTP client
- `asyncpg` - Async PostgreSQL adapter
- `dependency-injector` - Dependency injection
- `pydantic` - Data validation
- `rich` - Terminal output formatting
- `alembic` - Database migrations

```bash
# Add production dependency
uv pip install package-name

# Add specific version
uv pip install package-name==1.2.3
```

### Development Dependencies
Testing, linting, formatting tools, and other development utilities.

**Required Development Dependencies:**
- `pytest` - Testing framework
- `pytest-asyncio` - Async test support
- `pytest-bdd` - BDD testing
- `allure-pytest` - Test reporting
- `black` - Code formatting
- `isort` - Import sorting
- `flake8` - Linting
- `mypy` - Type checking
- `pytest-cov` - Test coverage

```bash
# Install development dependencies
uv pip install pytest pytest-asyncio pytest-bdd allure-pytest black isort flake8 mypy pytest-cov
```

## `pyproject.toml` Configuration

### Essential Configuration Sections
```toml
[project]
name = "airflow"
dynamic = ["version"]
description = "JIRA data pipeline for extracting and storing data from Jira Cloud to PostgreSQL"
requires-python = ">=3.10"
dependencies = [
    "alembic",
    "aiohttp",
    "asyncpg",
    "dependency-injector",
    "pandas",
    "psycopg2-binary",
    "pydantic",
    "rich",
    "sqlalchemy",
    # ... other dependencies
]

[project.optional-dependencies]
dev = [
    "black",
    "isort", 
    "mypy",
    "types-SQLAlchemy",
]
test = [
    "pytest",
    "pytest-asyncio",
    "pytest-bdd",
    "allure-pytest",
    "pytest-cov",
    "pytest-mock",
    "responses",
    "aioresponses",
    # ... other test dependencies
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
addopts = [
    "--strict-markers",
    "--alluredir=allure_results",
    "--clean-alluredir",
    "--asyncio-mode=auto"
]
markers = [
    "unit: fast isolated tests",
    "integration: component interaction tests", 
    "functional: end-to-end feature tests",
    "performance: performance and load tests",
    "slow: tests that take more than 1 second",
    "smoke: basic functionality tests",
    "regression: tests for previously fixed bugs",
    "async: Asynchronous tests",
    "bdd: Marker for BDD-style tests",
]

[tool.black]
line-length = 100
target-version = ["py310", "py311", "py312"]

[tool.isort]
profile = "black"
line_length = 100
known_first_party = ["dags"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
```

## Testing with Pytest and Allure

### Running Tests
```bash
# Basic test execution
pytest

# Generate Allure results
pytest --alluredir=allure_results

# Run with coverage
pytest --cov=dags --cov-report=html --cov-report=term-missing

# Run specific test markers
pytest -m "not slow"
pytest -m "integration"
pytest -m "async"

# Run async tests
pytest --asyncio-mode=auto

# Run BDD tests
pytest tests/features/
```

### Allure Report Generation
**Pre-requisite:** Ensure Allure Commandline is installed and accessible in your PATH.

**Installation on Windows:**
```bash
# Using Scoop (recommended)
scoop install allure

# Or download from https://github.com/allure-framework/allure2/releases
```

**Generate Reports:**
```bash
# Serve interactive report
allure serve allure_results

# Generate static HTML report
allure generate allure_results -o allure-report --clean
```

### Allure Annotations
Encourage the use of Allure decorators in test files for better reporting:

```python
import allure
import pytest

@allure.epic("JIRA Integration")
@allure.feature("Data Pipeline")
@allure.story("Issue Processing")
@allure.title("Should successfully process JIRA issues")
@allure.description("Test verifies that JIRA issues are processed and stored correctly")
@pytest.mark.asyncio
async def test_process_jira_issues():
    with allure.step("Fetch issues from JIRA"):
        # Test implementation
        pass
    
    with allure.step("Process and transform data"):
        # Test implementation
        pass
    
    with allure.step("Store in database"):
        # Test implementation
        pass

# Attach files to reports
@allure.step("Process JIRA data file")
async def process_jira_data_file(file_path: Path):
    result = await process_file(file_path)
    allure.attach.file(file_path, name="JIRA Data File", attachment_type=allure.attachment_type.TEXT)
    return result
```

## Context for AI Assistants

### When Writing Code
- Always check if imports are available in pyproject.toml before using them
- Suggest adding missing dependencies with uv commands
- Include unit tests for new functions with appropriate Allure decorators
- Consider Windows-specific path handling using `pathlib.Path`
- Add type hints and comprehensive docstrings
- Include proper error handling and logging
- Use async/await patterns for I/O operations
- Follow SQLAlchemy 2.0 patterns for database operations
- Use dependency injection patterns with dependency-injector

### When Debugging
- Check virtual environment activation first: `where python`
- Verify Python version compatibility: `python --version`
- Look for Windows-specific issues (path separators, permissions)
- Check if all dependencies are installed: `uv pip list`
- Verify test configuration in pyproject.toml
- Check async/await patterns for correctness
- Verify database connection and migration status

### When Suggesting Dependencies
- Use `uv pip install` for adding new packages
- Distinguish between production and development dependencies
- Always suggest adding to pyproject.toml for permanent installation
- Consider version constraints for stability
- Check compatibility with existing dependencies

## Code Style and Best Practices

### General Guidelines
- Adhere to PEP 8 for Python code style
- Use type hints for improved code readability and maintainability
- Write clear and concise docstrings for modules, classes, and functions
- Prioritize modular and reusable code
- Use meaningful variable and function names
- Keep functions focused and single-purpose
- Use async/await for I/O operations
- Follow SQLAlchemy 2.0 patterns

### Import Organization
```python
# Standard library imports
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

# Third-party imports
import pandas as pd
import aiohttp
from sqlalchemy import create_engine, text
from pydantic import BaseModel

# Local imports
from dags.data_pipeline.database.upsert_operations import UpsertOperations
from dags.data_pipeline.jira.api_client import JiraApiClient
```

### Documentation Standards
```python
async def process_jira_issues(
    project_key: str,
    api_client: JiraApiClient,
    db_session: AsyncSession
) -> List[Dict[str, Any]]:
    """Process and store JIRA issues for a specific project.
    
    This function fetches issues from JIRA API, processes them,
    and stores them in the database using async operations.
    
    Args:
        project_key: The JIRA project key to fetch issues from
        api_client: Configured JIRA API client instance
        db_session: Async database session for storage operations
        
    Returns:
        List[Dict[str, Any]]: List of processed issue data
        
    Raises:
        ValueError: If project_key is invalid
        SQLAlchemyError: If database operation fails
        aiohttp.ClientError: If API request fails
        
    Example:
        >>> client = JiraApiClient("https://example.atlassian.net")
        >>> issues = await process_jira_issues("PROJ", client, session)
        >>> print(f"Processed {len(issues)} issues")
    """
    # Implementation here
    pass
```

## Common Issues and Solutions

### Virtual Environment Issues
- **Problem:** Commands fail or use wrong Python version
- **Solution:** Ensure `.venv` is activated: `.\.venv\Scripts\Activate.ps1`
- **Verification:** Use `where python` to confirm virtual environment is active

### PowerShell Execution Policy
- **Problem:** Cannot run PowerShell activation script
- **Solution:** Set execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Path Issues
- **Problem:** Cross-platform path compatibility
- **Solution:** Always use `pathlib.Path` instead of string concatenation
- **Example:** `Path("data") / "file.csv"` instead of `"data\\file.csv"`

### Dependency Issues
- **Problem:** Package not found or import errors
- **Solution:** Check installation with `uv pip list` and reinstall if needed
- **Prevention:** Always update pyproject.toml when adding dependencies

### Test Discovery Issues
- **Problem:** Pytest not finding tests
- **Solution:** Ensure test files follow naming convention (`test_*.py` or `*_test.py`)
- **Configuration:** Check `testpaths` in pyproject.toml

### Async/Await Issues
- **Problem:** Async functions not working correctly
- **Solution:** Use `pytest --asyncio-mode=auto` for async tests
- **Verification:** Ensure all async functions are properly awaited

### Database Issues
- **Problem:** Database connection or migration errors
- **Solution:** Check Alembic migrations: `alembic upgrade head`
- **Verification:** Ensure PostgreSQL is running and accessible

## IDE Configuration

### VS Code / Cursor Settings
Create `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": ".venv/Scripts/python.exe",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.testing.unittestEnabled": false,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### Recommended Extensions
- Python (Microsoft)
- Pylance (Microsoft)
- Python Test Explorer
- Allure Test Results
- SQLAlchemy (for database operations)

## Performance Considerations

### For Large Projects
- Use `pytest-xdist` for parallel test execution
- Implement proper logging levels for production
- Consider using `pytest-benchmark` for performance testing
- Use `pytest-mock` for mocking external dependencies
- Use async patterns for I/O operations

### Memory Management
- Close file handles explicitly or use context managers
- Avoid loading large datasets entirely into memory
- Use generators for processing large data sets
- Profile memory usage with `memory_profiler`
- Use async streaming for large data processing

## Security Best Practices

### Code Security
- Never commit sensitive information (API keys, passwords)
- Use environment variables for configuration
- Validate all external inputs
- Use parameterized queries for database operations
- Keep dependencies updated
- Use secure connection strings for databases

### Testing Security
- Mock external API calls in tests
- Don't use real credentials in test data
- Test error handling paths
- Validate input sanitization
- Use test databases for integration tests

## Airflow-Specific Guidelines

### DAG Structure
- Keep DAGs in the `dags/` directory
- Use descriptive DAG IDs and task names
- Implement proper error handling and retries
- Use XCom for task communication
- Follow Airflow best practices for task dependencies

### Database Operations
- Use SQLAlchemy 1.4.50+ patterns
- Implement proper connection pooling
- Use async operations where possible
- Handle database migrations with Alembic
- Implement proper error handling for database operations

### JIRA Integration
- Use async HTTP clients for API calls
- Implement proper rate limiting
- Handle API pagination correctly
- Cache responses when appropriate
- Implement proper error handling for API failures