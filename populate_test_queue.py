#!/usr/bin/env python3
"""
Utility script to populate queue_upsert_issue with test data for debugging.

This script can run the consume_* functions to generate data in queue_upsert_issue
so you can test the debug processor.
"""

import asyncio
import sys
import pandas as pd
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dags.data_pipeline.containers import ApplicationContainer
from dags.data_pipeline.models import Issue, IssueComments, IssueLinks


async def create_test_data_in_queue():
    """Create some test data directly in queue_upsert_issue for debugging."""
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=[__name__])
    
    # Get the queue
    queue_container = container.queue_container()
    queues = queue_container.queue_selector()
    queue_upsert_issue = queues["queue_upsert_issue"]
    
    logger = container.logger()
    
    logger.info("Creating test data in queue_upsert_issue...")
    
    # Test data 1: Simple Issue data
    test_issue_df = pd.DataFrame({
        'id': [12345, 12346],
        'key': ['TEST-1', 'TEST-2'],
        'summary': ['Test Issue 1', 'Test Issue 2'],
        'description': ['Test description 1', 'Test description 2'],
        'status': ['Open', 'In Progress'],
        'created': ['2024-01-01T10:00:00.000Z', '2024-01-02T10:00:00.000Z'],
        'updated': ['2024-01-01T10:00:00.000Z', '2024-01-02T10:00:00.000Z']
    })
    
    await queue_upsert_issue.put({
        "model": Issue,
        "df": test_issue_df,
        "no_update_cols": ("tscv_summary_description",),
        "on_conflict_update": True,
        "conflict_condition": ["updated"]
    })
    
    # Test data 2: Issue Comments data
    test_comment_df = pd.DataFrame({
        'id': [1001, 1002],
        'issue_id': [12345, 12346],
        'body': [{'content': 'Test comment 1'}, {'content': 'Test comment 2'}],
        'author': ['user1', 'user2'],
        'created': ['2024-01-01T11:00:00.000Z', '2024-01-02T11:00:00.000Z'],
        'updated': ['2024-01-01T11:00:00.000Z', '2024-01-02T11:00:00.000Z']
    })
    
    await queue_upsert_issue.put({
        "model": IssueComments,
        "df": test_comment_df,
        "on_conflict_update": True,
        "conflict_condition": ["updated"]
    })
    
    # Test data 3: Issue Links data
    test_links_df = pd.DataFrame({
        'id': [2001, 2002],
        'issue_id': [12345, 12346],
        'outwardIssue_id': [12346, 12345],
        'inwardIssue_id': [12345, 12346],
        'type': ['Blocks', 'Relates']
    })
    
    await queue_upsert_issue.put({
        "model": IssueLinks,
        "df": test_links_df
    })
    
    # Test data 4: Problematic data (to test error handling)
    problematic_df = pd.DataFrame({
        'id': ['not_a_number', None],  # This should cause issues
        'key': ['PROB-1', 'PROB-2'],
        'summary': [None, ''],
        'mixed_type_col': [123, 'string_value'],  # Mixed types
        'datetime_col': ['invalid_date', '2024-01-01T10:00:00.000Z']
    })
    
    await queue_upsert_issue.put({
        "model": Issue,
        "df": problematic_df,
        "no_update_cols": (),
        "on_conflict_update": True,
        "conflict_condition": ["updated"]
    })
    
    # Add termination signal
    await queue_upsert_issue.put(None)
    
    logger.info(f"Test data created. Queue size: {queue_upsert_issue.qsize()}")
    logger.info("Queue contents:")
    logger.info("- 1 Issue record (normal)")
    logger.info("- 1 IssueComments record")
    logger.info("- 1 IssueLinks record")
    logger.info("- 1 Issue record (problematic data)")
    logger.info("- 1 termination signal (None)")


async def create_empty_dataframe_test():
    """Create test data with empty DataFrames to test edge cases."""
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=[__name__])
    
    # Get the queue
    queue_container = container.queue_container()
    queues = queue_container.queue_selector()
    queue_upsert_issue = queues["queue_upsert_issue"]
    
    logger = container.logger()
    
    logger.info("Creating empty DataFrame test data...")
    
    # Empty DataFrame
    empty_df = pd.DataFrame()
    
    await queue_upsert_issue.put({
        "model": Issue,
        "df": empty_df,
        "no_update_cols": (),
        "on_conflict_update": True,
        "conflict_condition": ["updated"]
    })
    
    # DataFrame with columns but no rows
    empty_with_cols_df = pd.DataFrame(columns=['id', 'key', 'summary'])
    
    await queue_upsert_issue.put({
        "model": Issue,
        "df": empty_with_cols_df,
        "no_update_cols": (),
        "on_conflict_update": True,
        "conflict_condition": ["updated"]
    })
    
    # Add termination signal
    await queue_upsert_issue.put(None)
    
    logger.info(f"Empty DataFrame test data created. Queue size: {queue_upsert_issue.qsize()}")


async def main():
    """Main function."""
    
    print("Queue Population Utility")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
    else:
        test_type = "normal"
    
    try:
        if test_type == "empty":
            await create_empty_dataframe_test()
        elif test_type == "normal":
            await create_test_data_in_queue()
        else:
            print(f"Unknown test type: {test_type}")
            print("Available options: normal, empty")
            return
            
        print("\nTest data has been added to queue_upsert_issue.")
        print("You can now run the debug processor to test it:")
        print("  python test_queue_debug.py 1")
        
    except Exception as e:
        print(f"Error creating test data: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
