# Queue Issues Analysis and Fixes

## Issues Identified

### 1. **Insufficient None Termination Signals**
**Problem**: The `consume_issues` function was hardcoded to expect exactly 2 None signals before terminating, but the actual number of producers could vary based on `split_jql_by_count` results.

**Root Cause**: 
- `split_jql_by_count` can return 1-10 JQLs depending on data volume
- Each producer sends exactly 1 None signal when it completes
- Consumers were waiting for exactly 2 None signals regardless of producer count

**Fix Applied**:
- Modified `consume_issues` function to accept `num_producers` parameter
- Updated termination logic to wait for `num_producers` None signals instead of hardcoded 2
- Pass actual producer count from `process_jira_issues` to consumers

### 2. **Race Condition in Consumer Termination**
**Problem**: Multiple consumers (5 total) could simultaneously try to send termination signals to downstream queues, causing duplicate None signals and potential deadlocks.

**Root Cause**: All consumers were sending termination signals when they detected completion

**Fix Applied**:
- Modified termination logic so only consumer 0 sends termination signals
- Other consumers simply break from their loops without sending signals
- Prevents duplicate None signals and race conditions

### 3. **Inconsistent JQL Splitting Results**
**Problem**: `split_jql_by_count` could return empty list in error conditions, but the fallback handling was incomplete.

**Root Cause**: 
- Function returns `[], -1` on various error conditions
- Original code didn't properly handle empty JQL list case

**Fix Applied**:
- Added explicit check for empty JQL list in `process_jira_issues`
- Fallback to original JQL with proper logging
- Ensures at least 1 producer is always created

### 4. **Missing Safety Mechanism for Producer Failures**
**Problem**: If a producer failed or didn't complete properly, consumers could wait forever for None signals that never arrive.

**Root Cause**: No mechanism to detect or handle missing None signals

**Fix Applied**:
- Added safety mechanism after TaskGroup completion
- Checks queue size and adds missing None signals if needed
- Prevents consumers from hanging indefinitely

## Code Changes Made

### 1. Modified `process_jira_issues` function:
```python
# Handle empty JQL list
if not jqls:
    my_logger.warning("No JQLs returned from split_jql_by_count, using original JQL")
    jqls = [jql]
    total_records = -1

# Pass producer count to consumers
num_producers = len(jqls)
# ... pass num_producers to consume_issues calls

# Add safety mechanism
for i in range(max(0, num_producers - current_queue_size)):
    my_logger.warning(f"Adding safety None signal {i+1} to queue_issues")
    await q_container.queue_selector()["queue_issues"].put(None)
```

### 2. Modified `consume_issues` function:
```python
# Added num_producers parameter
async def consume_issues(
    # ... other parameters
    num_producers: int = 1,  # Number of producers to expect None signals from
    # ... rest of parameters
):

# Updated termination logic
if count_none >= num_producers:
    my_logger.info(f"All {num_producers} producers finished, terminating consumer {consumer_id}")
    # Only consumer 0 sends termination signals
    if consumer_id == 0:
        my_logger.info(f"Consumer {consumer_id} sending termination signals to all queues")
        await q_container.queue_selector()["queue_changelog"].put(None)
        await q_container.queue_selector()["queue_worklog"].put(None)
        await q_container.queue_selector()["queue_comment"].put(None)
        await q_container.queue_selector()["queue_issue_links"].put(None)
        await q_container.queue_selector()["queue_issue"].put(None)
    break
```

## Testing Recommendations

1. **Test with different JQL split scenarios**:
   - Small dataset (1 JQL returned)
   - Large dataset (10 JQLs returned)
   - Error conditions (empty JQL list)

2. **Test producer failure scenarios**:
   - Simulate network failures during producer execution
   - Verify safety mechanism activates correctly

3. **Monitor queue sizes**:
   - Add logging to track queue sizes throughout execution
   - Verify all queues properly terminate

4. **Load testing**:
   - Test with high-volume projects
   - Verify no deadlocks or hanging processes

## Additional Improvements Suggested

1. **Add timeout mechanism**: Consider adding timeouts to queue operations to prevent indefinite waiting

2. **Enhanced monitoring**: Add more detailed queue status monitoring and alerting

3. **Graceful error handling**: Improve error handling in producer tasks to ensure None signals are always sent

4. **Configuration**: Make the number of consumers and producers configurable based on workload

## Files Modified

- `dags/data_pipeline/utility_code.py`: Main fixes applied to queue handling logic

## Backward Compatibility

All changes are backward compatible. The `num_producers` parameter has a default value of 1, so existing code will continue to work.
