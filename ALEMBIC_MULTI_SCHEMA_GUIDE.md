# Alembic Multi-Schema Setup Guide

This guide explains how to use Alembic with your multi-schema PostgreSQL database setup.

## Overview

Your database has the following schema structure:

- **Public Schema (`public`)**: Contains tables with explicit `{'schema': 'public'}` in `__table_args__`
  - Tables: `all_boards`, `user`, `issue_fields`, `nlp_training_data`, etc.

- **Custom Schemas (`plat`, `plp`, `acq`)**: Contains tables with `{'schema': None}` or no schema specification
  - Tables: `issue`, `issue_comments`, `issue_links`, `work_log`, `changelog_json`, etc.

## Configuration

### Environment Variable

The setup uses the `ALEMBIC_SCHEMA` environment variable to control which schema to operate on:

- `ALEMBIC_SCHEMA=public`: Operates on public schema tables only
- `ALEMBIC_SCHEMA=plat`: Operates on plat schema tables only
- `ALEMBIC_SCHEMA=plp`: Operates on plp schema tables only
- `ALEMBIC_SCHEMA=acq`: Operates on acq schema tables only

### Schema Translation

The `alembic/env.py` file uses SQLAlchemy's `schema_translate_map` feature to:
1. Create schemas automatically if they don't exist
2. Translate `None` schema to the current custom schema
3. Filter tables based on the current schema context

## Usage

### Using the Management Script (Recommended)

A management script `manage_migrations.py` is provided for easier multi-schema operations:

```powershell
# Upgrade all schemas to latest
python manage_migrations.py upgrade --all

# Upgrade specific schema
python manage_migrations.py upgrade --schema public
python manage_migrations.py upgrade --schema plat

# Check status of all schemas
python manage_migrations.py status

# Create new migration for specific schema
python manage_migrations.py revision --message "Add new table" --schema public --autogenerate

# Show migration history
python manage_migrations.py history
```

### Manual Commands

You can also run Alembic commands manually with environment variables:

```powershell
# For public schema
$env:ALEMBIC_SCHEMA='public'; uv run alembic upgrade head

# For plat schema
$env:ALEMBIC_SCHEMA='plat'; uv run alembic upgrade head

# For plp schema
$env:ALEMBIC_SCHEMA='plp'; uv run alembic upgrade head

# For acq schema
$env:ALEMBIC_SCHEMA='acq'; uv run alembic upgrade head
```

## Migration Workflow

### Initial Setup (Already Done)

1. **Public Schema Migration**: Creates tables with `{'schema': 'public'}`
   - Migration: `0d6c02afdf3a_create_public_schema_tables.py`

2. **Custom Schema Migration**: Creates tables for all custom schemas
   - Migration: `68d72ca579f7_create_custom_schema_tables.py`

### Adding New Migrations

#### For Public Schema Tables

```powershell
# Generate migration for public schema
$env:ALEMBIC_SCHEMA='public'; uv run alembic revision --autogenerate -m "Add new public table"

# Apply migration
$env:ALEMBIC_SCHEMA='public'; uv run alembic upgrade head
```

#### For Custom Schema Tables

```powershell
# Generate migration for custom schemas (use any custom schema)
$env:ALEMBIC_SCHEMA='plat'; uv run alembic revision --autogenerate -m "Add new custom table"

# Apply to all custom schemas
$env:ALEMBIC_SCHEMA='plat'; uv run alembic upgrade head
$env:ALEMBIC_SCHEMA='plp'; uv run alembic upgrade head
$env:ALEMBIC_SCHEMA='acq'; uv run alembic upgrade head
```

## Model Guidelines

### For Public Schema Tables

Add `{'schema': 'public'}` to `__table_args__`:

```python
class MyPublicTable(Base):
    id = Column(Integer, primary_key=True)
    name = Column(String)
    
    __table_args__ = (
        {'schema': 'public'}
    )
```

### For Custom Schema Tables

Use `{'schema': None}` or no schema specification:

```python
class MyCustomTable(Base):
    id = Column(Integer, primary_key=True)
    name = Column(String)
    
    __table_args__ = (
        {'schema': None}  # or omit __table_args__ entirely
    )
```

## Foreign Key Considerations

### Cross-Schema References

When referencing tables in different schemas:

```python
# Custom schema table referencing public schema table
class IssueInCustomSchema(Base):
    id = Column(Integer, primary_key=True)
    assignee = Column(String, ForeignKey('public.user.accountId'))
    
    __table_args__ = ({'schema': None})

# Public schema table (no cross-schema references needed)
class UserInPublicSchema(Base):
    accountId = Column(String, primary_key=True)
    
    __table_args__ = ({'schema': 'public'})
```

### Self-References Within Schema

For self-referential tables within the same schema:

```python
class Issue(Base):
    id = Column(Integer, primary_key=True)
    parent_id = Column(Integer, ForeignKey('issue.id'))  # Same schema reference
    
    __table_args__ = ({'schema': None})
```

## Troubleshooting

### Common Issues

1. **"Target database is not up to date"**
   - Run migrations for the current schema first
   - Check migration status with `python manage_migrations.py status`

2. **Foreign Key Constraint Errors**
   - Ensure referenced tables exist in the target schema
   - Check that unique constraints exist for foreign key targets

3. **Schema Not Found**
   - Custom schemas are created automatically during migration
   - Ensure `ALEMBIC_SCHEMA` environment variable is set correctly

### Checking Migration Status

```powershell
# Check current migration status for all schemas
python manage_migrations.py status

# Check specific schema
$env:ALEMBIC_SCHEMA='public'; uv run alembic current
```

### Rolling Back Migrations

```powershell
# Rollback specific schema to previous migration
python manage_migrations.py downgrade --schema plat --target previous_revision_id
```

## Best Practices

1. **Always test migrations** on a development database first
2. **Use the management script** for consistency across schemas
3. **Keep schema-specific logic** in the `include_object` function in `env.py`
4. **Document schema changes** in migration messages
5. **Backup your database** before running migrations in production

## Files Modified/Created

- `alembic/env.py`: Multi-schema configuration
- `alembic/versions/0d6c02afdf3a_create_public_schema_tables.py`: Public schema tables
- `alembic/versions/68d72ca579f7_create_custom_schema_tables.py`: Custom schema tables
- `manage_migrations.py`: Management script for multi-schema operations
- `ALEMBIC_MULTI_SCHEMA_GUIDE.md`: This documentation
