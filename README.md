# JIRA DATA PIPELINE

> This project automates the extraction of data from a Jira Cloud instance via its REST API and stores the retrieved
> data into a PostgreSQL database for further processing and analysis. The main objective is to streamline the data
> collection process, ensuring that relevant Jira data is continuously available for business insights, reporting, or
> other downstream applications.

![Build Status](https://img.shields.io/bitbucket/pipelines/visby8em/airflow?logo=bitbucket&style=flat)
![Python Version](https://img.shields.io/badge/python-3.10%2B-blue?logo=python&style=flat)
![License](https://img.shields.io/badge/license-MIT-green)

## Environment variables
|  Environment Variable  |             Description             |
|:----------------------:|:-----------------------------------:|
| ISSUE_FIELDS_YAML_FILE | should point issue_fields.yaml file |
|      AIRFLOW_HOME      |      should contain keepass db      |

## Installation

This project uses [uv](https://github.com/astral-sh/uv) for dependency management.

### Install uv

```sh
# Install uv
pip install uv
```

### Setup development environment

```sh
# Clone the repository
git clone https://<EMAIL>/visby8em/airflow.git
cd airflow

# Create virtual environment and install dependencies
uv venv .venv
uv pip install -e .

# On Linux/macOS, install Airflow dependencies
uv pip install -e ".[linux]"

# Install development dependencies
uv pip install -e ".[dev]"
```

### Activate virtual environment

```sh
# On Windows
.venv\Scripts\activate

# On Linux/macOS
source .venv/bin/activate
```

## Usage example

The pipeline can be run either as a standalone Python application or as an Airflow DAG.

### Running as a standalone application

```sh
python -m dags.data_pipeline.utility_code
```

### Running as an Airflow DAG

```sh
# Start Airflow (Linux/macOS only)
airflow standalone
```

Then navigate to the Airflow UI at http://localhost:8080 and trigger the `jira_dag`.

_For more examples and usage, please refer to the [Wiki][wiki]._

## Development setup

```sh
# Install development dependencies
uv pip install -e ".[dev]"

# Run tests
pytest
```

## Release History

* 0.2.1
    * CHANGE: Update docs (module code remains unchanged)
* 0.2.0
    * CHANGE: Remove `setDefaultXYZ()`
    * ADD: Add `init()`
* 0.1.1
    * FIX: Crash when calling `baz()` (Thanks @GenerousContributorName!)
* 0.1.0
    * The first proper release
    * CHANGE: Rename `foo()` to `bar()`
* 0.0.1
    * Work in progress

## Meta

Your Name – [@YourTwitter](https://twitter.com/dbader_org) – <EMAIL>

Distributed under the MIT license. See ``LICENSE`` for more information.

[https://github.com/yourname/github-link](https://github.com/dbader/)

## Contributing

1. Fork it (<https://github.com/yourname/yourproject/fork>)
2. Create your feature branch (`git checkout -b feature/fooBar`)
3. Commit your changes (`git commit -am 'Add some fooBar'`)
4. Push to the branch (`git push origin feature/fooBar`)
5. Create a new Pull Request

<!-- Markdown link & img dfn's -->
[wiki]: https://github.com/yourname/yourproject/wiki
