import csv

# Given dictionary
column_rename_map = {
    'aggregateprogress.percent': 'aggregateprogress_percent',
    'aggregateprogress.progress': 'aggregateprogress_progress',
    'aggregateprogress.total': 'aggregateprogress_total',
    'progress.percent': 'progress_percent',
    'progress.progress': 'progress_progress',
    'progress.total': 'progress_total',
    'assignee.accountId': 'assignee',
    'reporter.accountId': "reporter",
    "customfield_10006.value": "change_risk",
    'customfield_10049.value': "severity",
    'customfield_10015': 'startdate',
    'customfield_10019': 'Rank',
    "customfield_10056.value": "category_type",
    "customfield_10071.value": "initiated_by",
    "customfield_10078.value": 'approvalstatus',
    'customfield_10146.value': 'reqfinalized',
    "customfield_10179.value": "qc_check",
    'customfield_10059': 'testcaseno',
    'customfield_10060': 'testcasesuite',
    'customfield_10061': 'teststepno',
    'customfield_10062': 'scenariono',
    'customfield_10067': 'ClientJira',
    'customfield_10120': 'totaleffort',
    'customfield_10121': 'totaldeveffort',
    'customfield_10122': 'baeffort',
    'customfield_10123': 'adeffort',
    'customfield_10124': 'rdeffort',
    'customfield_10125': 'qaeffort',
    'customfield_10126': 'contingency',
    "customfield_10147": "reopen_count",
    "customfield_10256": "initiative_detail",
    'issuetype.name': 'issuetype',
    'priority.name': 'priority',
    'resolution.name': "resolution",
    'status.name': "status",
    'issuetype.subtask': 'isSubTask',
    'status.statusCategory.name': 'statusCategory',
    'parent.id': 'parent_id',
    'parent.key': 'parent_key',
    "customfield_10199": "cvss_score",
    'customfield_10024': 'storypoints',
    'customfield_10020': 'sprint',
    'customfield_10092.value': 'urgency'
}

# Prepare data for CSV
csv_data = []
prefix_map = {}

# Build the required columns for CSV
for key, value in column_rename_map.items():
    prefix = key.split('.')[0]
    if prefix not in prefix_map:
        prefix_map[prefix] = {'rename_from': [], 'rename_to': []}

    prefix_map[prefix]['rename_from'].append(key)
    prefix_map[prefix]['rename_to'].append(value)

# Create rows based on the dictionary
for prefix, items in prefix_map.items():
    rename_from = ', '.join(items['rename_from']) if len(items['rename_from']) > 1 else ''
    rename_to = ', '.join(items['rename_to']) if len(items['rename_to']) > 1 else items['rename_to'][0]
    csv_data.append([prefix, rename_from, rename_to])

# Define file path
file_path = 'column_rename_map.csv'

# Write to CSV file
with open(file_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Prefix', 'Rename From', 'Rename To'])
    writer.writerows(csv_data)

print(f"CSV file created at {file_path}")
