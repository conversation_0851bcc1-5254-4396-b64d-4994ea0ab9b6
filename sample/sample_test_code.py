import asyncio
import ast
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
from rich.table import Table
from rich.live import Live


async def process_task():
    console = Console(log_path=False, log_time=True, log_time_format="%Y%m%d %H:%M:%S.%f")

    project_list = ["acq"]
    tasks = ["create_db_extension"]
    task_child = [f"create_schema_tables_ddl('{project}')" for project in project_list]
    tasks.extend(task_child)
    tasks.extend(["get_fields", "get_jira_users", "add_to_db_all_jira_boards"])

    task_child = []
    for project in project_list:
        task_child.append(f"process_jira_issues('{project}', 'project', True)")
        task_child.append(f"process_jira_versions('{project}')")
        task_child.append(f"upsert_issue_classification('{project}', batch_size=50_000)")
        task_child.append(f"del_deleted_worklog('{project}')")
        task_child.append(f"get_sprint_details('{project}')")
    tasks.extend(task_child)
    tasks.append("create_refresh_mv")

    # Create overall progress
    overall_progress = Progress()
    overall_task = overall_progress.add_task("Overall Task Progress", total=len(tasks))

    # Create job-specific progress
    job_progress = Progress(
        "{task.description}",
        SpinnerColumn(),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
    )

    # Add tasks to the job progress
    job_tasks = [job_progress.add_task(f"[cyan]{task}", total=100) for task in tasks]

    # Create a table for displaying the panels
    progress_table = Table.grid()
    progress_table.add_row(
        Panel.fit(
            overall_progress, title="Overall Progress", border_style="green", padding=(2, 2)
        ),
        Panel.fit(job_progress, title="[b]Task Progress", border_style="red", padding=(1, 2)),
    )

    with Live(progress_table, console=console, refresh_per_second=10) as live:
        for i, task in enumerate(tasks):
            task_status = f"Executing {task}..."
            status_panel = Panel(task_status, title="Task Status", border_style="yellow")

            # Process each task sequentially
            try:
                if task == "process_jira_issues('acq', 'project', True)":
                    # Display a separate screen for queue stats and stats while this task runs
                    await run_jira_issues_with_live_display()
                    task_status = f"Finished {task}"
                else:
                    # Other task processing logic
                    task_status = f"Executing {task}..."
                    # Here you would process other tasks normally

                # Update the progress and the status panel
                job_progress.update(job_tasks[i], advance=100)
                progress_table.add_row(status_panel)

                # Update the overall progress
                completed = i + 1
                overall_progress.update(overall_task, completed=completed)

            except Exception as e:
                console.print(f"[bold red]Error executing {task}:[/bold red] {e}")
                task_status = f"Error executing {task}: {e}"

            # Update the task status panel with the current task status
            status_panel = Panel(task_status, title="Task Status", border_style="yellow")
            progress_table.add_row(status_panel)

            # Refresh the display after each task
            live.update(progress_table)
            await asyncio.sleep(0.1)  # Brief delay to simulate task processing


async def run_jira_issues_with_live_display():
    # Create queues for monitoring the jira issue processing
    queue_issues = asyncio.Queue(1000)
    queue_stats = asyncio.Queue(1000)

    # Start background tasks for stats and queue status
    stats_task = asyncio.create_task(produce_stats(queue_stats))
    queue_status_task = asyncio.create_task(
        check_queue_status([(queue_issues, "queue_issues"), (queue_stats, "queue_stats")]))

    # Simulate running process_jira_issues (your long-running process)
    await process_jira_issues("acq", "project", True)

    # Once the process finishes, cancel the background tasks
    stats_task.cancel()
    queue_status_task.cancel()


async def process_jira_issues(project_key: str, scope: str, initial_load: bool):
    # Simulate issue processing and updating the queues
    queue_issues: asyncio.Queue = asyncio.Queue(1000)
    queue_stats: asyncio.Queue = asyncio.Queue(1000)

    for i in range(100):
        await queue_issues.put(f"issue_{i}")  # Add dummy issues to the queue
        await queue_stats.put({"total": 100, "processed": i + 1})  # Simulating stats
        await asyncio.sleep(0.5)  # Simulating some delay in processing


async def produce_stats(stats_queue: asyncio.Queue):
    from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn

    stats_console = Console(log_path=False, log_time=True, log_time_format="%Y%m%d %H:%M:%S.%f")
    progress = Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        "[progress.percentage]{task.percentage:>3.0f}%",
        "•",
        TextColumn("Processed: {task.completed} / {task.total}"),
        TimeElapsedColumn(),
        refresh_per_second=4,
        transient=True,
        console=stats_console
    )

    with progress:
        task = None
        try:
            while True:
                result = await stats_queue.get()

                if task is None:
                    # Create a progress task the first time we have data
                    task = progress.add_task(
                        "Processing Records",
                        total=result['total']
                    )
                progress.update(task, completed=result['processed'])

        except asyncio.CancelledError:
            pass


async def check_queue_status(queues: list):
    from rich.table import Table
    from rich.live import Live
    from rich.align import Align

    table = Table(title="Queue Status", show_footer=False, caption=" " * 10)
    table_centered = Align.center(table)
    table.pad_edge = False

    def initialize_table():
        for queue_name, name in queues:
            table.add_row(name, str(queue_name.qsize()))

    def update_table():
        for index, (queue_name, name) in enumerate(queues):
            table.columns[1]._cells[index] = str(queue_name.qsize())

    initialize_table()

    with Live(table_centered, refresh_per_second=4) as live:
        while True:
            update_table()
            live.update(table_centered)
            await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(process_task())