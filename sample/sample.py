"""

Demonstrates the use of multiple Progress instances in a single Live display.

"""
import ast
import asyncio
from time import sleep

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn
from rich.table import Table


async def process_task():

    project_list = ["acq", "plat"]
    tasks = ["create_db_extension", ]
    task_child = [f"create_schema_tables_ddl('{project}')" for project in project_list]
    tasks.extend(task_child)
    tasks.extend(["get_fields", "get_jira_users", "add_to_db_all_jira_boards"])
    task_child = []
    for project in project_list:
        task_child.append(f"process_jira_issues('{project}', 'project', True)")
        task_child.append(f"process_jira_versions('{project}')")
        task_child.append(f"upsert_issue_classification('{project}', batch_size=50_000)")
        task_child.append(f"del_deleted_worklog('{project}')")
        task_child.append(f"get_sprint_details('{project}')")
    tasks.extend(task_child)
    tasks.append("create_refresh_mv")

    # Create overall progress
    overall_progress = Progress()
    overall_task = overall_progress.add_task("Overall Task Progress", total=len(tasks))

    # Create job-specific progress
    job_progress = Progress(
        "{task.description}",
        SpinnerColumn(),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        " ",
        # TextColumn("Processed: {task.completed} / {task.total}"),
        TimeElapsedColumn(),
    )

    # Add tasks to the job progress
    job_tasks = [job_progress.add_task(f"[cyan]{task}", total=100) for task in tasks]

    # Create a table for displaying the panels
    progress_table = Table.grid()
    progress_table.add_row(
        Panel.fit(
            overall_progress, title="Overall Progress", border_style="green", padding=(2, 2)
        ),
        Panel.fit(job_progress, title="[b]Task Progress", border_style="red", padding=(1, 2)),
    )

    with Live(progress_table, refresh_per_second=4, screen=True, console=console) as live:
        for i, task in enumerate(tasks):
            task_status = f"Executing {task}..."
            # status_panel = Panel(task_status, title="Task Status", border_style="yellow")
            try:
                if "(" in task and ")" in task:
                    # If it's a function with arguments, parse it manually
                    function_name, args_str = task.split(sep="(", maxsplit=1)
                    args_str = args_str.rstrip(")")

                    # Split arguments into positional and keyword arguments
                    args = []
                    kwargs = {}
                    for arg in args_str.split(","):
                        arg = arg.strip()
                        if "=" in arg:
                            # Handle keyword arguments
                            key, value = arg.split("=", maxsplit=1)
                            kwargs[key.strip()] = ast.literal_eval(value.strip())
                        else:
                            # Handle positional arguments
                            args.append(ast.literal_eval(arg))

                    # Split arguments safely and evaluate them using ast.literal_eval
                    # args = [ast.literal_eval(arg.strip()) for arg in args_str.split(",") if arg.strip()]
                    function = globals().get(function_name)
                else:
                    # If it's a simple function call without arguments
                    function = globals().get(task)
                    args = []
                    kwargs = {}

                if function:
                    if asyncio.iscoroutinefunction(function):
                        # If it's an async function, await it
                        result = await function(*args, **kwargs)
                    else:
                        # Otherwise, call it normally
                        result = function(*args, **kwargs)
                    task_status = f"Executing {task}... {result}"
                else:
                    raise ValueError(f"Function {task} not found.")

                # Update the progress and the status panel
                job_progress.update(job_tasks[i], advance=100)
                # progress_table.add_row(status_panel)

                # Update the overall progress
                completed = i + 1
                overall_progress.update(overall_task, completed=completed)

            except Exception as e:
                task_status = f"Error executing {task}: {e}"
                console.print(f"[bold red]Error executing {task}:[/bold red] {task_status}")
                handle_exception(e)


            # # Update the task status panel with the current task status
            # status_panel = Panel(task_status, title="Task Status", border_style="yellow")
            # progress_table.add_row(status_panel)
            #
            # # Refresh the display after each task
            # live.update(progress_table)
            # await asyncio.sleep(0.1)  # Brief delay to simulate task processing



async def process_task_old():
    console = Console(log_path=False, log_time=True, log_time_format="%Y%m%d %H:%M:%S.%f")

    project_list = ["acq"]
    tasks = ["create_db_extension", ]
    task_child = [f"create_schema_tables_ddl('{project}')" for project in project_list]
    tasks.extend(task_child)
    tasks.extend(["get_fields", "get_jira_users", "add_to_db_all_jira_boards"])
    task_child = []
    for project in project_list:
        task_child.append(f"process_jira_issues('{project}', 'project', True)")
        task_child.append(f"process_jira_versions('{project}')")
        task_child.append(f"upsert_issue_classification('{project}', batch_size=50_000)")
        task_child.append(f"del_deleted_worklog('{project}')")
        task_child.append(f"get_sprint_details('{project}')")
    tasks.extend(task_child)
    tasks.append("create_refresh_mv")

    # Create a table for task results
    task_table = Table(title="Task Results")
    task_table.add_column("Task", style="cyan")
    task_table.add_column("Status", style="green")

    # with console.status("[bold green]Working on tasks..."):
    with Live(console=console, auto_refresh=True) as live:
        with Progress(transient=True) as progress:
            task_name = progress.add_task("[cyan]Processing...", total=len(tasks))
            live.update(task_table)
            while tasks:
                task = tasks.pop(0)
                try:
                    if "(" in task and ")" in task:
                        # If it's a function with arguments, parse it manually
                        function_name, args_str = task.split(sep="(", maxsplit=1)
                        args_str = args_str.rstrip(")")

                        # Split arguments into positional and keyword arguments
                        args = []
                        kwargs = {}
                        for arg in args_str.split(","):
                            arg = arg.strip()
                            if "=" in arg:
                                # Handle keyword arguments
                                key, value = arg.split("=", maxsplit=1)
                                kwargs[key.strip()] = ast.literal_eval(value.strip())
                            else:
                                # Handle positional arguments
                                args.append(ast.literal_eval(arg))

                        # Split arguments safely and evaluate them using ast.literal_eval
                        # args = [ast.literal_eval(arg.strip()) for arg in args_str.split(",") if arg.strip()]
                        function = globals().get(function_name)
                    else:
                        # If it's a simple function call without arguments
                        function = globals().get(task)
                        args = []
                        kwargs = {}

                    if function:
                        if asyncio.iscoroutinefunction(function):
                            # If it's an async function, await it
                            result = await function(*args, **kwargs)
                        else:
                            # Otherwise, call it normally
                            result = function(*args, **kwargs)
                        # console.log(f"{task} Status: {result}")
                        task_table.add_row(task, str(result))
                    else:
                        raise ValueError(f"Function {task} not found.")
                    progress.update(task_name, advance=1)
                    live.update(task_table)
                except Exception as e:
                    console.print(f"[bold red]Error executing {task}:[/bold red] {e}")


job_progress = Progress(
    "{task.description}",
    SpinnerColumn(),
    BarColumn(),
    TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
)
job1 = job_progress.add_task("[green]Cooking")
job2 = job_progress.add_task("[magenta]Baking", total=200)
job3 = job_progress.add_task("[cyan]Mixing", total=400)

total = sum(task.total for task in job_progress.tasks)
overall_progress = Progress()
overall_task = overall_progress.add_task("All Jobs", total=int(total))

progress_table = Table.grid()
progress_table.add_row(
    Panel.fit(
        overall_progress, title="Overall Progress", border_style="green", padding=(2, 2)
    ),
    Panel.fit(job_progress, title="[b]Jobs", border_style="red", padding=(1, 2)),
)

with Live(progress_table, refresh_per_second=10):
    while not overall_progress.finished:
        sleep(0.1)
        for job in job_progress.tasks:
            if not job.finished:
                job_progress.advance(job.id)

        completed = sum(task.completed for task in job_progress.tasks)
        overall_progress.update(overall_task, completed=completed)